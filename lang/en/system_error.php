<?php

return [
    // generic errors
    '403' => 'Forbidden',
    '404' => 'Not Found',

    // Master Data Errors
    '10001' => 'This data cannot be deleted because it is being used.',

    // Validation Errors
    '11001' => 'The phone number field must be a valid phone number.',
    '11002' => 'The email has already been taken.',
    '11003' => 'The phone has already been taken.',

    //Maintenance Error
    '12001' => 'Site is currently under maintenance.',
    '12002' => 'New app version available. Kindly update to the latest version.',

    //Report Error
    '13001' => 'Please define a report adapter first.',

    //User Error
    '14001' => 'Email and password are required.',
    '14002' => 'Invalid credentials.',
    '14003' => 'User does not have required roles: :roles',
    '14004' => 'User must be validated first.',

    //Instructor Error
    '15001' => 'User is linked with another stylist before.',
    '15002' => 'Only user with Instructor role allowed to link.',

    //Notification Error
    '16001' => 'SendPulse API credentials are missing.',

    //Order Error
    '17001' => 'Order product does not match.',
    '17002' => 'Only processing order are enable to link products.',
];
