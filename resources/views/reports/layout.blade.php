<!DOCTYPE html>

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        @font-face {
            font-family: 'Noto Serif SC';
            font-style: normal;
            font-weight: 200 900;
            font-display: swap;
            src: url({{ asset('fonts/truetype/NotoSerifSC.ttf') }});
        }

        html, body {
            font-family: 'Noto Serif SC', sans-serif;
        }

        table {
            font-size: 12px;
            width: 100%;
            border-collapse: collapse;
        }

        table.small-font {
            font-size: 10px;
            width: 100%;
            border-collapse: collapse;
        }

        table thead {
            display: table-header-group;
        <!-- or table-row-group ? -->
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }

        tfoot {
            display: table-row-group;
        }

        th {
            background-color: #f2f2f2;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: avoid;
            page-break-before: avoid;
        }

        table.narrow-rows td {
            padding: 5px 8px;
        }

        tr.narrow-rows td {
            padding: 5px 8px;
        }

        tr.bold td {
            font-weight: bold;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .bg-white {
            background-color: #fff !important;
        }
    </style>

    @stack('styles')
</head>

<body>
@isset($title)
    <div style="text-align: center; margin-bottom: 10px;">
        @isset($title)
            <h2>{{$title}}</h2>
        @endisset
        @isset($subtitle)
            <h4>{{$subtitle}}</h4>
        @endisset

        <h4>{{__('general.print_date')}}
            : {{now()->setTimezone(config('school.timezone'))->format('d M Y, h:i A')}}</h4>
    </div>
@endisset

@yield('content')
</body>

</html>
