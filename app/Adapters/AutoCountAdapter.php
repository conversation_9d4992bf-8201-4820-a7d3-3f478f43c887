<?php

namespace App\Adapters;

use App\Enums\AutoCountProductStatus;
use App\Enums\SettingType;
use App\Models\Order;
use App\Services\SettingService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AutoCountAdapter extends AbstractAccountingSoftwareAdapter
{
    protected string $baseUrl;
    protected string $apiKey;
    protected $accounts;
    protected $config;

    public function __construct()
    {
        $setting_service = app(SettingService::class);
        $this->accounts = $setting_service->findVia(SettingType::AUTOCOUNT, 'type');

        if (!$this->accounts?->options) {
            $this->handleError([
                'message' => 'Missing configuration key',
                'config' => $this->accounts,
                'required_key' => [
                    'book_id',
                    'api_key',
                    'key_id',
                ],
            ]);
        }

        $this->config = Arr::first($this->accounts->options['accounts']);

        $this->checkConfig($this->config, [
            'book_id',
            'api_key',
            'key_id',
            'debtor_code',
            'debtor_name',
            'credit_term',
            'sales_location',
            'purchase_account_number',
        ]);

        $this->baseUrl = config('services.autocount.base_url');
    }

    public function setConfig(array $config): static
    {
        $this->config = $config;

        return $this;
    }

    public function getProducts(): array
    {
        $data = [
            'page' => $this->page,
            'filter' => [],
        ];

        foreach (AutoCountProductStatus::getValues() as $status) {
            $data['filter']['statuses'][$status] = true;
        }

        if ($this->productStatuses) {
            foreach (AutoCountProductStatus::getValues() as $status) {
                $data['filter']['statuses'][$status] = false;
            }

            foreach ($this->productStatuses as $status) {
                $data['filter']['statuses'][$status] = true;
            }
        }

        return $this->request('product/listing', $data, 'POST');
    }

    public function getProduct(string $product_id): array
    {
        return $this->request("product/{$product_id}");
    }

    public function createInvoice(Order $order): void
    {
        $data = [
            'master' => [
                'docDate' => $order->created_at->format('Y-m-d'),
                'debtorCode' => $this->config['debtor_code'],
                'debtorName' => $this->config['debtor_name'],
                'creditTerm' => $this->config['credit_term'],
                'salesLocation' => $this->config['sales_location'],
            ],
            'details' => [],
        ];

        foreach ($order->products as $order_product) {
            if ($order_product->product->is_bundle) {
                foreach ($order_product->product->bundleItems as $bundle_item) {
                    $data['details'][] = [
                        'accNo' => $this->config['purchase_account_number'],
                        'productCode' => $bundle_item->item->sku,
                        'description' => $bundle_item->item->name,
                        'unitPrice' => $bundle_item->item->price * 100,
                        'qty' => $bundle_item->quantity * $order_product->invoiced_quantity,
                    ];
                }
            }
            else {
                $data['details'][] = [
                    'accNo' => $this->config['purchase_account_number'],
                    'productCode' => $order_product->sku,
                    'description' => $order_product->name,
                    'unitPrice' => $order_product->unit_price,
                    'qty' => $order_product->invoiced_quantity,
                ];
            }
        }

        $this->request("invoice", $data, 'POST');
    }

    public function request(string $endpoint, array $data = [], string $method = 'GET')
    {
        $url = $this->baseUrl . '/' . $this->config['book_id'] . '/' . $endpoint;

        Log::info('AutoCount API Request', [
            'url' => $url,
            'method' => $method,
            'data' => $data,
        ]);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'API-Key' => $this->config['api_key'],
            'Key-ID' => $this->config['key_id'],
        ])->timeout(30)
            ->$method($url, $data);

        $response->onError(function ($response) use ($url) {
            $this->handleError([
                'status' => $response->status(),
                'response' => $response->body(),
                'url' => $url,
            ]);
        });

        $data = $response->json();

        Log::info('AutoCount API Response', [
            'url' => $url,
            'status' => $response->status(),
            'data' => $data,
        ]);

        return $data;
    }

    public function handleError(array $response): void
    {
        Log::error('AutoCount API Error', $response);

        abort(500, json_encode(['title' => 'AutoCount API Error', 'data' => $response]));
    }

    public function checkConfig(array $config, array $key): void
    {
        if (!Arr::has($config, $key)) {
            $this->handleError([
                'message' => 'Missing configuration key',
                'config' => array_keys($config),
                'required_key' => $key,
            ]);
        }

        foreach ($config as $v) {
            if (!$v) {
                $this->handleError([
                    'message' => 'Missing configuration value',
                    'config' => array_keys($config),
                    'required_key' => $key,
                ]);
            }
        }
    }
}
