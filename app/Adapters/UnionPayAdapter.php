<?php

namespace App\Adapters;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class UnionPayAdapter extends AbstractPaymentGatewayAdapter
{
    protected string $sendPartyIIN;
    protected string $forwardingIIN;
    protected string $appId;
    protected string $signCertSerial;
    protected string $encCertSerial;
    protected string $institutionPrivateKey;
    protected string $upiPublicKey;
    protected string $upiEncryptionKey;
    public string $upiSigningCertData;
    public string $upiEncryptionCertData;

    public function __construct()
    {
        parent::__construct();

        $this->checkConfig(config('services.unionpay'), [
            'base_url',
            'app_id',
            'send_party_iin',
            'forwarding_iin',
            'sign_cert_serial',
            'enc_cert_serial',
        ]);

        $this->baseUrl = config('services.unionpay.base_url');
        $this->appId = config('services.unionpay.app_id');
        $this->sendPartyIIN = config('services.unionpay.send_party_iin');
        $this->forwardingIIN = config('services.unionpay.forwarding_iin');
        $this->signCertSerial = config('services.unionpay.sign_cert_serial');
        $this->encCertSerial = config('services.unionpay.enc_cert_serial');

        // Load certificate data from config
        $this->upiSigningCertData = config('services.unionpay.upi_signing_cert_data');
        $this->upiEncryptionCertData = config('services.unionpay.upi_encryption_cert_data');

        // Load keys
        $this->upiPublicKey = $this->loadUpiPublicKey();
        $this->upiEncryptionKey = $this->loadUpiEncryptionKey();
        $this->institutionPrivateKey = $this->loadInstitutionPrivateKey();
    }

    public function processPayment(array $paymentData): array
    {
        // For now, use fund inquiry as a basic implementation
        return $this->fundInquiry($paymentData['currency'] ?? '156');
    }

    public function verifyPayment(string $transactionId): array
    {
        return $this->formatErrorResponse('Not implemented yet');
    }

    public function refundPayment(string $transactionId, float $amount): array
    {
        return $this->formatErrorResponse('Not implemented yet');
    }

    public function getTransactionStatus(string $transactionId): array
    {
        return $this->formatErrorResponse('Not implemented yet');
    }

    public function getSupportedPaymentMethods(): array
    {
        return [
            'credit_card',
            'debit_card',
            'bank_transfer',
            'digital_wallet',
        ];
    }

    /**
     * Fund inquiry for credential validation
     */
    public function fundInquiry(string $currencyCode): array
    {
        $msgId = $this->generateMessageId();

        $requestBody = [
            'msgID' => $msgId,
            'sendPartyIIN' => $this->sendPartyIIN,
            'forwardingIIN' => $this->forwardingIIN,
            'localTxnDateTime' => date('mdHis'),
            'trxCurrency' => $currencyCode
        ];

        return $this->makeApiRequest('/umts/iswitch/v1/fundinquiry', $requestBody);
    }

    /**
     * Key exchange for certificate management
     */
    public function keyExchange(array $keyData): array
    {
        $msgId = $this->generateKeyExchangeMessageId();

        $requestBody = [
            'msgID' => $msgId,
            // 'sendPartyIIN' => $this->sendPartyIIN,
            // 'forwardingIIN' => $this->forwardingIIN,
            // 'localTxnDateTime' => date('mdHis')
        ];

        if (isset($keyData['signPublicKey'])) {
            $requestBody['signPublicKey'] = $keyData['signPublicKey'];
        }

        if (isset($keyData['encPublicKey'])) {
            $requestBody['encPublicKey'] = $keyData['encPublicKey'];
        }

        return $this->makeApiRequest('/umts/iswitch/v1/keyexchange', $requestBody);
    }

    /**
     * Create JWE encrypted payload
     */
    private function createJWE(array $sensitiveData): string
    {
        $header = [
            'alg' => 'RSA1_5',
            'enc' => 'A128CBC-HS256',
            'kid' => $this->encCertSerial
        ];

        $encodedHeader = $this->base64UrlEncode(json_encode($header, JSON_UNESCAPED_SLASHES));

        // Generate CEK (Content Encryption Key)
        $cek = random_bytes(32); // 256 bits for A128CBC-HS256

        // Encrypt CEK with UPI's public key
        $encryptedKey = '';
        if (!openssl_public_encrypt($cek, $encryptedKey, $this->upiEncryptionKey, OPENSSL_PKCS1_PADDING)) {
            throw new Exception('Failed to encrypt CEK: ' . openssl_error_string());
        }
        $encodedEncryptedKey = $this->base64UrlEncode($encryptedKey);

        // Generate IV
        $iv = random_bytes(16); // 128 bits for AES-CBC
        $encodedIV = $this->base64UrlEncode($iv);

        // Prepare payload
        $payload = json_encode($sensitiveData);

        // Encrypt payload with AES-CBC
        $ciphertext = openssl_encrypt($payload, 'AES-256-CBC', $cek, OPENSSL_RAW_DATA, $iv);
        if ($ciphertext === false) {
            throw new Exception('Failed to encrypt payload: ' . openssl_error_string());
        }
        $encodedCiphertext = $this->base64UrlEncode($ciphertext);

        // Generate authentication tag (HMAC)
        $authKey = substr($cek, 0, 16); // First 128 bits for HMAC
        $authData = $encodedHeader . '.' . $encodedEncryptedKey . '.' . $encodedIV . '.' . $encodedCiphertext;
        $authTag = hash_hmac('sha256', $authData, $authKey, true);
        $authTag = substr($authTag, 0, 16); // Truncate to 128 bits
        $encodedAuthTag = $this->base64UrlEncode($authTag);

        return $encodedHeader . '.' . $encodedEncryptedKey . '.' . $encodedIV . '.' . $encodedCiphertext . '.' . $encodedAuthTag;
    }

    /**
     * Create JWS signature with all UPI headers in protected header only
     */
    private function createJWS(string $payload, string $requestPath, string $uuid, string $timestamp): string
    {
        $header = [
            'alg' => 'RS256',
            'kid' => $this->signCertSerial,
            'crit' => ['UPI-UUID', 'UPI-TIMESTAMP', 'UPI-APPID', 'UPI-REQPATH'],
            'UPI-UUID' => $uuid,
            'UPI-TIMESTAMP' => $timestamp,
            'UPI-APPID' => $this->appId,
            'UPI-REQPATH' => $requestPath
        ];
        $encodedHeader = $this->base64UrlEncode(json_encode($header, JSON_UNESCAPED_SLASHES));
        $encodedPayload = $this->base64UrlEncode($payload);

        $signingInput = $encodedHeader . '.' . $encodedPayload;

        $signature = '';
        if (!openssl_sign($signingInput, $signature, $this->institutionPrivateKey, OPENSSL_ALGO_SHA256)) {
            throw new Exception('Failed to create JWS signature: ' . openssl_error_string());
        }

        $encodedSignature = $this->base64UrlEncode($signature);

        return $encodedHeader . '..' . $encodedSignature;
    }

    /**
     * Make API request with proper headers and signature
     */
    public function request(string $endpoint, array $data = [], string $method = 'POST'): array
    {
        return $this->makeApiRequest($endpoint, $data, $method);
    }

    private function makeApiRequest(string $endpoint, array $data, string $method = 'POST'): array
    {
        $url = $this->baseUrl . $endpoint;
        $payload = json_encode($data);
        $requestPath = parse_url($url, PHP_URL_PATH);

        $uuid = $this->generateUUID();
        $timestamp = $this->generateUnixTimestamp();

        $jwsSignature = $this->createJWS($payload, $requestPath, $uuid, $timestamp);

        $headers = [
            'Content-Type: application/json',
            'UPI-JWS: ' . $jwsSignature
        ];

        $response = Http::withHeaders(array_reduce($headers, function ($carry, $header) {
            [$key, $value] = explode(': ', $header, 2);
            $carry[$key] = $value;
            return $carry;
        }, []))->timeout(30)->$method($url, $data);

        $response->onError(function ($response) use ($url) {
            $this->handleError([
                'status' => $response->status(),
                'response' => $response->body(),
                'url' => $url,
            ]);
        });

        $responseData = $response->json();

        return $responseData;
    }

    public function handleError(array $response): void
    {
        Log::error('UnionPay API Error', $response);
        throw new Exception('UnionPay API Error: ' . json_encode($response));
    }

    public function checkConfig(array $config, array $keys): void
    {
        foreach ($keys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                $this->handleError([
                    'message' => 'Missing configuration key: ' . $key,
                    'required_keys' => $keys,
                ]);
            }
        }
    }

    // Utility methods
    private function generateMessageId(): string
    {
        return $this->sendPartyIIN . $this->forwardingIIN . date('mdHis') . sprintf('%06d', mt_rand(0, 999999));
    }

    private function generateKeyExchangeMessageId(): string
    {
        $insId = $this->sendPartyIIN;

        $transactionTime = date('YmdHis'); // 14-digit: YYYYMMDDhhmmss
        $serialNumber = sprintf('%06d', mt_rand(0, 999999)); // 6-digit serial

        return "S" . $insId . $transactionTime . $serialNumber;
    }

    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    private function generateUnixTimestamp(): string
    {
        return (string) time();
    }

    private function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    private function base64UrlDecode(string $data): string
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    private function generateDummyPrivateKey(): string
    {
        $config = [
            "digest_alg" => "sha256",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];
        $res = openssl_pkey_new($config);
        openssl_pkey_export($res, $privateKey);
        return '';
    }

    private function loadUpiPublicKey(): string
    {
        return $this->extractPublicKeyFromCertData($this->upiSigningCertData);
    }

    private function loadUpiEncryptionKey(): string
    {
        return $this->extractPublicKeyFromCertData($this->upiEncryptionCertData);
    }

    private function loadInstitutionPrivateKey(): string
    {
        $keyPath = config('services.unionpay.institution_private_key');

        if (empty($keyPath)) {
            throw new Exception('Institution private key path not configured');
        }

        // Handle relative paths from project root
        if (!str_starts_with($keyPath, '/')) {
            $keyPath = base_path($keyPath);
        }

        if (!file_exists($keyPath)) {
            throw new Exception("Institution private key file not found: {$keyPath}. Please ensure the file exists or update UNIONPAY_INSTITUTION_PRIVATE_KEY environment variable.");
        }

        $privateKey = file_get_contents($keyPath);

        if ($privateKey === false) {
            throw new Exception("Failed to read institution private key file: {$keyPath}");
        }

        // Validate the private key
        $keyResource = openssl_pkey_get_private($privateKey);
        if ($keyResource === false) {
            throw new Exception('Invalid institution private key format: ' . openssl_error_string());
        }

        return $privateKey;
    }



    private function extractPublicKeyFromCertData(string $certData): string
    {
        $certPem = "-----BEGIN CERTIFICATE-----\n" . chunk_split($certData, 64, "\n") . "-----END CERTIFICATE-----\n";

        $cert = openssl_x509_read($certPem);
        if ($cert === false) {
            throw new Exception('Failed to read certificate: ' . openssl_error_string());
        }

        $publicKey = openssl_pkey_get_public($cert);
        if ($publicKey === false) {
            throw new Exception('Failed to extract public key from certificate: ' . openssl_error_string());
        }

        $keyDetails = openssl_pkey_get_details($publicKey);
        if ($keyDetails === false) {
            throw new Exception('Failed to get public key details: ' . openssl_error_string());
        }

        return $keyDetails['key'];
    }
}
