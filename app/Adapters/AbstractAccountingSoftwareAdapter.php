<?php

namespace App\Adapters;

use App\Interfaces\AccountingSoftwareInterface;

abstract class AbstractAccountingSoftwareAdapter implements AccountingSoftwareInterface
{
    protected string $dateFrom;
    protected string $dateTo;
    protected int $page = 1;
    protected int $limit = 100;
    protected array $productStatuses;

    public function setDateFrom(string $date_from): AccountingSoftwareInterface
    {
        $this->dateFrom = $date_from;
        return $this;
    }

    public function setDateTo(string $date_to): AccountingSoftwareInterface
    {
        $this->dateTo = $date_to;
        return $this;
    }

    public function setPage(int $page): AccountingSoftwareInterface
    {
        $this->page = $page;
        return $this;
    }

    public function setPageSize(int $size): AccountingSoftwareInterface
    {
        $this->limit = $size;
        return $this;
    }

    public function setProductStatuses(array $statuses): AccountingSoftwareInterface
    {
        $this->productStatuses = $statuses;
        return $this;
    }
}
