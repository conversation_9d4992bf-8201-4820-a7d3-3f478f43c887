<?php

namespace App\Adapters;

use App\Interfaces\PaymentGatewayInterface;

abstract class AbstractPaymentGatewayAdapter implements PaymentGatewayInterface
{
    protected array $credentials;
    protected string $baseUrl;
    protected bool $isTestMode;

    public function __construct()
    {
        $this->isTestMode = config('app.env') !== 'production';
    }

    public function setCredentials(array $credentials): PaymentGatewayInterface
    {
        $this->credentials = $credentials;
        return $this;
    }

    public function getConfiguration(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'is_test_mode' => $this->isTestMode,

        ];
    }



    /**
     * Format error response consistently across all adapters
     */
    protected function formatErrorResponse(string $message, array $details = []): array
    {
        return [
            'success' => false,
            'error' => $message,
            'details' => $details,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Format success response consistently across all adapters
     */
    protected function formatSuccessResponse(array $data): array
    {
        return [
            'success' => true,
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ];
    }
}
