<?php

namespace App\Adapters;

use Barryvdh\DomPDF\PDF;

class ExcelExportAdapter extends AbstractExportAdapter
{
    public function generate()
    {
        $this->reportBuilder->store($this->outputLocalFilePath, 'local');

        return $this;
    }

    public function getFileNameWithExtension($file_name): string
    {

        if (!preg_match("/\.xlsx$/i", $file_name)) {
            $file_name .= '.xlsx';
        }

        return $file_name;

    }
}
