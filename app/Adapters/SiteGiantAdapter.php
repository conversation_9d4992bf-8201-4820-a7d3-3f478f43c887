<?php

namespace App\Adapters;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SiteGiantAdapter extends AbstractEcommerceProviderAdapter
{
    protected string $baseUrl;
    protected string $token;

    public function __construct()
    {
        $this->checkConfig(config('services.sitegiant'), [
            'base_url',
            'secret_key',
            'store_email',
            'partner_token',
        ]);

        $this->baseUrl = config('services.sitegiant.base_url');
        $this->token = $this->getToken();
        $this->channelId = null;
        $this->orderStatuses = [];
    }

    public function getOrders(): array
    {
        $data = [
            'page' => $this->page,
            'limit' => $this->limit,
            'date_from' => $this->dateFrom->format('Y-m-d'),
            'date_to' => $this->dateTo->format('Y-m-d'),
        ];

        if($this->channelId) {
            $data['channel_id'] = $this->channelId;
        }

        if($this->orderStatuses) {
            $data['order_status'] = implode(',', $this->orderStatuses);
        }

        $response = $this->request('orders', $data);


        return $response['response'];
    }

    public function getOrder(): array
    {
        $response = $this->request('orders/' . $this->orderId);

        return $response['response'];
    }

    public function getBranches(): array
    {
        $response = $this->request('channels');

        return $response['response']['channel_list'];
    }

    public function getPaymentMethods(): array
    {
        $response = $this->request('paymentMethods');

        return $response['response']['payment_methods'];
    }

    public function getToken()
    {
        $token = cache('sitegiant_token');

        if (!$token) {
            $response = Http::post("{$this->baseUrl}/getAccessToken", [
                'secretKey' => config('services.sitegiant.secret_key'),
                'storeEmail' => config('services.sitegiant.store_email'),
                'partnerToken' => config('services.sitegiant.partner_token'),
            ])->json();

            if ($response['code'] != 200) {
                $this->handleError($response);
            }

            $token = $response['response']['access_token'];

            cache(['sitegiant_token' => $token], now()->addMonth());
        }

        return $token;
    }

    public function request(string $endpoint, array $data = [], string $method = 'GET'): array
    {
        $response = Http::contentType('application/json')
            ->withHeader('Access-Token', $this->token)
            ->$method("{$this->baseUrl}/{$endpoint}", $data);

        $response->onError(function ($response) {
            if ($response->getStatusCode() == 403) {
                cache()->delete('sitegiant_token');

                $this->getToken();
            }

            $this->handleError(['code' => $response->getStatusCode(), 'message' => $response->getReasonPhrase()]);
        });

        $response = $response->json();


        if ($response['code'] != 200) {
            $this->handleError($response);
        }

        return $response;
    }

    public function handleError(array $response): void
    {
        Log::error('Sitegiant API Error', $response);

        abort(500, json_encode(['title' => 'Sitegiant API Error', 'data' => $response]));
    }

    public function checkConfig(array $config, array $key): void
    {
        if (!Arr::has($config, $key)) {
            $this->handleError([
                'message' => 'Missing configuration key',
                'config' => array_keys($config),
                'required_key' => $key,
            ]);
        }

        foreach ($config as $v) {
            if(!$v) {
                $this->handleError([
                    'message' => 'Missing configuration value',
                    'config' => array_keys($config),
                    'required_key' => $key,
                ]);
            }
        }
    }
}
