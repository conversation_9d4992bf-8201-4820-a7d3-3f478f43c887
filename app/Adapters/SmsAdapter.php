<?php

namespace App\Adapters;

use App\Interfaces\NotificationAdapterInterface;
use App\Contracts\NotificationProviderInterface;

class SmsAdapter implements NotificationAdapterInterface
{
    private NotificationProviderInterface $notificationProvider;

    public function __construct(NotificationProviderInterface $notificationProvider)
    {
        $this->notificationProvider = $notificationProvider;
    }

    public function send(string $recipient, string $content, ?string $subject = null): bool
    {
        return $this->notificationProvider->sendSms($recipient, $content);
    }
}
