<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class MediaValidationRule implements Rule
{
    protected ?array $field_configs = null;
    protected ?string $collection_name = null;
    protected string $type;
    protected int $max_file_size;
    protected array $allowed_mime_types;
    protected bool $required;
    protected ?string $error_message = null;

    /**
     * Create a new rule instance.
     *
     * Can be used in two ways:
     * 1. Single collection: new MediaValidationRule('image', 'single', 5120, ['image'])
     * 2. Multiple collections: new MediaValidationRule(['image' => [...], 'gallery' => [...]])
     *
     * Expected form data format (matches MediaService):
     * media[image][0][file] = (binary)
     * media[image][0][order_column] = 1
     * media[gallery][0][file] = (binary)
     * media[gallery][1][file] = (binary)
     * media[delete][] = 123 (optional - for deleting existing media)
     * media[delete][] = 456
     *
     * @param string|array $collection_name_or_configs Collection name or array of field configs
     * @param string $type Field type ('single' or 'multiple')
     * @param int $max_file_size Maximum file size in KB
     * @param array $allowed_mime_types Allowed file types
     * @param bool $required Whether the collection is required
     */
    public function __construct(
        string|array $collection_name_or_configs,
        string $type = 'single',
        int $max_file_size = 5120,
        array $allowed_mime_types = ['image'],
        bool $required = false
    ) {
        if (is_array($collection_name_or_configs)) {
            // Multiple collections mode
            $this->field_configs = $collection_name_or_configs;
        } else {
            // Single collection mode
            $this->collection_name = $collection_name_or_configs;
            $this->type = $type;
            $this->max_file_size = $max_file_size;
            $this->allowed_mime_types = $allowed_mime_types;
            $this->required = $required;
        }
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        if ($this->field_configs !== null) {
            // Multiple collections mode (like MediaStructureRule)
            return $this->validateMultipleCollections($value);
        } else {
            // Single collection mode (like MediaCollectionRule)
            return $this->validateSingleCollection($value);
        }
    }

    /**
     * Validate multiple collections (MediaStructureRule functionality)
     *
     * @param mixed $value
     * @return bool
     */
    protected function validateMultipleCollections($value): bool
    {
        if (!is_array($value)) {
            $this->error_message = 'The media field must be an array.';
            return false;
        }

        // Handle global delete operations first (matches MediaService)
        if (isset($value['delete']) && is_array($value['delete'])) {
            if (!$this->validateDeleteOperations($value['delete'])) {
                return false;
            }
        }

        // Validate each configured field
        foreach ($this->field_configs as $field_name => $config) {
            if (isset($value[$field_name])) {
                if (!$this->validateField($field_name, $value[$field_name], $config)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate single collection (MediaCollectionRule functionality)
     *
     * @param mixed $value
     * @return bool
     */
    protected function validateSingleCollection($value): bool
    {
        // If value is null/empty and not required, pass validation
        if (empty($value) && !$this->required) {
            return true;
        }

        // If required but empty, fail validation
        if (empty($value) && $this->required) {
            $this->error_message = "The {$this->collection_name} collection is required.";
            return false;
        }

        if (!is_array($value)) {
            $this->error_message = "The {$this->collection_name} collection must be an array.";
            return false;
        }


        if (!isset($value[$this->collection_name])) {
            if ($this->required) {
                $this->error_message = "The {$this->collection_name} collection is required.";
                return false;
            }
            return true; // Optional collection not provided
        }

        $collection_data = $value[$this->collection_name];

        // Validate direct format: [0 => ['file' => ..., 'order_column' => ...], 1 => [...]]
        if (!$this->validateCreateOperations($this->collection_name, $collection_data, $this->max_file_size, $this->allowed_mime_types)) {
            return false;
        }

        // For single type, ensure only one item
        if ($this->type === 'single' && count($collection_data) > 1) {
            $this->error_message = "The {$this->collection_name} collection can only have one item for single type.";
            return false;
        }

        return true;
    }

    /**
     * Validate a specific media field (used in multiple collections mode)
     *
     * @param string $field_name
     * @param mixed $field_value
     * @param array $config
     * @return bool
     */
    protected function validateField(string $field_name, $field_value, array $config): bool
    {
        if (!is_array($field_value)) {
            $this->error_message = "The media.{$field_name} field must be an array.";
            return false;
        }

        // Apply defaults for common configurations
        $config = array_merge([
            'type' => 'single',
            'max_size' => 5120,
            'mime_types' => ['image']
        ], $config);

        $max_size = $config['max_size'];
        $mime_types = $config['mime_types'];

        // Handle direct format: media[image][0][file], media[image][1][file], etc.
        if (!$this->validateCreateOperations($field_name, $field_value, $max_size, $mime_types)) {
            return false;
        }

        return true;
    }

    /**
     * Validate create operations
     *
     * @param string $field_name
     * @param mixed $create_data
     * @param int $max_size
     * @param array $mime_types
     * @return bool
     */
    protected function validateCreateOperations(string $field_name, $create_data, int $max_size, array $mime_types): bool
    {
        if (!is_array($create_data)) {
            $this->error_message = "The media.{$field_name} field must be an array.";
            return false;
        }

        foreach ($create_data as $index => $item) {
            if (!is_array($item)) {
                $this->error_message = "The media.{$field_name}.{$index} field must be an array.";
                return false;
            }

            // Validate required file field
            if (!isset($item['file'])) {
                $this->error_message = "The media.{$field_name}.{$index}.file field is required.";
                return false;
            }

            $file = $item['file'];

            // Check if it's a valid uploaded file
            if (!$file || !method_exists($file, 'getSize') || !method_exists($file, 'getMimeType')) {
                $this->error_message = "The media.{$field_name}.{$index}.file must be a valid uploaded file.";
                return false;
            }

            // Validate file size
            if ($file->getSize() > ($max_size * 1024)) {
                $this->error_message = "The media.{$field_name}.{$index}.file exceeds maximum size of {$max_size}KB.";
                return false;
            }

            // Validate MIME type
            if (!$this->isValidMimeType($file->getMimeType(), $mime_types)) {
                $allowed_types = implode(', ', $mime_types);
                $this->error_message = "The media.{$field_name}.{$index}.file must be one of: {$allowed_types}.";
                return false;
            }

            // Validate order_column if present (matches MediaService expectation)
            if (isset($item['order_column'])) {
                if (!is_numeric($item['order_column']) || $item['order_column'] < 1) {
                    $this->error_message = "The media.{$field_name}.{$index}.order_column must be a positive integer.";
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Validate delete operations (matches MediaService deleteMediaByIds)
     *
     * @param array $delete_data
     * @return bool
     */
    protected function validateDeleteOperations(array $delete_data): bool
    {
        foreach ($delete_data as $index => $media_id) {
            if (!is_numeric($media_id)) {
                $this->error_message = "The media.delete.{$index} must be a valid media ID.";
                return false;
            }
        }

        return true;
    }

    /**
     * Check if MIME type is valid
     *
     * @param string $mime_type
     * @param array $allowed_types
     * @return bool
     */
    protected function isValidMimeType(string $mime_type, array $allowed_types): bool
    {
        foreach ($allowed_types as $allowed_type) {
            switch ($allowed_type) {
                case 'image':
                    if (str_starts_with($mime_type, 'image/')) {
                        return true;
                    }
                    break;
                case 'video':
                    if (str_starts_with($mime_type, 'video/')) {
                        return true;
                    }
                    break;
                case 'audio':
                    if (str_starts_with($mime_type, 'audio/')) {
                        return true;
                    }
                    break;
                case 'document':
                    $document_types = [
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'text/plain'
                    ];
                    if (in_array($mime_type, $document_types)) {
                        return true;
                    }
                    break;
                default:
                    // If it's a specific MIME type like 'image/jpeg', check directly
                    if (str_contains($allowed_type, '/')) {
                        if ($mime_type === $allowed_type) {
                            return true;
                        }
                    } else {
                        // For unknown types, assume it's a general category
                        if (str_starts_with($mime_type, $allowed_type . '/')) {
                            return true;
                        }
                    }
                    break;
            }
        }

        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return $this->error_message ?? 'The media field is invalid.';
    }
}
