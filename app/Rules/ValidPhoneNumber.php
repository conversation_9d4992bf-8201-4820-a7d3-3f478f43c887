<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use libphonenumber\PhoneNumberUtil;

class ValidPhoneNumber implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        try {
            $phoneUtil = PhoneNumberUtil::getInstance();

            $isValid = $phoneUtil->isValidNumber($phoneUtil->parse($value));

            if (!$isValid) {
                $fail('The :attribute is not a valid phone number.');
            }
        } catch (\Exception $e) {
            $fail('The :attribute is not a valid phone number.');
        }
    }
}
