<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class UniqueGmailAddressRule implements ValidationRule
{
    protected string $table;
    protected string $column;
    protected ?int $ignoreId;

    public function __construct(string $table, string $column, ?int $ignoreId = null)
    {
        $this->table = $table;
        $this->column = $column;
        $this->ignoreId = $ignoreId;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Basic validation
        if (empty($value) || !is_string($value) || !str_contains($value, '@')) {
            return;
        }

        if (!$this->isGmailAddress($value)) {
            return; // Not a Gmail address, skip validation
        }

        $normalizedEmail = $this->normalizeGmailAddress($value);

        // Get all existing Gmail addresses from the database
        $existingEmails = DB::table($this->table)
            ->whereNotNull($this->column)
            ->whereNull('deleted_at')
            ->when($this->ignoreId, function ($query) {
                return $query->where('id', '!=', $this->ignoreId);
            })
            ->pluck($this->column)
            ->toArray();

        // Check if any existing email normalizes to the same value
        foreach ($existingEmails as $existingEmail) {
            if ($this->isGmailAddress($existingEmail)) {
                $existingNormalized = $this->normalizeGmailAddress($existingEmail);
                if ($existingNormalized === $normalizedEmail) {
                    $fail('The email has already been taken (Gmail addresses with dots/plus signs are considered identical).');
                    return;
                }
            }
        }
    }

    /**
     * Check if the email is a Gmail address
     */
    protected function isGmailAddress(string $email): bool
    {
        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return false;
        }

        $domain = strtolower(trim($parts[1]));
        return in_array($domain, ['gmail.com', 'googlemail.com']);
    }

    /**
     * Normalize Gmail address by removing dots and plus signs
     */
    protected function normalizeGmailAddress(string $email): string
    {
        $parts = explode('@', strtolower(trim($email)));
        if (count($parts) !== 2) {
            return $email; // Return original if invalid format
        }

        [$username, $domain] = $parts;

        // Remove dots from username
        $username = str_replace('.', '', $username);

        // Remove plus signs and everything after
        $username = explode('+', $username)[0];

        // Normalize domain (googlemail.com -> gmail.com)
        $domain = $domain === 'googlemail.com' ? 'gmail.com' : $domain;

        return $username . '@' . $domain;
    }
}
