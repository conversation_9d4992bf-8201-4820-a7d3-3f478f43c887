<?php

namespace App\Contracts;

interface NotificationProviderInterface
{
    /**
     * Send an SMS message
     *
     * @param string $phoneNumber
     * @param string $messageBody
     * @return bool
     */
    public function sendSms(string $phoneNumber, string $messageBody): bool;

    /**
     * Send a WhatsApp message
     *
     * @param string $phoneNumber
     * @param string $messageBody
     * @return bool
     */
    public function sendWhatsAppMessage(string $phoneNumber, string $messageBody): bool;

    /**
     * Send a WhatsApp template message
     *
     * @param string $phoneNumber
     * @param string $templateName
     * @param string $languageCode
     * @param array $components
     * @return bool
     */
    public function sendWhatsAppTemplate(
        string $phoneNumber,
        string $templateName,
        string $languageCode = 'en',
        array $components = []
    ): bool;
}
