<?php

namespace App\Services;

use App\Repositories\RoleRepository;

class RoleService extends BaseService
{
    public function __construct(RoleRepository $role_repository)
    {
        $this->repository = $role_repository;
        $this->relationship = ['permissions'];
    }

    /**
     * Sync permissions for the role
     *
     * @param array $permissions
     * @return static
     */
    public function syncPermissions(array $permissions): static
    {
        $this->repository->syncPermissions($this->model, $permissions);
        return $this;
    }

    /**
     * Sync users for the role
     *
     * @param array $user_ids
     * @return static
     */
    public function syncUsers(array $user_ids): static
    {
        $this->repository->syncUsers($this->model, $user_ids);

        return $this;
    }
}
