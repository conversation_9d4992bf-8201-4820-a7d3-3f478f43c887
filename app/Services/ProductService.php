<?php

namespace App\Services;

use App\Repositories\ProductRepository;
use Illuminate\Support\Arr;

class ProductService extends BaseService
{
    public function __construct(
        ProductRepository                  $product_repository,
        protected MediaService             $mediaService,
        protected ProductVariantService    $productVariantService,
        protected ProductBundleItemService $productBundleItemService,
    ) {
        $this->repository = $product_repository;
        $this->relationship = ['variants', 'bundleItems', 'warehouses'];
    }

    /**
     * Sync product variants - handles create, update, and delete operations
     *
     * This method will:
     * 1. Create new variants (those without ID)
     * 2. Update existing variants (those with ID)
     * 3. Delete variants that are no longer in the provided array
     */
    public function syncVariants(array $variants): static
    {
        // If no variants provided, delete all existing variants
        if (empty($variants)) {
            $this->repository->deleteVariants($this->model);

            return $this;
        }

        // Get all existing variant IDs for this product
        $existing_variant_ids = $this->getModel()
            ->variants
            ->pluck('id')
            ->toArray();

        // Track which existing variants are being kept/updated
        $variant_to_keep_ids = [];

        // Process each variant in the input array
        foreach ($variants as $variant_data) {
            // Remove gallery from variant data since it's not fillable
            unset($variant_data['gallery']);

            if (!empty($variant_data['id'])) {
                // Update existing variant
                $variant_to_keep_ids[] = $variant_data['id'];

                $this->productVariantService
                    ->setModelById($variant_data['id'])
                    ->update($variant_data);
            } else {
                // Create new variant
                $variant_data['product_id'] = $this->model->id;

                $variant = $this->productVariantService
                    ->store($variant_data)
                    ->getModel();

                // Add the new variant ID to the keep list
                $variant_to_keep_ids[] = $variant->id;
            }
        }

        // Delete variants that are no longer needed
        $variant_to_delete_ids = array_diff($existing_variant_ids, $variant_to_keep_ids);

        if (!empty($variant_to_delete_ids)) {
            $this->repository->deleteVariants($this->model, $variant_to_delete_ids);
        }

        return $this;
    }

    /**
     * Sync bundle items - handles create, update, and delete operations
     */
    public function syncBundleItems(array $items): static
    {
        // If no variants provided, delete all existing variants
        if (empty($items)) {
            $this->repository->deleteBundleItems($this->model);

            return $this;
        }

        $existing_item_ids = $this->getModel()
            ->bundleItems
            ->pluck('item_product_id')
            ->toArray();

        $item_to_keep_ids = [];

        foreach ($items as $item) {
            $item_to_keep_ids[] = $item['item_product_id'];

            $this->productBundleItemService->updateOrCreate([
                'item_product_id' => $item['item_product_id'],
                'bundle_product_id' => $this->model->id,
            ], [
                'quantity' => Arr::get($item, 'quantity', 1),
            ]);
        }

        // Delete items not present anymore
        $item_to_delete_ids = array_diff($existing_item_ids, $item_to_keep_ids);

        if (!empty($item_to_delete_ids)) {
            $this->repository->deleteBundleItems($this->model, $item_to_delete_ids);
        }

        return $this;
    }

    public function syncCategories(array $product_category_ids): static
    {
        $this->repository->syncCategories($this->model, $product_category_ids);
        return $this;
    }

    public function syncWarehouses(array $warehouses): static
    {
        $warehouses = collect($warehouses)
            ->mapWithKeys(fn($warehouse) => [
                $warehouse['id'] => ['label' => $warehouse['label'] ?? null],
            ])
            ->toArray();

        $this->repository->syncWarehouses($this->model, $warehouses);
        return $this;
    }

    /**
     * Handle media operations for the product
     */
    public function syncMedia(array $media_data): static
    {
        $this->mediaService->handleMedia($this->model, $media_data);
        return $this;
    }
}
