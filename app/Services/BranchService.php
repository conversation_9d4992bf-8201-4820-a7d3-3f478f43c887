<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Repositories\BranchRepository;


class BranchService extends BaseService
{
    public function __construct(
        BranchRepository               $branch_repository,
        protected BranchDetailsService $branchDetailsService,
        protected MediaService         $mediaService
    ) {
        $this->repository = $branch_repository;
        $this->relationship = ['details'];
    }

    /**
     * Override delete method to check for associated models
     */
    public function delete(): static
    {
        // Check if branch has any associated models
        if ($this->repository->hasAssociatedModels($this->model)) {
            ErrorCodeHelper::throwError(
                ErrorCodeHelper::MASTER_DATA_ERROR,
                10001,
            );
        }

        // If no associated models, proceed with deletion
        return parent::delete();
    }

    public function syncDetails(array $data): static
    {
        $this->branchDetailsService->updateOrCreate(
            ['branch_id' => $this->model->id],
            $data
        );
        return $this;
    }

    /**
     * Handle media operations for the branch
     */
    public function syncMedia(array $media_data): static
    {
        $this->mediaService->handleMedia($this->model, $media_data);
        return $this;
    }
}
