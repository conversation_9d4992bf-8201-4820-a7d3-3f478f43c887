<?php

namespace App\Services;

use App\Enums\NotificationChannel;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Notifications\GenericNotification;
use App\Repositories\NotificationRepository;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    protected $templateName;

    protected $user;

    protected $locale = 'en';

    protected $notificationProvider;

    protected array $templateData = [];

    // Define the preferred order of channels
    protected array $preferredChannels = [NotificationChannel::MAIL, NotificationChannel::WHATSAPP, NotificationChannel::SMS];

    public function __construct(protected NotificationRepository $notificationRepository)
    {
        // Don't instantiate provider here - do it lazily when needed
        $this->notificationProvider = null;
    }

    /**
     * Send notification using <PERSON><PERSON>'s native notification system
     * Implements the flow: Get template -> Set adapter -> Call send -> Use provider
     *
     * @param array $replacements
     * @return bool
     */
    public function send(): bool
    {
        if (!$this->user) {
            Log::warning("No user set for notification '{$this->templateName}'.");
            return false;
        }

        // Step 1: Get template - Determine active channel
        $activeChannel = $this->determineActiveChannel($this->templateName, $this->locale);
        if (!$activeChannel) {
            Log::warning("No active channel found for '{$this->templateName}' in locale '{$this->locale}'.");
            return false;
        }

        // Step 2: Get template for the determined channel
        $template = $this->notificationRepository->getTemplate($this->templateName, $activeChannel, $this->locale);
        if (!$template) {
            return false;
        }

        // Step 3: Prepare template content with replacements
        $content = $this->prepareTemplateContent($template);

        $this->user->notify(new GenericNotification(
            $this->templateName,
            $this->templateData,
            [$activeChannel],
            $content
        ));

        return true;
    }

    public function setPreferredChannels(array $channels): static
    {
        $this->preferredChannels = $channels;
        return $this;
    }

    public function setTemplateName(string $templateName): static
    {
        $this->templateName = $templateName;
        return $this;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function setLocale(string $locale): static
    {
        $this->locale = $locale;
        return $this;
    }

    public function setTemplateData(array $data): static
    {
        $this->templateData = $data;

        return $this;
    }


    /**
     * Determines the single active channel based on preferred order and template existence.
     *
     * @param string $templateName
     * @param string $locale
     * @return string|null Returns the channel class name or null if no suitable channel found
     */
    public function determineActiveChannel(string $templateName, string $locale = 'en'): ?string
    {
        // Use repository to fetch all potential templates for this notification name and locale
        $availableTemplates = $this->notificationRepository->getAvailableTemplates(
            $templateName,
            $locale,
            $this->preferredChannels
        );

        foreach ($this->preferredChannels as $channel) {
            // Check if the channel exists in the already fetched collection
            if ($availableTemplates->has($channel)) {
                return $channel; // Return the enum value directly
            }
        }

        // If no template is found for any preferred channel, return null
        Log::warning("No suitable active template found for notification '{$templateName}' for any preferred channel or locale '{$locale}'.");
        return null;
    }

    /**
     * Prepare template content with replacements
     */
    protected function prepareTemplateContent(NotificationTemplate $template): array
    {
        $searchKeys = array_map(fn($key) => '{{' . $key . '}}', array_keys($this->templateData));
        $replaceValues = array_values($this->templateData);

        return [
            'subject' => str_replace($searchKeys, $replaceValues, $template->subject ?? ''),
            'content' => str_replace($searchKeys, $replaceValues, $template->content),
        ];
    }
}
