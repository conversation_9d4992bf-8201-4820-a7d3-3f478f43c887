<?php

namespace App\Services;

use App\Contracts\NotificationProviderInterface;
use App\Helpers\ErrorCodeHelper;
use Illuminate\Support\Facades\Log;
use Sendpulse\RestApi\ApiClient;
use Sendpulse\RestApi\ApiClientException;
use Sendpulse\RestApi\Storage\FileStorage;

class SendPulseService implements NotificationProviderInterface
{
    protected ApiClient $apiClient;
    protected string|null $whatsappBotId;
    protected string|null $smsSender;

    public function __construct()
    {
        $apiUserId = config('services.sendpulse.client_id');
        $apiSecret = config('services.sendpulse.client_secret');

        $this->whatsappBotId = config('services.sendpulse.whatsapp_bot_id');
        $this->smsSender = config('services.sendpulse.sms_sender');

        if (empty($apiUserId) || empty($apiSecret)) {
            Log::error('SendPulse API credentials are not fully configured.');
            ErrorCodeHelper::throwError(ErrorCodeHelper::NOTIFICATION_ERROR, 16001);
        }

        $this->apiClient = new ApiClient($apiUserId, $apiSecret, new FileStorage());
    }

    /**
     * Sends an SMS message via SendPulse.
     */
    public function sendSms(string $phoneNumber, string $messageBody): bool
    {
        if (empty($this->smsSender)) {
            Log::error('Cannot send SMS: SendPulse SMS Sender is not configured.');
            return false;
        }

        try {
            // SendPulse SMS example shows "380683850429", which implies numbers without '+' prefix.
            // Ensure the phone number is formatted correctly for SendPulse's SMS API.
            $formattedPhoneNumber = ltrim($phoneNumber, '+');

            $data = [
                "sender" => $this->smsSender,
                "phones" => [$formattedPhoneNumber],
                "body" => $messageBody,
            ];

            $response = $this->apiClient->post('sms/send', $data);

            if (isset($response['result']) && $response['result'] === true) {
                Log::info("SMS sent successfully to {$phoneNumber}. Response: " . json_encode($response));
                return true;
            }

            Log::error("Failed to send SMS to {$phoneNumber}. Response: " . json_encode($response));
            return false;
        } catch (ApiClientException $e) {
            Log::error("SendPulse SMS API error for {$phoneNumber}: " . $e->getMessage());
            return false;
        } catch (\Exception $e) {
            Log::error("Unexpected error sending SMS to {$phoneNumber}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sends a WhatsApp text message via SendPulse.
     */
    public function sendWhatsAppMessage(string $phoneNumber, string $messageBody): bool
    {
        if (empty($this->whatsappBotId)) {
            Log::error('Cannot send WhatsApp message: SendPulse WhatsApp Bot ID is not configured.');
            return false;
        }
        $formattedPhoneNumber = ltrim($phoneNumber, '+');
        try {
            $data = [
                "bot_id" => $this->whatsappBotId,
                "phone" => $formattedPhoneNumber,
                "message" => [
                    "type" => "text",
                    "text" => [
                        "body" => $messageBody,
                    ],
                ],
            ];

            $response = $this->apiClient->post('whatsapp/contacts/sendByPhone', $data);

            if (isset($response['success']) && $response['success'] === true) {
                Log::info("WhatsApp message sent successfully to {$phoneNumber}. Response: " . json_encode($response));
                return true;
            }

            Log::error("Failed to send WhatsApp message to {$phoneNumber}. Response: " . json_encode($response));
            return false;
        } catch (ApiClientException $e) {
            Log::error("SendPulse WhatsApp API error for {$phoneNumber}: " . $e->getMessage());
            return false;
        } catch (\Exception $e) {
            Log::error("Unexpected error sending WhatsApp message to {$phoneNumber}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sends a WhatsApp template message via SendPulse.
     */
    public function sendWhatsAppTemplate(
        string $phoneNumber,
        string $templateName,
        string $languageCode = 'en',
        array $components = []
    ): bool {
        if (empty($this->whatsappBotId)) {
            Log::error('Cannot send WhatsApp template: SendPulse WhatsApp Bot ID is not configured.');
            return false;
        }

        try {
            $data = [
                "bot_id" => $this->whatsappBotId,
                "phone" => $phoneNumber,
                "template" => [
                    "name" => $templateName,
                    "language" => [
                        "code" => $languageCode,
                    ],
                    "components" => $components,
                ],
            ];

            $response = $this->apiClient->post('whatsapp/contacts/sendTemplateByPhone', $data);

            if (isset($response['success']) && $response['success'] === true) {
                Log::info("WhatsApp template message sent successfully to {$phoneNumber}. Template: {$templateName}. Response: " . json_encode($response));
                return true;
            }

            Log::error("Failed to send WhatsApp template to {$phoneNumber}. Template: {$templateName}. Response: " . json_encode($response));
            return false;
        } catch (ApiClientException $e) {
            Log::error("SendPulse WhatsApp template API error for {$phoneNumber}: " . $e->getMessage());
            return false;
        } catch (\Exception $e) {
            Log::error("Unexpected error sending WhatsApp template to {$phoneNumber}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the API client instance for advanced usage
     */
    public function getApiClient(): ApiClient
    {
        return $this->apiClient;
    }

    /**
     * Get WhatsApp Bot ID
     */
    public function getWhatsAppBotId(): ?string
    {
        return $this->whatsappBotId;
    }

    /**
     * Get SMS Sender
     */
    public function getSmsSender(): ?string
    {
        return $this->smsSender;
    }
}
