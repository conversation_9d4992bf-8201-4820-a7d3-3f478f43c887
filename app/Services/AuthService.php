<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Hash;

class AuthService extends BaseService
{
    protected ?User $user;
    protected array $credentials = [];
    protected array $requiredRoles = [];
    protected array $tokenData = [];

    public function __construct(
        UserRepository $user_repository
    )
    {
        $this->repository = $user_repository;
    }

    /**
     * Set credentials for authentication
     */
    public function setCredentials(array $credentials): static
    {
        $this->credentials = $credentials;
        return $this;
    }

    /**
     * Set required roles for login
     */
    public function setRequiredRoles(array $roles): static
    {
        $this->requiredRoles = $roles;
        return $this;
    }

    /**
     * Validate credentials and find user
     */
    public function validateCredentials(): static
    {
        $email = $this->credentials['email'];
        $password = $this->credentials['password'];

        $this->user = $this->repository->findWhere([
            'email' => $email,
            'is_active' => true,
        ])->first();

        if (!$this->user || !Hash::check($password, $this->user->password)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 14002);
        }

        return $this;
    }

    /**
     * Check if user has required roles for login type, if requiredRoles is empty, then any role can login
     */
    public function checkRolePermissions(): static
    {
        if ($this->requiredRoles && !$this->user->hasAnyRole($this->requiredRoles)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 14003, ['roles' => implode(', ', $this->requiredRoles)]);
        }

        return $this;
    }

    public function generateToken(): static
    {
        if (!$this->user) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::USER_ERROR, 14004);
        }

        $token = $this->user->createToken('auth_token')->plainTextToken;

        $this->tokenData = [
            'token' => $token,
            'token_type' => 'Bearer',
        ];

        return $this;
    }

    public function getTokenData(): array
    {
        return $this->tokenData;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;
        return $this;
    }
}
