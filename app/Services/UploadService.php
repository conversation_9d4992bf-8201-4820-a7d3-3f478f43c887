<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

class UploadService extends BaseService
{
    protected string $filename;

    public function generatePresignedUrl(): array
    {
        // Generate unique file key while preserving the sanitized filename
        $file_extension = pathinfo($this->filename, PATHINFO_EXTENSION);
        $filename_without_ext = pathinfo($this->filename, PATHINFO_FILENAME);

        // Create unique filename by prepending timestamp and unique ID to preserve original name
        $unique_file_name = uniqid() . '_' . $filename_without_ext . '.' . $file_extension;
        $key = 'uploads/' . date('Y/m/d') . '/' . $unique_file_name;


        // Use Laravel's temporaryUploadUrl method for S3 with public-read ACL and proper Content-Type
        ['url' => $upload_url, 'headers' => $headers] = Storage::disk('s3')->temporaryUploadUrl(
            $key,
            now()->addMinutes(15),
            [
                'ACL' => 'public-read',
            ]
        );

        // Generate the permanent file URL
        $file_url = Storage::disk('s3')->url($key);

        return [
            'upload_url' => $upload_url,
            'file_url' => $file_url,
            'key' => $key,
            'expires_in' => 900, // 15 minutes in seconds
            'method' => 'PUT',
            'headers' => $headers,
            'original_name' => $this->filename,
        ];
    }

    public function setFileName(string $filename): static
    {
        $this->filename = $filename;

        return $this;
    }
}
