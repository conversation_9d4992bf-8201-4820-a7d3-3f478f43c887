<?php

namespace App\Services;


class BaseService
{
    protected $model;
    protected $repository;
    protected array $relationship = [];

    public function findVia($value, $key = 'id')
    {
        return $this->repository->findByField($key, $value)->first();
    }

    public function all()
    {
        return $this->repository->all();
    }

    public function store(array $data): static
    {
        $model = $this->repository->create($data);

        $this->setModel($model);

        return $this;
    }

    public function update(array $data): static
    {
        $model = $this->repository->update($data, $this->model->id);

        $this->setModel($model);

        return $this;
    }

    public function delete(): static
    {
        $this->repository->delete($this->model->id);

        return $this;
    }

    public function updateOrCreate(array $attributes, array $data = []): static
    {
        $model = $this->repository->updateOrCreate($attributes, $data);

        $this->setModel($model);

        return $this;
    }

    public function setModel($model): static
    {
        $this->model = $model;

        return $this;
    }

    public function setModelById($id): static
    {
        $this->model = $this->repository->find($id);

        return $this;
    }

    public function getModel()
    {
        if ($this->relationship) {
            $this->model->loadMissing($this->relationship);
        }

        return $this->model;
    }

    public function getRepository()
    {
        return $this->repository;
    }
}
