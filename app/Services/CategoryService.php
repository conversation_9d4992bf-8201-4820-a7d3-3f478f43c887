<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Repositories\CategoryRepository;

class CategoryService extends BaseService
{
    public function __construct(
        CategoryRepository $category_repository,
        protected MediaService $mediaService
    ) {
        $this->repository = $category_repository;
    }

    /**
     * Delete a category after checking if it has children.
     */
    public function delete(): static
    {
        // Check if category has children
        if ($this->model->hasChildren()) {
            ErrorCodeHelper::throwError(
                ErrorCodeHelper::MASTER_DATA_ERROR,
                10001
            );
        }

        return parent::delete();
    }

    /**
     * Handle media operations for the category
     */
    public function syncMedia(array $media_data): static
    {
        $this->mediaService->handleMedia($this->model, $media_data);
        return $this;
    }
}
