<?php

namespace App\Services;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Http\UploadedFile;

class MediaService
{
    /**
     * Handle media operations for a model
     *
     * @param HasMedia&InteractsWithMedia $model
     * @param array $media_data
     * @return void
     */
    public function handleMedia($model, array $media_data): void
    {
        // Handle global delete operations first
        if (isset($media_data['delete']) && is_array($media_data['delete'])) {
            $this->deleteMediaByIds($model, $media_data['delete']);
        }

        // Handle media creation for each collection
        foreach ($media_data as $collection_name => $media_items) {

            // Skip the 'delete' key as it's handled above
            if ($collection_name === 'delete' || !is_array($media_items)) {
                continue;
            }

            $this->createMedia($model, $media_items, $collection_name);
        }
    }

    /**
     * Create media items for a collection
     *
     * @param HasMedia&InteractsWithMedia $model
     * @param array $media_items
     * @param string $collection_name
     * @return void
     */
    protected function createMedia($model, array $media_items, string $collection_name): void
    {
        foreach ($media_items as $media_item) {
            if (!isset($media_item['file'])) {
                continue;
            }

            $file = $media_item['file'];
            $order_column = $media_item['order_column'] ?? null;

            if ($file instanceof UploadedFile) {
                $media_adder = $model->addMedia($file);

                if ($order_column !== null) {
                    $media_adder->setOrder($order_column);
                }

                $media_adder->toMediaCollection($collection_name);
            }
        }
    }

    /**
     * Delete media items by their IDs across all collections
     *
     * @param HasMedia&InteractsWithMedia $model
     * @param array $media_ids
     * @return void
     */
    protected function deleteMediaByIds($model, array $media_ids): void
    {
        $media_items = $model->getMedia("*");

        foreach ($media_ids as $media_id) {
            $media_item = $media_items->where('id', $media_id)->first();

            if ($media_item) {
                $media_item->delete();
            }
        }
    }
}
