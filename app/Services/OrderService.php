<?php

namespace App\Services;

use App\Enums\OrderStatus;
use App\Helpers\ErrorCodeHelper;
use App\Jobs\CreateAutoCountInvoice;
use App\Repositories\OrderRepository;
use Illuminate\Support\Arr;


class OrderService extends BaseService
{
    public function __construct(
        OrderRepository               $orderRepository,
        protected OrderProductService $orderProductService,
        protected OrderStatusService  $orderStatusService,
        protected ProductService      $productService,
    )
    {
        $this->repository = $orderRepository;
    }

    public function syncProducts(array $product_data): static
    {
        $this->repository->clearProducts($this->model);

        foreach ($product_data as $product) {
            $product['order_id'] = $this->model->id;
            $this->orderProductService->store($product);
        }

        return $this;
    }

    public function syncBranches(array $branches): static
    {
        $this->repository->syncBranches($this->model, $branches);
        return $this;
    }

    public function linkProducts(array $order_products): static
    {
        foreach ($order_products as $order_product) {
            $product = $this->productService->findVia($order_product['product_id']);
            $this->orderProductService
                ->setModelById($order_product['id'])
                ->update([
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                ]);
        }

        return $this;
    }

    public function validateOrderProducts(array $order_products): static
    {
        if ($this->model->status->slug != OrderStatus::PROCESSING && $this->model->status->slug != OrderStatus::PACKED) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ORDER_ERROR, 17002);
        }

        $model_order_product_ids = $this->model->products->pluck('id')->toArray();
        $data_order_product_ids = Arr::pluck($order_products, 'id');

        sort($model_order_product_ids);
        sort($data_order_product_ids);

        if ($model_order_product_ids !== $data_order_product_ids) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ORDER_ERROR, 17001);
        }

        return $this;
    }

    public function updateStatus(string $status): static
    {
        $status = $this->orderStatusService->findVia($status, 'slug');

        $this->update(['order_status_id' => $status->id]);

        return $this;
    }

    public function createAutoCountInvoice(): static
    {
        $this->relationship = ['products.product.bundleItems.item', 'branches', 'status'];

        CreateAutoCountInvoice::dispatch($this->getModel());

        return $this;
    }
}
