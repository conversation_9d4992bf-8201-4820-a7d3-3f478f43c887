<?php

namespace App\Services;

use App\Enums\PaperOrientation;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\Printable;
use App\Traits\ExportFileAdaptable;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;

/**
 * Used to print PDF of a single model only.
 */
class DocumentPrintService
{
    use ExportFileAdaptable;

    const PRINT_FOLDER = 'printing';

    protected Printable $printable;
    protected string $remoteFileUrl;
    protected string $localFilePath;
    protected string $fileName;
    protected string $paperOrientation;

    const FILE_SYSTEM = 's3';
    protected string $model;
    protected array $request;

    public function __construct(protected ExportHistoryService $exportHistoryService)
    {
        $this->paperOrientation = PaperOrientation::PORTRAIT;
    }

    public function generate(): static
    {
        if (!isset($this->exportFileAdapter)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::REPORT_ERROR,13001);
        }

        $this->localFilePath = $this->exportFileAdapter->getFileNameWithExtension(static::PRINT_FOLDER . DIRECTORY_SEPARATOR . $this->fileName);

        $this->setFileName($this->exportFileAdapter->getFileNameWithExtension($this->fileName));

        $this->exportFileAdapter
            ->setOutputLocalFilePath($this->localFilePath)
            ->setPaperOrientation($this->paperOrientation)
            ->generate();

        return $this;

    }

    public function upload(): static
    {
        // upload to S3 storage
        $absolute_path = Storage::disk('local')->path($this->localFilePath);

        Storage::disk(static::FILE_SYSTEM)->putFileAs(static::PRINT_FOLDER, new File($absolute_path), $this->fileName);

        $this->remoteFileUrl = Storage::disk(static::FILE_SYSTEM)->url($this->getLocalFilePathInUrlFormat());

        // delete from local
        Storage::disk('local')->delete($this->localFilePath);

        return $this;
    }

    public function save(): static
    {
        $this->exportHistoryService->store([
            'model' => $this->model,
            'filename' => $this->getFileName(),
//            'path' => $filepath,
            'url' => $this->getFileUrl(),
//            'from_date' => $this->from,
//            'to_date' => $this->to,
            'options' => $this->request,
            'status' => 'success'
        ]);

        return $this;
    }

    public function deleteFromRemote(): void
    {
        Storage::disk(static::FILE_SYSTEM)->delete($this->getLocalFilePathInUrlFormat());
    }

    public function getLocalFilePathInUrlFormat(): array|string
    {
        // replace any directory separator with '/' since URL is using '/' anyways
        return str_replace(DIRECTORY_SEPARATOR, '/', $this->localFilePath);
    }

    public function getPrintable(): Printable
    {
        return $this->printable;
    }

    public function setPrintable(Printable $printable): static
    {
        $this->printable = $printable;
        return $this;
    }

    public function getFileUrl(): string
    {
        return $this->remoteFileUrl;
    }

    public function getLocalFilePath(): string
    {
        return $this->localFilePath;
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }

    public function setFileName(string $filename): static
    {
        $this->fileName = $filename;
        return $this;
    }

    public function getPaperOrientation(): string
    {
        return $this->paperOrientation;
    }

    public function setPaperOrientation(string $paperOrientation): DocumentPrintService
    {
        $this->paperOrientation = $paperOrientation;
        return $this;
    }

    public function setModel(string $model): static
    {
        $this->model = $model;

        return $this;
    }

    public function setRequest(array $request): static
    {
        $this->request = $request;

        return $this;
    }


}
