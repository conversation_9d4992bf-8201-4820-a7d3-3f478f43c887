<?php

namespace App\Services;

use App\Repositories\SettingRepository;

class SettingService extends BaseService
{
    public function __construct(SettingRepository $setting_repository)
    {
        $this->repository = $setting_repository;
    }

    public function all()
    {
        return cache('settings');
    }

    public function cache(): static
    {
        $settings = $this->repository->all();

        cache()->forever('settings', $settings);

        return $this;
    }
}
