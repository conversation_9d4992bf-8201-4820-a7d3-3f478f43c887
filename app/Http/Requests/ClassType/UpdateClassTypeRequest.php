<?php

namespace App\Http\Requests\ClassType;

use App\Enums\ClassTypePriceType;
use App\Models\Category;
use App\Models\TaxClass;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateClassTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'duration_in_minutes' => ['sometimes', 'required', 'integer', 'min:1'],
            'class_count' => ['nullable', 'integer', 'min:1'],
            'price_type' => ['sometimes', 'required', 'string', Rule::in(ClassTypePriceType::getValues())],
            'price' => ['nullable', 'numeric', 'min:0'],
            'tax_class_id' => ['nullable', Rule::exists(TaxClass::class, 'id')],
            'colour' => ['sometimes', 'required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'is_addon' => ['boolean'],
            'is_bookable' => ['boolean'],
            'is_active' => ['boolean'],
            'category_ids' => ['nullable', 'array'],
            'category_ids.*' => [Rule::exists(Category::class, 'id')],
        ];
    }
}
