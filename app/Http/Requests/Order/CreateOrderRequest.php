<?php

namespace App\Http\Requests\Order;

use App\Models\Branch;
use App\Models\CountryState;
use App\Models\OrderStatus;
use App\Models\PaymentMethod;
use App\Models\Product;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'branch_id' => ['required', Rule::exists(Branch::class, 'id')],
            'customer' => 'required|array',
            'customer.id' => 'nullable',
            'customer.email' => 'nullable|email',
            'customer.phone' => 'nullable|string',
            'customer_name' => 'nullable|string',
            'customer_contact' => 'nullable|string',
            'customer_email' => 'nullable|email',
            'order_status_id' => ['nullable', Rule::exists(OrderStatus::class, 'id')],
            'amount' => 'nullable|numeric',
            'total' => 'nullable|numeric',
            'billing_address' => 'array',
            'shipping_address' => 'array',
            'state_id' => ['nullable', Rule::exists(CountryState::class, 'id')],
            'products' => 'required|array',
            'products.*.id' => ['required', Rule::exists(Product::class, 'id')],
            'products.*.product_variant_id' => 'nullable',
            'products.*.quantity' => 'numeric',
            'products.*.price' => 'numeric',
            'shipping_method' => 'string',
            'delivery_fee' => 'numeric',
            'delivery_remarks' => 'nullable|string',
            'discount' => 'numeric',
            'payment_method' => ['nullable', Rule::exists(PaymentMethod::class, 'id')],
            'payment_reference_id' => 'nullable|string',
            'payment_remarks' => 'nullable|string',
            'paid_total' => 'nullable|numeric',
            'comment' => 'nullable|string',
            'coupon' => 'nullable|string',
            'sale_by_id' => ['nullable', Rule::exists(User::class, 'id')],
            'shop_id' => ['nullable', Rule::exists(Shop::class, 'id')],
        ];
    }
}
