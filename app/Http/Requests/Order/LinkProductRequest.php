<?php

namespace App\Http\Requests\Order;

use App\Models\OrderProduct;
use App\Models\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LinkProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'order_products' => ['required', 'array'],
            'order_products.*.product_id' => ['required', 'integer', Rule::exists(Product::class, 'id')],
            'order_products.*.id' => ['required', 'integer', Rule::exists(OrderProduct::class, 'id')],
        ];
    }
}
