<?php

namespace App\Http\Requests\Auth;

use App\Helpers\SanitizesInput;
use App\Rules\ValidPhoneNumber;
use App\Rules\UniqueGmailAddressRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'firstname' => ['required', 'string', 'max:255'],
            'lastname' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                Rule::unique('users', 'email')->withoutTrashed(),
                'email',
                new UniqueGmailAddressRule('users', 'email')
            ],
            'phone' => ['nullable', Rule::unique('users', 'phone')->withoutTrashed(), new ValidPhoneNumber],
            'password' => ['required', 'string', 'min:8'],
            'dob' => ['nullable', 'date', 'before:today'],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'firstname' => $this->firstname ? SanitizesInput::sanitizeName($this->firstname) : $this->firstname,
            'lastname' => $this->lastname ? SanitizesInput::sanitizeName($this->lastname) : $this->lastname,
            'email' => $this->email ? SanitizesInput::sanitizeEmail($this->email) : $this->email,
            'phone' => $this->phone ? SanitizesInput::sanitizePhoneNumber($this->phone) : $this->phone,
        ]);
    }
}
