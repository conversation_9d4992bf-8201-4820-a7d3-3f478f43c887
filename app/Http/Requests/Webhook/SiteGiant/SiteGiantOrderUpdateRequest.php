<?php

namespace App\Http\Requests\Webhook\SiteGiant;

use App\Enums\SiteGiantOrderStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SiteGiantOrderUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $status = request()->token == config('services.sitegiant.webhook_token');

        if (!$status) {
            Log::error('Site Giant webhook', [
                'request_token' => request()->token,
                'token' => config('services.sitegiant.webhook_token'),
                'message' => 'Invalid token',
            ]);
        }

        return $status;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'data' => ['required', 'array'],
            'data.order_id' => ['required', 'integer'],
            'data.marketplace_order_id' => ['nullable', 'string'],
            'data.order_status' => ['required', Rule::in(SiteGiantOrderStatus::getValues())],
            'data.order_time' => ['required', 'date_format:Y-m-d H:i:s'],
            'data.last_modified_date' => ['required', 'date_format:Y-m-d H:i:s'],
            'code' => ['required', 'integer'],
            'timestamp' => ['required', 'integer'],
        ];
    }
}
