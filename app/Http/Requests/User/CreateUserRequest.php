<?php

namespace App\Http\Requests\User;

use App\Helpers\SanitizesInput;
use App\Rules\MediaValidationRule;
use App\Rules\ValidPhoneNumber;
use App\Rules\UniqueGmailAddressRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'firstname' => ['required', 'string', 'max:255'],
            'lastname' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                Rule::unique('users', 'email')->withoutTrashed(),
                'email',
                new UniqueGmailAddressRule('users', 'email')
            ],
            'phone' => ['nullable', Rule::unique('users', 'phone')->withoutTrashed(), new ValidPhoneNumber],
            'password' => ['required', 'string', 'min:8'],
            'dob' => ['nullable', 'date', 'before:today'],
            'roles' => ['nullable', 'array'],
            'roles.*' => ['string', 'exists:roles,name'],
            'branches' => ['nullable', 'array'],
            'branches.*' => ['integer', 'exists:branch,id'],
            'profile' => ['nullable', 'array'],
            'profile.bio' => ['nullable', 'string', 'max:1000'],
            'profile.contact' => ['nullable', 'string', 'max:255'],
            'profile.emergency_contact' => ['nullable', 'string', 'max:255'],
            'profile.emergency_phone' => ['nullable', 'string', 'max:20'],
            'profile.street_address' => ['nullable', 'string', 'max:500'],
            'profile.city' => ['nullable', 'string', 'max:100'],
            'profile.zip' => ['nullable', 'string', 'max:20'],
            'profile.race' => ['nullable', 'string', 'max:50'],

            // Media validation
            'media' => [
                'nullable',
                'array',
                new MediaValidationRule('avatar')
            ],
        ];

        return $rules;
    }

    public function prepareForValidation()
    {
        $this->merge([
            'firstname' => $this->firstname ? SanitizesInput::sanitizeName($this->firstname) : $this->firstname,
            'lastname' => $this->lastname ? SanitizesInput::sanitizeName($this->lastname) : $this->lastname,
            'email' => $this->email ? SanitizesInput::sanitizeEmail($this->email) : $this->email,
            'phone' => $this->phone ? SanitizesInput::sanitizePhoneNumber($this->phone) : $this->phone,
        ]);
    }
}
