<?php

namespace App\Http\Requests\Instructor;

use App\Enums\Gender;
use App\Helpers\SanitizesInput;
use App\Models\Branch;
use App\Models\Designation;
use App\Models\User;
use App\Rules\MediaValidationRule;
use App\Rules\UniqueGmailAddressRule;
use App\Rules\ValidPhoneNumber;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UpdateInstructorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string',
            'bio' => 'nullable|string',
            'branches' => ['nullable', 'array'],
            'branches.*' => ['integer', Rule::exists(Branch::class, 'id')],
            'priority' => 'nullable|numeric',
            'designation_id' => ['nullable', Rule::exists(Designation::class, 'id')],
            'active' => 'required|boolean',
            'user.id' => ['nullable', 'integer', Rule::exists(User::class, 'id')],
        ];

        if ($this->input('user.email') || $this->input('user.phone')) {
            $rules = array_merge($rules, [
                'user.firstname' => ['required', 'string', 'max:255'],
                'user.lastname' => ['required', 'string', 'max:255'],
                'user.email' => [
                    'nullable',
                    'email',
                    Rule::unique(User::class, 'email')->withoutTrashed(),
                    new UniqueGmailAddressRule('users', 'email'),
                ],
                'user.phone' => [
                    'nullable',
                    Rule::unique(User::class, 'phone')->withoutTrashed(),
                    new ValidPhoneNumber,
                ],
                'user.password' => ['required', 'string'],
                'user.country_code' => ['nullable', 'string', 'max:4'],
                'user.dob' => ['nullable', 'date', 'before:today'],
                'user.gender' => ['nullable', 'string', Rule::in(Gender::getValues())],
                'user.roles' => ['nullable', 'array'],
                'user.roles.*' => ['nullable', 'string', Rule::exists(Role::class, 'name')],
            ]);
        }

        // Media validation
        $rules['media'] = [
            'nullable',
            'array',
            new MediaValidationRule('avatar')
        ];

        return $rules;
    }
}
