<?php

namespace App\Http\Requests\Instructor;

use App\Models\Instructor;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ReorderInstructorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'instructors' => ['required', 'array'],
            'instructors.*.id' => ['required', Rule::exists(Instructor::class, 'id'), 'distinct'],
            'instructors.*.order_column' => ['required', 'integer', 'min:1'],
        ];
    }
}
