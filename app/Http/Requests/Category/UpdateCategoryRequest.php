<?php

namespace App\Http\Requests\Category;

use App\Enums\Permission;
use App\Enums\ProductType;
use App\Helpers\SanitizesInput;
use App\Rules\MediaValidationRule;
use BenSampo\Enum\Rules\EnumValue;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $categoryId = $this->route('category')->id;

        return [
            'name' => ['required', 'string', 'max:255'],
            'parent_id' => [
                'nullable',
                'exists:categories,id',
                function ($attribute, $value, $fail) use ($categoryId) {
                    if ($value == $categoryId) {
                        $fail('A category cannot be its own parent.');
                    }
                }
            ],
            'type' => ['required', 'string',  new EnumValue(ProductType::class)],
            'is_active' => ['boolean'],
            'meta' => ['nullable', 'array'],
            'order_column' => ['nullable', 'integer', 'min:0'],

            // Media validation
            'media' => [
                'nullable',
                'array',
                new MediaValidationRule('image')
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'parent_id.exists' => 'The selected parent category does not exist.',
        ];
    }
}
