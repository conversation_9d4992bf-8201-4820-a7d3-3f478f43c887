<?php

namespace App\Http\Requests\Branch;

use App\Helpers\SanitizesInput;
use App\Rules\MediaValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateBranchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'image' => ['nullable', 'string'],
            'geolocation' => ['nullable', 'array'],
            'geolocation.lat' => ['nullable', 'numeric', 'between:-90,90'],
            'geolocation.lng' => ['nullable', 'numeric', 'between:-180,180'],
            'geolocation.address' => ['nullable', 'string', 'max:500'],
            'active' => ['nullable', 'boolean'],
            'virtual' => ['nullable', 'boolean'],
            'details' => ['nullable', 'array'],
            'details.company_name' => ['required_with:details', 'string', 'max:255'],
            'details.company_registration_no' => ['nullable', 'string'],
            'details.company_address' => ['nullable', 'string', 'max:500'],
            'details.company_phone' => ['nullable', 'string', 'max:20'],
            'details.company_email' => ['nullable', 'email', 'max:255'],

            // Media validation
            'media' => [
                'nullable',
                'array',
                new MediaValidationRule('image')
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'name' => $this->name ? SanitizesInput::sanitizeName($this->name) : $this->name,
        ]);
    }
}
