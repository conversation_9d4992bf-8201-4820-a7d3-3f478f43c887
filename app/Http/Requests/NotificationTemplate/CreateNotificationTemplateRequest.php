<?php

namespace App\Http\Requests\NotificationTemplate;

use App\Enums\NotificationChannel;
use App\Helpers\SanitizesInput;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateNotificationTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:500'],
            'channel' => ['required', 'string', Rule::in(NotificationChannel::getValues())],
            'locale' => ['required', 'string', 'size:2'],
            'subject' => ['nullable', 'string', 'max:255'],
            'content' => ['required', 'string'],
            'replacements' => ['nullable', 'array'],
            'replacements.*' => ['string', 'max:255'],
            'is_active' => ['boolean'],
        ];
    }
}
