<?php

namespace App\Http\Requests\Product;

use App\Helpers\SanitizesInput;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Warehouse;
use App\Rules\MediaValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $productId = $this->route('product')->id;

        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:10000'],
            'price' => ['nullable', 'numeric', 'min:0'],
            'sale_price' => ['nullable', 'numeric', 'min:0', 'lt:price'],
            'sku' => ['nullable', 'string', 'max:255', Rule::unique('products', 'sku')->ignore($productId)],
            'barcode' => ['nullable', 'string', 'max:255'],
            'is_taxable' => ['boolean'],
            'tax_class_id' => ['nullable', 'integer'],
            'shipping_class_id' => ['nullable', 'integer'],
            'meta' => ['nullable', 'array'],
            'status' => ['string', 'in:publish,draft,inactive'],
            'unit' => ['nullable', 'string', 'max:50'],
            'height' => ['nullable', 'numeric', 'min:0'],
            'width' => ['nullable', 'numeric', 'min:0'],
            'length' => ['nullable', 'numeric', 'min:0'],
            'weight' => ['nullable', 'numeric', 'min:0'],
            'variants' => ['nullable', 'array'],
            'variants.*.id' => ['nullable', 'integer', Rule::exists(ProductVariant::class, 'id')],
            'variants.*.title' => ['required_with:variants', 'string', 'max:255'],
            'variants.*.price' => ['required_with:variants', 'numeric'],
            'variants.*.sale_price' => ['nullable', 'string'],
            'variants.*.sku' => ['nullable', 'string', 'max:255'],
            'variants.*.barcode' => ['nullable', 'string', 'max:255'],
            'variants.*.is_active' => ['boolean'],
            'variants.*.width' => ['nullable', 'numeric', 'min:0'],
            'variants.*.height' => ['nullable', 'numeric', 'min:0'],
            'variants.*.length' => ['nullable', 'numeric', 'min:0'],
            'variants.*.weight' => ['nullable', 'numeric', 'min:0'],
            'delete_variants' => ['nullable', 'array'],
            'delete_variants.*' => ['integer', 'exists:product_variants,id'],
            'variants.*.gallery' => ['nullable', 'array'],
            'variants.*.gallery.*' => ['string', 'url'],
            'variant_attributes' => ['nullable', 'array'],

            // Bundle-specific fields
            'is_bundle' => ['boolean'],
            'is_require_double_scanning' => ['boolean'],
            'bundle_items' => ['nullable', 'array'],
            'bundle_items.*.item_product_id' => ['required', 'integer', Rule::exists(Product::class, 'id')],
            'bundle_items.*.quantity' => ['nullable', 'integer', 'min:1'],

            'warehouses' => ['nullable', 'array'],
            'warehouses.*.id' => ['nullable', 'integer', Rule::exists(Warehouse::class, 'id')],
            'warehouses.*.label' => ['nullable', 'string', 'max:255'],

            // Media validation
            'media' => [
                'nullable',
                'array',
                new MediaValidationRule([
                    'image' => [],
                    'gallery' => [
                        'type' => 'multiple',
                        'mime_types' => ['image', 'video'],
                    ],
                ]),
            ],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'name' => $this->name ? SanitizesInput::sanitizeName($this->name) : $this->name,
            'slug' => $this->slug ? trim($this->slug) : $this->slug,
            'sku' => $this->sku ? (trim($this->sku)) : $this->sku,
            'barcode' => $this->barcode ? trim($this->barcode) : $this->barcode,
        ]);
    }
}
