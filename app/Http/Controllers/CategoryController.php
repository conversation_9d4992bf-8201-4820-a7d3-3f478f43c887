<?php

namespace App\Http\Controllers;

use App\Http\Requests\Category\CreateCategoryRequest;
use App\Http\Requests\Category\UpdateCategoryRequest;

use App\Http\Resources\ApiResponse;
use App\Models\Category;
use App\Services\CategoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CategoryController extends Controller
{
    public function __construct(protected CategoryService $categoryService) {}

    /**
     * Store a newly created category.
     *
     * @param CreateCategoryRequest $request
     * @return JsonResponse
     */
    public function store(CreateCategoryRequest $request): JsonResponse
    {
        $input = $request->validated();

        $category = DB::transaction(function () use ($input) {
            return $this->categoryService
                ->store($input)
                ->syncMedia($input['media'] ?? [])
                ->getModel();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($category)
            ->getResponse();
    }

    /**
     * Update the specified category.
     *
     * @param UpdateCategoryRequest $request
     * @param Category $category
     * @return JsonResponse
     */
    public function update(UpdateCategoryRequest $request, Category $category): JsonResponse
    {
        $input = $request->validated();

        $category = DB::transaction(function () use ($input, $category) {
            return $this->categoryService
                ->setModel($category)
                ->update($input)
                ->syncMedia($input['media'] ?? [])
                ->getModel();
        });


        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($category)
            ->getResponse();
    }

    /**
     * Remove the specified category.
     *
     * @param Category $category
     * @return JsonResponse
     */
    public function destroy(Category $category): JsonResponse
    {
        $this->categoryService->setModel($category)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
