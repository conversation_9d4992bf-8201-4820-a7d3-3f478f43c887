<?php

namespace App\Http\Controllers;

use App\Http\Requests\PresignedUrlRequest;
use App\Http\Resources\ApiResponse;
use App\Services\UploadService;
use Illuminate\Http\JsonResponse;

class UploadController extends Controller
{
    public function __construct(protected UploadService $uploadService)
    {
    }

    /**
     * Generate S3 presigned URL for direct upload
     *
     * @param PresignedUrlRequest $request
     * @return JsonResponse
     */
    public function generatePresignedUrl(PresignedUrlRequest $request): JsonResponse
    {
        $input = $request->validated();

        // Handle multiple files (array only)
        $presigned_urls = [];

        foreach ($input['media'] as $filename) {
            $presigned_urls[] = $this->uploadService
                ->setFileName($filename)
                ->generatePresignedUrl();
        }

        return (new ApiResponse())
            ->setData($presigned_urls)
            ->getResponse();
    }
}
