<?php

namespace App\Http\Controllers;

use App\Http\Requests\Setting\SaveSettingRequest;
use App\Http\Resources\ApiResponse;
use App\Services\SettingService;
use Illuminate\Http\JsonResponse;

class SettingController extends Controller
{
    public function __construct(protected SettingService $settingService)
    {

    }

    public function index(): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($this->settingService->all())
            ->getResponse();
    }

    public function save(SaveSettingRequest $request): JsonResponse
    {
        $input = $request->validated();

        $settings = $this->settingService
            ->updateOrCreate(['type' => $input['type']], [
                'options' => $input['options'],
            ])
            ->cache()
            ->all();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($settings)
            ->getResponse();
    }
}
