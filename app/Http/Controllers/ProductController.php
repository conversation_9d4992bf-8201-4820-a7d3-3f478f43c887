<?php

namespace App\Http\Controllers;

use App\Http\Requests\Product\CreateProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class ProductController extends Controller
{
    public function __construct(protected ProductService $productService) {}

    /**
     * Store a newly created product.
     *
     * @param CreateProductRequest $request
     * @return JsonResponse
     */
    public function store(CreateProductRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (!Arr::get($input, 'is_bundle')) {
            $input['bundle_items'] = [];
        }

        $product = $this->productService
            ->store($input)
            ->syncVariants($input['variants'] ?? [])
            ->syncBundleItems(Arr::get($input, 'bundle_items', []))
            ->syncWarehouses($input['warehouses'] ?? [])
            ->syncMedia($input['media'] ?? [])
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($product)
            ->getResponse();
    }

    /**
     * Update the specified product.
     *
     * @param UpdateProductRequest $request
     * @param Product $product
     * @return JsonResponse
     */
    public function update(UpdateProductRequest $request, Product $product): JsonResponse
    {
        $input = $request->validated();

        if (!Arr::get($input, 'is_bundle')) {
            $input['bundle_items'] = [];
        }

        $this->productService
            ->setModel($product)
            ->update($input)
            ->syncVariants($input['variants'] ?? [])
            ->syncWarehouses($input['warehouses'] ?? [])
            ->syncBundleItems(Arr::get($input, 'bundle_items', []))
            ->syncMedia($input['media'] ?? []);

        $product = $this->productService->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($product)
            ->getResponse();
    }



    /**
     * Remove the specified product.
     *
     * @param Product $product
     * @return JsonResponse
     */
    public function destroy(Product $product): JsonResponse
    {
        $this->productService->setModel($product)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
