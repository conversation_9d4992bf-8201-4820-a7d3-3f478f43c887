<?php

namespace App\Http\Controllers;

use App\Http\Requests\Instructor\CreateInstructorRequest;
use App\Http\Requests\Instructor\ReorderInstructorRequest;
use App\Http\Requests\Instructor\UpdateInstructorRequest;

use App\Http\Resources\ApiResponse;
use App\Models\Instructor;
use App\Services\InstructorService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class InstructorController extends Controller
{
    public function __construct(
        protected InstructorService $instructorService
    ) {}

    public function store(CreateInstructorRequest $request): JsonResponse
    {
        $input = $request->validated();

        $branches = $input['branches'] ?? [];

        $service = DB::transaction(function () use ($input, $branches) {
            $service = $this->instructorService
                ->store($input)
                ->syncBranches($branches);

            if (!empty($input['user'])) {
                $input['user']['branches'] = $branches;

                $service->syncUser($input['user']);
            }

            return $service->syncMedia($input['media'] ?? []);
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($service->getModel())
            ->getResponse();
    }

    public function update(UpdateInstructorRequest $request, Instructor $instructor): JsonResponse
    {
        $input = $request->validated();

        $branches = $input['branches'] ?? [];

        $service = DB::transaction(function () use ($input, $branches, $instructor) {
            $service = $this->instructorService
                ->setModel($instructor)
                ->update($input)
                ->syncBranches($branches);

            if (!empty($input['user'])) {
                $input['user']['branches'] = $branches;

                $service->syncUser($input['user']);
            }

            return $service->syncMedia($input['media'] ?? []);
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($service->getModel())
            ->getResponse();
    }



    public function destroy(Instructor $instructor): JsonResponse
    {
        $this->instructorService->setModel($instructor)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }

    public function reorder(ReorderInstructorRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->instructorService->reorder($input['instructors']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
