<?php

namespace App\Http\Controllers;

use App\Http\Requests\Role\CreateRoleRequest;
use App\Http\Requests\Role\UpdateRoleRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Role;
use App\Services\RoleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class RoleController extends Controller
{
    public function __construct(protected RoleService $roleService)
    {
    }

    /**
     * Store a newly created role in storage.
     *
     * @param CreateRoleRequest $request
     * @return JsonResponse
     */
    public function store(CreateRoleRequest $request): JsonResponse
    {
        $input = $request->validated();

        $role = DB::transaction(function () use ($input) {
            return $this->roleService
                ->store($input)
                ->syncPermissions($input['permissions'] ?? [])
                ->syncUsers($input['user_ids'] ?? [])
                ->getModel();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($role)
            ->getResponse();
    }

    /**
     * Update the specified role in storage.
     *
     * @param UpdateRoleRequest $request
     * @param Role $role
     * @return JsonResponse
     */
    public function update(UpdateRoleRequest $request, Role $role): JsonResponse
    {
        $input = $request->validated();

        $role = DB::transaction(function () use ($input, $role) {
            return $this->roleService
                ->setModel($role)
                ->update($input)
                ->syncPermissions($input['permissions'] ?? [])
                ->syncUsers($input['user_ids'] ?? [])
                ->getModel();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($role)
            ->getResponse();
    }

    /**
     * Remove the specified role from storage.
     *
     * @param Role $role
     * @return JsonResponse
     */
    public function destroy(Role $role): JsonResponse
    {
        DB::transaction(function () use ($role) {
            $this->roleService->setModel($role)
                ->syncUsers([])
                ->syncPermissions([])
                ->delete();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
