<?php

namespace App\Http\Controllers;

use App\Enums\Role;
use App\Http\Requests\Auth\AdminLoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Resources\ApiResponse;
use App\Services\AuthService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AuthController extends Controller
{
    public function __construct(
        protected AuthService $authService,
        protected UserService $userService,
    ) {}

    public function login(AdminLoginRequest $request): JsonResponse
    {
        $input = $request->validated();
        $data = $this->authService->setCredentials($input)
            ->setRequiredRoles([])
            ->validateCredentials()
            ->checkRolePermissions()
            ->generateToken()
            ->getTokenData();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($data)
            ->getResponse();
    }

    /**
     * Register a new user with customer role
     *
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        $input = $request->validated();

        // Create user with customer role using existing UserService methods
        $user = $this->userService
            ->store($input)
            ->syncRoles([Role::CUSTOMER])
            ->sendWelcomeNotification()
            ->getModel();

        // Generate token using existing AuthService methods
        $data = $this->authService
            ->setUser($user)
            ->generateToken()
            ->getTokenData();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($data)
            ->getResponse();
    }

    public function logout(): JsonResponse
    {
        auth()->user()->currentAccessToken()->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
