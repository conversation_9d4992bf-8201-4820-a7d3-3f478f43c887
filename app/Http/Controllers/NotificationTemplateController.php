<?php

namespace App\Http\Controllers;

use App\Http\Requests\NotificationTemplate\CreateNotificationTemplateRequest;
use App\Http\Requests\NotificationTemplate\UpdateNotificationTemplateRequest;
use App\Models\NotificationTemplate;
use Illuminate\Http\JsonResponse;
use App\Http\Resources\ApiResponse;
use App\Services\NotificationTemplateService;

class NotificationTemplateController extends Controller
{
    public function __construct(protected NotificationTemplateService $notificationTemplateService) {}

    /**
     * Store a newly created notification template.
     */
    public function store(CreateNotificationTemplateRequest $request): JsonResponse
    {
        $input = $request->validated();
        $notificationTemplate = $this->notificationTemplateService->store($input)->getModel();
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($notificationTemplate)
            ->getResponse();
    }



    /**
     * Update the specified notification template.
     */
    public function update(UpdateNotificationTemplateRequest $request, NotificationTemplate $notification_template): JsonResponse
    {
        $input = $request->validated();
        $notification_template = $this->notificationTemplateService
            ->setModel($notification_template)
            ->update($input)
            ->getModel();
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($notification_template)
            ->getResponse();
    }

    /**
     * Remove the specified notification template.
     */
    public function destroy(NotificationTemplate $notification_template): JsonResponse
    {
        $this->notificationTemplateService->setModel($notification_template)->delete();
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
