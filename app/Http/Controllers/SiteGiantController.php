<?php

namespace App\Http\Controllers;

use App\Enums\SiteGiantOrderStatus;
use App\Http\Requests\Webhook\SiteGiant\SiteGiantOrderUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Jobs\ProcessSiteGiantOrders;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SiteGiantController extends Controller
{
    public function orderUpdate(SiteGiantOrderUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        Log::info('Site Giant webhook', $input);

        if ($input['data']['order_status'] == SiteGiantOrderStatus::UNPAID) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->getResponse();
        }

        ProcessSiteGiantOrders::dispatch([
            [
                'order_id' => $input['data']['order_id'],
                'order_status' => $input['data']['order_status'],
            ],
        ]);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
