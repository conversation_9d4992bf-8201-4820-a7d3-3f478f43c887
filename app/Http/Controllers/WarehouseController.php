<?php

namespace App\Http\Controllers;

use App\Http\Requests\Warehouse\CreateWarehouseRequest;
use App\Http\Requests\Warehouse\UpdateWarehouseRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Warehouse;
use App\Services\WarehouseService;
use Illuminate\Http\JsonResponse;

class WarehouseController extends Controller
{
    public function __construct(protected WarehouseService $warehouseService) {}

    /**
     * Store a newly created warehouse.
     *
     * @param CreateWarehouseRequest $request
     * @return JsonResponse
     */
    public function store(CreateWarehouseRequest $request): JsonResponse
    {
        $input = $request->validated();

        $warehouse = $this->warehouseService
            ->store($input)
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($warehouse)
            ->getResponse();
    }

    /**
     * Update the specified warehouse.
     *
     * @param UpdateWarehouseRequest $request
     * @param Warehouse $warehouse
     * @return JsonResponse
     */
    public function update(UpdateWarehouseRequest $request, Warehouse $warehouse): JsonResponse
    {
        $input = $request->validated();

        $warehouse = $this->warehouseService
            ->setModel($warehouse)
            ->update($input)
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($warehouse)
            ->getResponse();
    }

    /**
     * Remove the specified warehouse.
     *
     * @param Warehouse $warehouse
     * @return JsonResponse
     */
    public function destroy(Warehouse $warehouse): JsonResponse
    {
        $this->warehouseService->setModel($warehouse)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}

