<?php

namespace App\Http\Controllers;

use App\Http\Requests\Branch\CreateBranchRequest;
use App\Http\Requests\Branch\UpdateBranchRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Branch;
use App\Services\BranchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class BranchController extends Controller
{
    public function __construct(protected BranchService $branchService) {}

    /**
     * Store a newly created branch.
     *
     * @param CreateBranchRequest $request
     * @return JsonResponse
     */
    public function store(CreateBranchRequest $request): JsonResponse
    {
        $input = $request->validated();

        $branch = DB::transaction(function () use ($input) {
            return $this->branchService
                ->store($input)
                ->syncDetails($input['details'] ?? [])
                ->syncMedia($input['media'] ?? [])
                ->getModel();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($branch)
            ->getResponse();
    }

    /**
     * Update the specified branch.
     *
     * @param UpdateBranchRequest $request
     * @param Branch $branch
     * @return JsonResponse
     */
    public function update(UpdateBranchRequest $request, Branch $branch): JsonResponse
    {
        $input = $request->validated();

        $branch = DB::transaction(function () use ($input, $branch) {
            return $this->branchService
                ->setModel($branch)
                ->update($input)
                ->syncDetails($input['details'] ?? [])
                ->syncMedia($input['media'] ?? [])
                ->getModel();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($branch)
            ->getResponse();
    }



    /**
     * Remove the specified branch.
     *
     * @param Branch $branch
     * @return JsonResponse
     */
    public function destroy(Branch $branch): JsonResponse
    {
        $this->branchService->setModel($branch)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
