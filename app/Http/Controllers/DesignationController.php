<?php

namespace App\Http\Controllers;

use App\Http\Requests\Designation\CreateDesignationRequest;
use App\Http\Requests\Designation\UpdateDesignationRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Designation;
use App\Services\DesignationService;
use Illuminate\Http\JsonResponse;

class DesignationController extends Controller
{
    public function __construct(protected DesignationService $designationService)
    {
    }

    /**
     * Store a newly created designation.
     *
     * @param CreateDesignationRequest $request
     * @return JsonResponse
     */
    public function store(CreateDesignationRequest $request): JsonResponse
    {
        $input = $request->validated();

        $designation = $this->designationService
            ->store($input)
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($designation)
            ->getResponse();
    }

    /**
     * Update the specified designation.
     *
     * @param UpdateDesignationRequest $request
     * @param Designation $designation
     * @return JsonResponse
     */
    public function update(UpdateDesignationRequest $request, Designation $designation): JsonResponse
    {
        $input = $request->validated();

        $designation = $this->designationService
            ->setModel($designation)
            ->update($input)
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($designation)
            ->getResponse();
    }

    /**
     * Remove the specified designation.
     *
     * @param Designation $designation
     * @return JsonResponse
     */
    public function destroy(Designation $designation): JsonResponse
    {
        $this->designationService->setModel($designation)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
