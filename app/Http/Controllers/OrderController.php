<?php

namespace App\Http\Controllers;

use App\Enums\OrderStatus;
use App\Http\Requests\Order\CreateOrderRequest;
use App\Http\Requests\Order\LinkProductRequest;
use App\Http\Requests\Order\UpdateOrderRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Order;
use App\Services\OrderService;
use Illuminate\Http\JsonResponse;

class OrderController extends Controller
{
    public function __construct(protected OrderService $orderService)
    {
    }

    /**
     * Store a newly created order.
     *
     * @param CreateOrderRequest $request
     * @return JsonResponse
     */
    public function store(CreateOrderRequest $request): JsonResponse
    {
        $input = $request->validated();

        $order = $this->orderService
            ->store($input)
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($order)
            ->getResponse();
    }

    /**
     * Update the specified order.
     *
     * @param UpdateOrderRequest $request
     * @param Order $order
     * @return JsonResponse
     */
    public function update(UpdateOrderRequest $request, Order $order): JsonResponse
    {
        $input = $request->validated();

        $updatedOrder = $this->orderService
            ->setModel($order)
            ->update($input)
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($updatedOrder)
            ->getResponse();
    }

    /**
     * Remove the specified order from storage.
     *
     * @param Order $order
     * @return JsonResponse
     */
    public function destroy(Order $order): JsonResponse
    {
        $this->orderService
            ->setModel($order)
            ->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }

    public function linkProducts(LinkProductRequest $request, Order $order): JsonResponse
    {
        $input = $request->validated();

        $this->orderService
            ->setModel($order)
            ->validateOrderProducts($input['order_products'])
            ->linkProducts($input['order_products'])
            ->updateStatus(OrderStatus::PACKED)
            ->createAutoCountInvoice();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
