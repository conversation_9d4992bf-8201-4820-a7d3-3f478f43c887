<?php

namespace App\Channels;

use App\Factories\NotificationAdapterFactory;
use App\Enums\NotificationChannel;
use Illuminate\Notifications\Notification;

class SmsChannel
{
    /**
     * Send the given notification.
     */
    public function send($notifiable, Notification $notification)
    {
        // Get the SMS adapter using the existing factory pattern
        $adapter = NotificationAdapterFactory::getAdapterFor(NotificationChannel::SMS);

        // Get the SMS message from the notification
        if (!method_exists($notification, 'toSms')) {
            return false; // Don't send if method doesn't exist
        }

        $message = $notification->toSms($notifiable);

        // Get the recipient phone number
        $recipient = $notifiable->routeNotificationForPhone($notification);

        if (!$recipient) {
            return false;
        }

        // Send via adapter
        return $adapter->send($recipient, $message);
    }
}
