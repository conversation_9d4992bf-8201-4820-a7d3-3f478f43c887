<?php

namespace App\Channels;

use App\Factories\NotificationAdapterFactory;
use App\Enums\NotificationChannel;
use Illuminate\Notifications\Notification;

class WhatsAppChannel
{
    /**
     * Send the given notification.
     */
    public function send($notifiable, Notification $notification)
    {
        // Get the WhatsApp adapter using the existing factory pattern
        $adapter = NotificationAdapterFactory::getAdapterFor(NotificationChannel::WHATSAPP);

        // Get the WhatsApp message from the notification
        if (!method_exists($notification, 'toWhatsApp')) {
            return false; // Don't send if method doesn't exist
        }

        $message = $notification->toWhatsApp($notifiable);

        // Get the recipient phone number
        $recipient = $notifiable->routeNotificationForPhone($notification);

        if (!$recipient) {
            return false;
        }

        // Send via adapter
        return $adapter->send($recipient, $message);
    }
}
