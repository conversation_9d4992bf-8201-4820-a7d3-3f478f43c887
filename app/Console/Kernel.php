<?php

namespace App\Console;

use App\Console\Commands\SyncAutoCountProducts;
use App\Console\Commands\SyncSiteGiantBranches;
use App\Console\Commands\SyncSiteGiantPaymentMethods;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();

        $schedule->command(SyncAutoCountProducts::class)->dailyAt('00:00');
        $schedule->command(SyncSiteGiantBranches::class)->dailyAt('00:00');
        $schedule->command(SyncSiteGiantPaymentMethods::class)->dailyAt('00:10');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
