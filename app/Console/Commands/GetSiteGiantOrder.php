<?php

namespace App\Console\Commands;

use App\Enums\EcommerceProvider;
use App\Factories\EcommerceProviderAdapterFactory;
use App\Services\OrderService;
use Illuminate\Console\Command;

class GetSiteGiantOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitegiant:get-order {order-id : The ID of the order}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get sitegiant order, for debug purpose';

    /**
     * Execute the console command.
     */
    public function handle(OrderService $order_service)
    {
        $adapter = EcommerceProviderAdapterFactory::getAdapterFor(EcommerceProvider::SITE_GIANT);

        $order_id = $this->argument('order-id');
        $order = $order_service->findVia($order_id);

        if (!$order) {
            $this->error('Order not found');
            return;
        }

        $sitegiant_order_id = explode('_', $order->display_id);
        $response = $adapter
            ->setOrderId($sitegiant_order_id[1])
            ->getOrder();

        dd($response);
    }
}
