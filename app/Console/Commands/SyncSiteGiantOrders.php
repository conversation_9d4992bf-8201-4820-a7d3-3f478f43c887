<?php

namespace App\Console\Commands;

use App\Enums\EcommerceProvider;
use App\Enums\SiteGiantOrderStatus;
use App\Factories\EcommerceProviderAdapterFactory;
use App\Jobs\ProcessSiteGiantOrders;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class SyncSiteGiantOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitegiant:sync-orders
                            {--from-date= : Order start date (YYYY-MM-DD)}
                            {--to-date= : Order end date (YYYY-MM-DD)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $adapter = EcommerceProviderAdapterFactory::getAdapterFor(EcommerceProvider::SITE_GIANT);

        $page = 1;

        $from_date = $this->option('from-date') ? new Carbon($this->option('from-date')) : now();
        $to_date = $this->option('to-date') ? new Carbon($this->option('to-date')) : now();

        do {
            $response = $adapter
                ->setOrderPage($page)
                ->setOrderPageSize(100)
                ->setOrderDateFrom($from_date)
                ->setOrderDateTo($to_date)
                ->setOrderStatuses([
                    SiteGiantOrderStatus::COMPLETED,
                    SiteGiantOrderStatus::READY_TO_PICKUP,
                    SiteGiantOrderStatus::PAID,
                    SiteGiantOrderStatus::PROCESSED,
                    SiteGiantOrderStatus::READY_TO_SHIP,
                    SiteGiantOrderStatus::SHIPPED,
                ])
                ->getOrders();

            ProcessSiteGiantOrders::dispatch($response['order_list']);

            $page++;
        } while ($response['more'] == true);
    }
}
