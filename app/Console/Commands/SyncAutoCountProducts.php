<?php

namespace App\Console\Commands;

use App\Enums\AccountingSoftwareProvider;
use App\Enums\AutoCountProductStatus;
use App\Enums\SettingType;
use App\Factories\AccountingSoftwareAdapterFactory;
use App\Jobs\ProcessAutoCountProduct;
use App\Services\SettingService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;

class SyncAutoCountProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autocount:sync-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync products from AutoCount API to local database';


    public function __construct(protected SettingService $settingService)
    {
        parent::__construct();
    }

    public function handle()
    {
        $adapter = AccountingSoftwareAdapterFactory::getAdapterFor(
            AccountingSoftwareProvider::AUTO_COUNT
        );

        $accounts = $this->settingService->findVia(SettingType::AUTOCOUNT, 'type');

        if (!Arr::get($accounts, 'options.accounts')) {
            $this->info('No Autocount accounts setup.');
            return;
        }

        foreach ($accounts->options['accounts'] as $account) {
            $page = 1;

            do {
                $response = $adapter
                    ->setConfig($account)
                    ->setPage($page)
                    ->setProductStatuses([
                        AutoCountProductStatus::ACTIVE,
                    ])
                    ->getProducts();

                if (count($response['data']) > 0) {
                    ProcessAutoCountProduct::dispatch($response['data']);
                }

                $this->info('Page ' . $page . ' of ' . count($response['data']) . ' products synced.');
                $page++;
            } while (count($response['data']) > 0);
        }

    }
}
