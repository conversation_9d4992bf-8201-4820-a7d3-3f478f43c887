<?php

namespace App\Console\Commands;

use App\Enums\EcommerceProvider;
use App\Factories\EcommerceProviderAdapterFactory;
use App\Services\BranchService;
use Illuminate\Console\Command;

class SyncSiteGiantBranches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitegiant:sync-branches';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync SiteGiant Branches';


    public function __construct(protected BranchService $branchService)
    {
        parent::__construct();

    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $adapter = EcommerceProviderAdapterFactory::getAdapterFor(EcommerceProvider::SITE_GIANT);
        $branches = $adapter->getBranches();

        foreach ($branches as $branch) {
            $this->branchService->updateOrCreate([
                'code' => 'sitegiant_' . $branch['id'],
            ],[
                'name' => $branch['name'],
            ]);

            $this->info('Branch ' . $branch['name'] . ' synced.');
        }
    }
}
