<?php

namespace App\Console\Commands;

use App\Enums\Role;
use App\Models\User;
use Database\Seeders\RolePermissionSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class PermissionSeed extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permission:seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed user roles and permissions';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔐 Permission Seed Command');
        $this->info('This command provides interactive seeding of roles, permissions, and users.');

        if ($this->confirm('Do you want to seed user roles and permissions?')) {
            $this->info('Running RolePermissionSeeder...');

            // Use the seeder class for consistency
            $seeder = new RolePermissionSeeder();
            $seeder->setCommand($this);
            $seeder->run();
        }

        if ($this->confirm('Do you want to create a custom super admin user? (Note: A mock admin is already created by the seeder)')) {
            $this->createSuperAdminUser();
        }

        $this->info('✅ Permission seeding completed!');
        return 0;
    }



    /**
     * Create super admin user
     *
     * @return void
     */
    protected function createSuperAdminUser()
    {
        $this->info('Provide admin credentials info to create a super admin user for you.');
        $firstname = $this->ask('Enter admin first name');
        $lastname = $this->ask('Enter admin last name');
        $email = $this->ask('Enter admin email');
        $password = $this->secret('Enter your admin password');
        $confirmPassword = $this->secret('Enter your password again');

        $this->info('Please wait, Creating a super admin profile for you...');
        $validator = Validator::make(
            [
                'firstname' => $firstname,
                'lastname' => $lastname,
                'email' => $email,
                'password' => $password,
                'confirmPassword' => $confirmPassword,
            ],
            [
                'firstname' => 'required|string',
                'lastname' => 'required|string',
                'email' => 'required|email|unique:users,email',
                'password' => 'required',
                'confirmPassword' => 'required|same:password',
            ]
        );

        if ($validator->fails()) {
            $this->info('User not created. See error messages below:');
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return;
        }

        $user = User::create([
            'firstname' => $firstname,
            'lastname' => $lastname,
            'email' => $email,
            'password' => Hash::make($password),
            'is_active' => true,
        ]);

        $user->assignRole(Role::SUPER_ADMIN);
        $this->info('Super Admin User Creation Successful!');
    }
}
