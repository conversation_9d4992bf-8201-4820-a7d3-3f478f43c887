<?php

namespace App\Console\Commands;

use App\Factories\PaymentGatewayAdapterFactory;
use Illuminate\Console\Command;

class unionpay_test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:unionpay_test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //  Run unionpay adapter fund inquiry
        $adapter =  PaymentGatewayAdapterFactory::getAdapterFor('unionpay');
        // $response = $adapter->fundInquiry('156');

        $response = $adapter->keyExchange([
            'signPublicKey' => $adapter->upiSigningCertData,
            'encPublicKey' => $adapter->upiEncryptionCertData
        ]);

        dd($response);
    }
}
