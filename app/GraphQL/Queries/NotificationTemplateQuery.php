<?php

namespace App\GraphQL\Queries;

use App\Repositories\NotificationTemplateRepository;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class NotificationTemplateQuery
{
    public function __construct(protected NotificationTemplateRepository $notificationTemplateRepository) {}

    /**
     * Retrieve notification templates with custom logic if needed
     *
     * @param mixed $rootValue
     * @param array $args
     * @param GraphQLContext $context
     * @param ResolveInfo $resolveInfo
     * @return mixed
     */
    public function index($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        // Custom logic for fetching notification templates if needed
        // For now, return basic repository query - Lighthouse handles pagination automatically
        return $this->notificationTemplateRepository->with([])
            ->orderBy('created_at', 'desc');
    }
}
