<?php

namespace App\GraphQL\Queries;

use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Auth;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class UserQuery
{


    public function __construct(protected UserRepository $userRepository) {}

    /**
     * Get the authenticated user
     *
     * @param mixed $rootValue
     * @param array $args
     * @param GraphQLContext $context
     * @param ResolveInfo $resolveInfo
     * @return mixed
     */
    public function me($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return auth()->user();
    }
}
