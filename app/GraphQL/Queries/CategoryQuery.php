<?php

namespace App\GraphQL\Queries;

use App\Models\Category;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class CategoryQuery
{
    /**
     * Get the full path of the category (including parent names).
     */
    public function fullPath($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $rootValue->getFullPathAttribute();
    }

    /**
     * Check if the category has children.
     */
    public function hasChildren($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $rootValue->hasChildren();
    }

    /**
     * Check if the category is a root category.
     */
    public function isRoot($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $rootValue->parent_id === null;
    }

    /**
     * Get the ancestors of the category.
     */
    public function ancestors($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $rootValue->ancestors()->get();
    }

    /**
     * Get the descendants of the category.
     */
    public function descendants($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $rootValue->descendants()->get();
    }

    /**
     * Get the siblings of the category.
     */
    public function siblings($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $rootValue->siblings()->get();
    }
}
