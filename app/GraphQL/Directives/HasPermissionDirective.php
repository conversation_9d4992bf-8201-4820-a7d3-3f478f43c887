<?php

namespace App\GraphQL\Directives;

use App\Enums\Role;
use Closure;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Auth\Access\AuthorizationException;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;

class HasPermissionDirective extends BaseDirective implements FieldMiddleware
{
    public static function definition(): string
    {
        return
            /** @lang GraphQL */
            '
            """
            Check if user has specific permission
            """
            directive @hasPermission(
                permission: String!
            ) on FIELD_DEFINITION
        ';
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(fn (callable $resolver) => function (mixed $root, array $args, GraphQLContext $context, ResolveInfo $resolveInfo) use ($resolver) {
            // Do something before the resolver, e.g. validate $args, check authentication

            // Call the actual resolver
            $result = $resolver($root, $args, $context, $resolveInfo);

            // Do something with the result, e.g. transform some fields
            $permission = $this->directiveArgValue('permission');
            $this->authorizeUser($permission);

            return $result;
        });
    }

    private function authorizeUser(string $permission): void
    {
        $user = auth()->user();

        if ($user?->hasRole(Role::SUPER_ADMIN)) {
            return;
        }

        if (!$user || !$user->hasPermissionTo($permission)) {
            throw new AuthorizationException();
        }
    }
}
