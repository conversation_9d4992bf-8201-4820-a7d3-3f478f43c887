<?php

namespace App\GraphQL\Directives;

use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class MediaUrlDirective extends BaseDirective implements FieldMiddleware
{
    public static function definition(): string
    {
        return
            /** @lang GraphQL */
            '
        """
        Get media URL(s) from a collection with N+1 prevention.
        """
        directive @mediaUrl(
            """
            The media collection name.
            """
            collection: String! = "default"

            """
            The conversion name (optional).
            """
            conversion: String

            """
            Return all media URLs instead of just the first one.
            """
            multiple: Boolean = false

            """
            Cache the result for this many minutes.
            """
            cache: Int
        ) on FIELD_DEFINITION
        ';
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(fn(callable $resolver) => function (mixed $root, array $args, GraphQLContext $context, ResolveInfo $resolveInfo) use ($resolver) {
            // Do something before the resolver, e.g. validate $args, check authentication
            $collection = $this->directiveArgValue('collection', 'default');
            $conversion = $this->directiveArgValue('conversion');
            $multiple = $this->directiveArgValue('multiple', false);
            // $cacheMinutes = $this->directiveArgValue('cache');

            // Check if the model uses HasMedia trait
            if (!method_exists($root, 'getFirstMediaUrl')) {
                return null;
            }

            // Call the actual resolver
            $result = $resolver($root, $args, $context, $resolveInfo);

            if (!$root->relationLoaded('media')) {
                $root->load(['media' => function ($query) use ($collection) {
                    $query->where('collection_name', $collection);
                }]);

                return $conversion
                    ? $root->getFirstMediaUrl($collection, $conversion)
                    : $root->getFirstMediaUrl($collection);
            }

            $root->getMedia($collection);
            $root->load(['media' => function ($query) use ($collection) {
                $query->where('collection_name', $collection);
            }]);
            return $root->media->map(function ($media) use ($conversion) {
                return $conversion ? $media->getUrl($conversion) : $media->getUrl();
            })->toArray();
        });
    }
}
