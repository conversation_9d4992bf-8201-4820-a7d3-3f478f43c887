<?php

namespace App\Exceptions;

use App\Http\Resources\ApiResponse;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\Exceptions\MissingAbilityException;
use Spatie\Permission\Exceptions\UnauthorizedException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Determine if the request should return JSON response
     * Always return JSON for API routes
     */
    protected function shouldReturnJson($request, Throwable $e): bool
    {
        // Always return JSON for API routes
        if ($request->is('api/*')) {
            return true;
        }

        // Use parent logic for other routes
        return parent::shouldReturnJson($request, $e);
    }

    public function render($request, Throwable $e): Response|JsonResponse|RedirectResponse|\Symfony\Component\HttpFoundation\Response
    {
        // For non-JSON requests, let parent handle it
        if (!$this->shouldReturnJson($request, $e)) {

            return parent::render($request, $e);
        }

        Log::error($e->getMessage(), ['trace' => $e->getTraceAsString()]);

        if ($e instanceof ValidationException) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->errors())
                ->setHttpCode(422)
                ->setCode((int) $e->getCode() ?: 422)
                ->getResponse();
        }

        if ($e instanceof NotFoundHttpException) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage() ?: '404 NOT FOUND')
                ->setHttpCode(404)
                ->setCode((int) $e->getCode() ?: 404)
                ->getResponse();
        }

        if ($e instanceof ModelNotFoundException) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError('Resource not found')
                ->setHttpCode(404)
                ->setCode((int) $e->getCode() ?: 404)
                ->getResponse();
        }

        if ($e instanceof MissingAbilityException) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError('Forbidden')
                ->setHttpCode(403)
                ->setCode((int) $e->getCode() ?: 403)
                ->getResponse();
        }

        if ($e instanceof AuthenticationException) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($e->getMessage())
                ->setHttpCode(401)
                ->setCode((int) $e->getCode() ?: 401)
                ->getResponse();
        }

        if ($e instanceof UnauthorizedException) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError("Unauthorized. " . $e->getMessage())
                ->setHttpCode(401)
                ->setCode(401)
                ->getResponse();
        }

        // Handle database errors
        if ($e instanceof QueryException) {
            $message = app()->hasDebugModeEnabled()
                ? $e->getMessage()
                : "An unexpected database query error occurred.";

            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($message)
                ->setHttpCode(500)
                ->setCode((int) $e->getCode() ?: 500)
                ->getResponse();
        }

        // Handle runtime errors
        if ($e instanceof \RuntimeException) {
            $message = app()->hasDebugModeEnabled()
                ? $e->getMessage()
                : "An unexpected error occurred.";

            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError($message)
                ->setHttpCode(500)
                ->setCode((int) $e->getCode() ?: 500)
                ->getResponse();
        }

        // Default fallback for all other exceptions
        $message = app()->hasDebugModeEnabled()
            ? $e->getMessage()
            : "An unexpected error occurred.";

        return (new ApiResponse())
            ->setStatus(ApiResponse::STATUS_ERROR)
            ->setMessage('An unexpected error occurred.')
            ->setError($message)
            ->setHttpCode(500)
            ->setCode($e->getCode() ?: 500)
            ->getResponse();
    }
}
