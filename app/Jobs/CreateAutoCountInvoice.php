<?php

namespace App\Jobs;

use App\Enums\AccountingSoftwareProvider;
use App\Enums\OrderStatus;
use App\Enums\SettingType;
use App\Factories\AccountingSoftwareAdapterFactory;
use App\Models\Order;
use App\Services\OrderService;
use App\Services\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class CreateAutoCountInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Order $order;

    /**
     * Create a new job instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Execute the job.
     */
    public function handle(SettingService $setting_service, OrderService $order_service): void
    {
        if ($this->order->status->slug != OrderStatus::PACKED) {
            Log::error('AutoCount API Error', [
                'type' => 'Create Invoice',
                'message' => 'Invalid order status',
                'data' => [
                    'order_id' => $this->order->id,
                    'order_status' => $this->order->status->slug,
                ],
            ]);
            return;
        }

        $adapter = AccountingSoftwareAdapterFactory::getAdapterFor(
            AccountingSoftwareProvider::AUTO_COUNT
        );

        $accounts = $setting_service->findVia(SettingType::AUTOCOUNT, 'type');

        if (!Arr::get($accounts, 'options.accounts')) {
            Log::error('AutoCount API Error', [
                'type' => 'Create Invoice',
                'message' => 'No autocount accounts setup',
            ]);
            return;
        }

        $order_branch = $this->order->branches->first();

        if (!$order_branch) {
            Log::error('AutoCount API Error', [
                'type' => 'Create Invoice',
                'message' => 'Order branch is empty',
                'data' => [
                    'order_id' => $this->order->id,
                ],
            ]);
            return;
        }

        if (!isset($accounts->options['accounts'][$order_branch->id])) {
            Log::error('AutoCount API Error', [
                'type' => 'Create Invoice',
                'message' => 'Branch not found',
            ]);
            return;
        }

        $adapter->setConfig($accounts->options['accounts'][$order_branch->id])
            ->createInvoice($this->order);

        $order_service->setModel($this->order)
            ->updateStatus(OrderStatus::COMPLETE);
    }
}
