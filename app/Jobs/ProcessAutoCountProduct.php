<?php

namespace App\Jobs;

use App\Enums\ProductStatus;
use App\Services\ProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessAutoCountProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $products;

    /**
     * Create a new job instance.
     */
    public function __construct(array $products)
    {
        $this->products = $products;
    }

    /**
     * Execute the job.
     */
    public function handle(ProductService $product_service): void
    {
        foreach ($this->products as $data) {
            $product = $data['product'];

            $product_data = $this->mapAutoCountToProduct($product);

            $product_service->updateOrCreate(
                ['sku' => $product['productCode']],
                $product_data
            );
        }
    }

    /**
     * Map AutoCount product data to local Product model
     *
     * This function maps fields from AutoCount API response to our local Product model.
     * Modify the field mappings below to change how data is synchronized.
     *
     * @param array $auto_count_product
     * @return array
     */
    protected function mapAutoCountToProduct(array $product): array
    {
        // =================================================================
        // FIELD MAPPING CONFIGURATION
        // =================================================================
        // Modify these mappings to change how AutoCount data maps to your Product model

        return [
            // BASIC PRODUCT INFORMATION
            // -------------------------
            'name' => $product['productName'],
            'barcode' => $product['barCode'],

            // PRICING
            // -------
            // Convert price to cents (multiply by 100) if your system uses cents
            'price' => (float)$product['price'],

            // PRODUCT SETTINGS
            // ----------------
            'unit' => $product['unit'] ?? 'PCS',
            'weight' => null, // AutoCount doesn't provide weight

            // STATUS & VISIBILITY
            // -------------------
            'status' => $this->mapProductStatus($product['status'] ?? 'I'),

            // AUTOCOUNT METADATA
            // ------------------
            // Store original AutoCount data for reference and debugging
            'meta' => $this->buildProductMeta($product),
        ];
    }

    /**
     * Convert price based on your system requirements
     * Modify this if you store prices in cents or different format
     */
    private function convertPrice($price): float
    {
        return (float)$price;
    }

    /**
     * Map AutoCount status to your product status
     * Modify these mappings to match your status values
     */
    private function mapProductStatus(string $auto_count_status): string
    {
        return match ($auto_count_status) {
            'A' => ProductStatus::PUBLISH,    // Active
            'I' => ProductStatus::DRAFT,  // Inactive
            'D' => ProductStatus::INACTIVE,     // Discontinued
            default => ProductStatus::DRAFT     // Default to draft for unknown statuses
        };
    }

    /**
     * Build metadata object with AutoCount information
     * Simply store the entire AutoCount product data as JSON
     */
    private function buildProductMeta(array $product): array
    {
        return [
            'source' => 'autocount',
            'last_synced_at' => now()->toISOString(),
            'autocount_data' => [
                'productId' => $product['productId'],
            ],
        ];
    }
}
