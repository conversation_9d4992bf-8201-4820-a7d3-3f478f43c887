<?php

namespace App\Traits;

use App\Interfaces\ReportExportable;

trait ExportFileAdaptable
{
    protected ReportExportable $exportFileAdapter;

    public function getExportFileAdapter(): ReportExportable
    {
        return $this->exportFileAdapter;
    }

    public function setExportFileAdapter(ReportExportable $exportFileAdapter): static
    {
        $this->exportFileAdapter = $exportFileAdapter;
        return $this;
    }

}
