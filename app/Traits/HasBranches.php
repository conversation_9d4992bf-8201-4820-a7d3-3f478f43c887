<?php

namespace App\Traits;

use App\Models\Branch;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasBranches
{
    /**
     * Get all branches associated with this model.
     */
    public function branches(): MorphToMany
    {
        return $this->morphToMany(
            Branch::class,
            'model',
            'branch_model',
            'model_id',
            'branch_id'
        );
    }

    /**
     * Attach this model to a branch.
     */
    public function attachToBranch(Branch $branch): void
    {
        $this->branches()->attach($branch->id);
    }

    /**
     * Detach this model from a branch.
     */
    public function detachFromBranch(Branch $branch): void
    {
        $this->branches()->detach($branch->id);
    }

    /**
     * Sync this model with branches.
     */
    public function syncBranches(array $branchIds): void
    {
        $this->branches()->sync($branchIds);
    }

    /**
     * Check if this model is associated with a branch.
     */
    public function isAssociatedWithBranch(Branch $branch): bool
    {
        return $this->branches()->where('branch_id', $branch->id)->exists();
    }

    /**
     * Get active branches associated with this model.
     */
    public function activeBranches(): MorphToMany
    {
        return $this->branches()->where('active', true);
    }
}
