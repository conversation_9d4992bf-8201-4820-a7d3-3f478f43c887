<?php

namespace App\Traits;

use App\Enums\ProductStatus;
use App\Models\Product;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

trait Productable
{
    /**
     * Boot the trait and set up model event listeners.
     */
    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    abstract public function productType(): string;

    protected static function bootProductable()
    {
        // Create or update Product when the productable model is created
        static::created(function ($model) {
            $model->syncProduct();
        });

        // Update Product when the productable model is updated
        static::updated(function ($model) {
            $model->syncProduct();
        });

        // Delete Product when the productable model is deleted
        static::deleted(function ($model) {
            if ($model->product) {
                $model->product->delete();
            }
        });
    }

    /**
     * Define the polymorphic relationship to Product.
     */
    public function product()
    {
        return $this->morphOne(Product::class, 'productable');
    }

    /**
     * Sync the Product record with the productable model's attributes.
     */
    public function syncProduct()
    {
        DB::transaction(function () {
            $attributes = $this->getProductAttributes();
            if ($this->product) {
                // Update existing Product
                $this->product->update($attributes);
            } else {
                // Create new Product
                $this->product = $this->product()->create($attributes);
            }
        });
    }

    /**
     * Get categories through the product relationship.
     * This is a helper method, not an Eloquent relationship.
     */
    public function getCategories()
    {
        return $this->product?->categories;
    }
    /**
     * Define the attributes to sync to the Product model.
     * Override this method in the model if specific attributes are needed.
     *
     * @return array
     */
    protected function getProductAttributes()
    {
        return [
            'name' => $this->name,
            'price' => $this->price,
            'type' => $this->productType(),
            'description' => $this->description,
            'tax_class_id' => $this->tax_class_id,
            'status' =>  $this->is_active ? ProductStatus::PUBLISH : ProductStatus::INACTIVE,
        ];
    }
}
