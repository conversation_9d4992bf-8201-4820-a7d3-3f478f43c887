<?php

namespace App\Repositories;

use App\Models\Instructor;
use App\Models\User;
use Prettus\Repository\Eloquent\BaseRepository;

class InstructorRepository extends BaseRepository
{
    function model()
    {
        return Instructor::class;
    }

    public function syncUser(Instructor $instructor, User $user): static
    {
        $instructor->user()->associate($user);
        $instructor->save();

        return $this;
    }

    public function syncBranches(Instructor $instructor, array $branches): static
    {
        $instructor->branches()->sync($branches);

        return $this;
    }
}
