<?php

namespace App\Repositories;

use App\Models\Order;
use Prettus\Repository\Eloquent\BaseRepository;

/**
 * Class OrderRepository.
 *
 * @package namespace App\Repositories;
 */
class OrderRepository extends BaseRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model()
    {
        return Order::class;
    }

    public function syncBranches(Order $order, array $branches): void
    {
        $order->branches()->sync($branches);
    }

    public function clearProducts(Order $order): void
    {
        $order->products()->delete();
    }
}
