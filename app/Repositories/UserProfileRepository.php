<?php

namespace App\Repositories;

use App\Models\UserProfile;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;
use Prettus\Repository\Exceptions\RepositoryException;

class UserProfileRepository extends BaseRepository
{

    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    /**
     * Configure the Model
     */
    public function model()
    {
        return UserProfile::class;
    }
}
