<?php

namespace App\Repositories;

use App\Models\Branch;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;

class BranchRepository extends BaseRepository
{

    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model(): string
    {
        return Branch::class;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    /**
     * Check if the given branch has any associated models
     *
     * @param Branch $branch
     * @return bool
     */
    public function hasAssociatedModels(Branch $branch): bool
    {
        $branch->load('models');

        return $branch->models->count() > 0;
    }
}
