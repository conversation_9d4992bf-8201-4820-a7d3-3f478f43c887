<?php

namespace App\Repositories;

use App\Models\Product;
use App\Models\ProductBundleItem;
use Prettus\Repository\Eloquent\BaseRepository;

class ProductRepository extends BaseRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model(): string
    {
        return Product::class;
    }

    public function syncCategories(Product $product, array $product_category_ids): void
    {
        $product->categories()->sync($product_category_ids);
    }

    public function deleteVariants(Product $product, $product_variant_ids = []): void
    {
        if (empty($product_variant_ids)) {
            $product->variants()->delete();
        } else {
            $product->variants()->whereIn('id', $product_variant_ids)->delete();
        }
    }

    public function deleteBundleItems(Product $product, $bundle_item_ids = []): void
    {
        if (empty($bundle_item_ids)) {
            $product->bundleItems()->delete();
        } else {
            $product->bundleItems()->whereIn('item_product_id', $bundle_item_ids)->delete();
        }
    }

    public function syncWarehouses(Product $product, array $warehouses): void
    {
        $product->warehouses()->sync($warehouses);
    }
}
