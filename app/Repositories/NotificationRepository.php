<?php

namespace App\Repositories;

use App\Models\NotificationTemplate;
use Illuminate\Database\Eloquent\Collection;

class NotificationRepository
{
    /**
     * Get available templates for notification name and locale
     */
    public function getAvailableTemplates(string $templateName, string $locale, array $channels): Collection
    {
        return NotificationTemplate::query()
            ->where('name', $templateName)
            ->where('locale', $locale)
            ->whereIn('channel', $channels)
            ->where('is_active', true)
            ->get() // Execute query and get a Collection
            ->keyBy('channel'); // Key the collection by channel for easy lookup
    }

    /**
     * Get template for specific channel
     */
    public function getTemplate(string $templateName, string $channel, string $locale): ?NotificationTemplate
    {
        return NotificationTemplate::where('name', $templateName)
            ->where('channel', $channel)
            ->where('locale', $locale)
            ->where('is_active', true)
            ->first();
    }
}
