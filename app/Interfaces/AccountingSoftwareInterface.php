<?php

namespace App\Interfaces;

use App\Models\Order;

interface AccountingSoftwareInterface extends ApiProviderInterface
{
    /**
     * Set date from for filtering records
     */
    public function setDateFrom(string $date_from): self;

    /**
     * Set date to for filtering records
     */
    public function setDateTo(string $date_to): self;

    /**
     * Set page number for pagination
     */
    public function setPage(int $page): self;

    /**
     * Set page size for pagination
     */
    public function setPageSize(int $size): self;

    /**
     * Switch config by branch id
     */
    public function setConfig(array $config): self;

    /**
     * Get products from accounting software (single page)
     */
    public function getProducts(): array;

    /**
     * Get specific product by ID
     */
    public function getProduct(string $product_id): array;

    public function createInvoice(Order $order): void;

}
