<?php

namespace App\Interfaces;

interface PaymentGatewayInterface extends ApiProviderInterface
{
    /**
     * Process a payment transaction
     */
    public function processPayment(array $paymentData): array;

    /**
     * Verify a payment transaction
     */
    public function verifyPayment(string $transactionId): array;

    /**
     * Refund a payment transaction
     */
    public function refundPayment(string $transactionId, float $amount): array;

    /**
     * Get transaction status
     */
    public function getTransactionStatus(string $transactionId): array;

    /**
     * Get supported payment methods
     */
    public function getSupportedPaymentMethods(): array;



    /**
     * Get payment gateway configuration
     */
    public function getConfiguration(): array;

    /**
     * Set payment gateway credentials
     */
    public function setCredentials(array $credentials): self;
}
