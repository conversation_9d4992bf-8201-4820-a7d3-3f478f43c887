<?php

namespace App\Interfaces;

use Illuminate\Support\Carbon;

interface EcommerceProviderInterface extends ApiProviderInterface
{
    public function setOrderDateFrom(Carbon $date_from): EcommerceProviderInterface;

    public function setOrderDateTo(Carbon $date_to): EcommerceProviderInterface;

    public function setOrderStatuses(array $statuses): EcommerceProviderInterface;

    public function setOrderPage(int $page): EcommerceProviderInterface;

    public function setOrderPageSize(int $size): EcommerceProviderInterface;

    public function setChannelId(int $channelId): EcommerceProviderInterface;

    public function getOrders(): array;

    public function getOrder(): array;
    public function setOrderId(string $order_id): EcommerceProviderInterface;

    public function getBranches(): array;

    public function getPaymentMethods(): array;
}
