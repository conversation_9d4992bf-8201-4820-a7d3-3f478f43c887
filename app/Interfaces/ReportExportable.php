<?php

namespace App\Interfaces;

use Illuminate\Database\Query\Builder;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromGenerator;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\FromView;

interface ReportExportable
{
    public function generate();

    public function setOutputLocalFilePath(string $outputLocalFilePath): ReportExportable;

    public function setPaperOrientation(string $paperOrientation): ReportExportable;

    public function setReportBuilder(FromCollection|FromView|FromQuery|FromGenerator $reportBuilder): ReportExportable;

    public function setReportViewName(string $reportViewName): ReportExportable;

    public function setReportData(array $reportData): ReportExportable;

    public function setQuery(\Illuminate\Database\Eloquent\Builder|Builder $query): ReportExportable;

    public function getFileNameWithExtension($file_name): string;
}
