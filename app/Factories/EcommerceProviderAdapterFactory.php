<?php

namespace App\Factories;

use App\Adapters\SiteGiantAdapter;
use App\Enums\EcommerceProvider;
use App\Helpers\ErrorCodeHelper;

class EcommerceProviderAdapterFactory
{

    public function __construct()
    {
    }

    public static function getAdapterFor(string $type)
    {
        return match ($type) {
            EcommerceProvider::SITE_GIANT => new SiteGiantAdapter(),
            default => ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 404),
        };
    }
}
