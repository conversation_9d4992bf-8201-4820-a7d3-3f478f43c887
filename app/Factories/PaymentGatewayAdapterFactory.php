<?php

namespace App\Factories;

use App\Adapters\UnionPayAdapter;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\PaymentGatewayInterface;

class PaymentGatewayAdapterFactory
{
    public function __construct()
    {
    }

    public static function getAdapterFor(string $type): PaymentGatewayInterface
    {
        return match ($type) {
            'unionpay' => new UnionPayAdapter(),
            default => ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 404),
        };
    }
}
