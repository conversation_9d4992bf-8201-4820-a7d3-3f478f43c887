<?php

namespace App\Factories;

use App\Adapters\AutoCountAdapter;
use App\Enums\AccountingSoftwareProvider;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\AccountingSoftwareInterface;
use InvalidArgumentException;

class AccountingSoftwareAdapterFactory
{
    /**
     * Get adapter for the specified accounting software provider
     *
     * @param AccountingSoftwareProvider $provider
     * @return AccountingSoftwareInterface
     * @throws InvalidArgumentException
     */
    public static function getAdapterFor(string $provider)
    {
        return match ($provider) {
            AccountingSoftwareProvider::AUTO_COUNT => new AutoCountAdapter(),
            default => ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 404),
        };
    }
}
