<?php

namespace App\Factories;

use App\Adapters\EmailAdapter;
use App\Adapters\WhatsAppAdapter;
use App\Adapters\SmsAdapter;
use App\Enums\NotificationChannel;
use App\Services\SendPulseService;
use App\Helpers\ErrorCodeHelper;

class NotificationAdapterFactory
{
    public function __construct() {}

    /**
     * Get adapter for specific notification channel with lazy provider instantiation
     */
    public static function getAdapterFor(string $channel): object
    {
        switch ($channel) {

            case NotificationChannel::WHATSAPP:
                // Lazy instantiation - only create SendPulse when needed
                $provider = app(SendPulseService::class);
                return new WhatsAppAdapter($provider);

            case NotificationChannel::SMS:
                // Lazy instantiation - only create SendPulse when needed
                $provider = app(SendPulseService::class);
                return new SmsAdapter($provider);

            default:
                ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 404);
        }
    }
}
