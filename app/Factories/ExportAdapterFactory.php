<?php

namespace App\Factories;

use App\Adapters\ExcelExportAdapter;
use App\Adapters\PdfExportAdapter;
use App\Enums\ExportType;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\ReportExportable;

class ExportAdapterFactory
{

    public function __construct() {}

    public static function getAdapterFor(string $export_file_type): ReportExportable
    {
        switch ($export_file_type) {
            case ExportType::EXCEL:
                return new ExcelExportAdapter();
            case ExportType::PDF:
                return new PDFExportAdapter();
            default:
                ErrorCodeHelper::throwError(ErrorCodeHelper::GENERIC_ERROR, 404);
        }
    }
}
