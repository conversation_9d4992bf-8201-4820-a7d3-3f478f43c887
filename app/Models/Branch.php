<?php

namespace App\Models;

use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Branch extends Model implements HasMedia
{
    use HasFactory, Sluggable, InteractsWithMedia;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'branch';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'image',
        'slug',
        'geolocation',
        'active',
        'virtual',

    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'image' => 'json',
        'geolocation' => 'json',
        'active' => 'boolean',
        'virtual' => 'boolean',

    ];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name',
            ],
        ];
    }

    /**
     * Scope a query to only include active branches.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include virtual branches.
     */
    public function scopeVirtual($query)
    {
        return $query->where('virtual', true);
    }

    /**
     * Scope a query to only include physical branches.
     */
    public function scopePhysical($query)
    {
        return $query->where('virtual', false);
    }

    /**
     * Scope a query to only include classpass enabled branches.
     */
    public function scopeClasspassEnabled($query)
    {
        return $query->where('classpass_enabled', true);
    }

    /**
     * Get the branch details.
     */
    public function details(): HasOne
    {
        return $this->hasOne(BranchDetails::class, 'branch_id');
    }

    /**
     * Get all models associated with this branch.
     */
    public function models(): HasMany
    {
        return $this->hasMany(BranchModel::class, 'branch_id');
    }

    /**
     * Get all users associated with this branch.
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(
            User::class,
            'model',
            'branch_model',
            'branch_id',
            'model_id'
        );
    }

    /**
     * Get all shops associated with this branch.
     */
    public function shops(): MorphToMany
    {
        return $this->morphedByMany(
            Shop::class,
            'model',
            'branch_model',
            'branch_id',
            'model_id'
        );
    }

    /**
     * Get the image media for GraphQL.
     */
    public function image()
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', 'image');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('image')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }
}
