<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'channel',
        'locale',
        'subject',
        'content',
        'replacements',
        'is_active'
    ];

    protected $casts = [
        'replacements' => 'array',
        'is_active' => 'boolean',
    ];
}
