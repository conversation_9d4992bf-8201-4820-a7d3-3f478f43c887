<?php

namespace App\Models;

use App\Traits\HasBranches;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes, HasBranches;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [
        'id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'require_shipping' => 'boolean',
        'amount' => 'float',
        'sales_tax' => 'decimal:2',
        'paid_total' => 'decimal:2',
        'total' => 'decimal:2',
        'total_invoiced' => 'decimal:2',
        'total_refunded' => 'decimal:2',
        'coupon_discount' => 'decimal:2',
        'discount' => 'decimal:2',
        'points_earn' => 'decimal:2',
        'points_used' => 'decimal:2',
        'points_discount' => 'decimal:2',
        'golds_earn' => 'decimal:2',
        'golds_used' => 'decimal:2',
        'golds_discount' => 'decimal:2',
        'store_credit_earn' => 'decimal:2',
        'store_credit' => 'decimal:2',
        'gift_card_credit' => 'float',
        'payment_method_info' => 'json',
        'shipping_address' => 'json',
        'billing_address' => 'json',
        'items_weight' => 'decimal:3',
        'delivery_fee' => 'decimal:2',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'auth_ref_id',
        'auth_token',
    ];

    /**
     * Get the customer that owns the order.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the sales person who created the order.
     */
    public function saleBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sale_by_id');
    }

    /**
     * Get the shop that owns the order.
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Get the parent order.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'parent_id');
    }

    /**
     * Get the child orders.
     */
    public function children(): HasMany
    {
        return $this->hasMany(Order::class, 'parent_id');
    }

    /**
     * Get the order products.
     */
    public function products(): HasMany
    {
        return $this->hasMany(OrderProduct::class);
    }

    public function status(): belongsTo
    {
        return $this->belongsTo(OrderStatus::class, 'order_status_id');
    }

    public function paymentMethod(): belongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    public function branches(): MorphToMany
    {
        return $this->morphToMany(Branch::class, 'model', 'branch_model');
    }
}
