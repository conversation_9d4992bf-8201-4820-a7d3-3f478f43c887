<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaxClass extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'tax_classes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'rate',
        'country',
        'state',
        'zip',
        'city',
        'is_global',
        'priority',
        'on_shipping',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rate' => 'decimal:4',
        'is_global' => 'boolean',
        'priority' => 'integer',
        'on_shipping' => 'boolean',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the class types that use this tax class.
     */
    public function classTypes(): HasMany
    {
        return $this->hasMany(ClassType::class, 'tax_class_id');
    }

    /**
     * Get the products that use this tax class.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'tax_class_id');
    }
}
