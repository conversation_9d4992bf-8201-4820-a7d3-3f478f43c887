<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class UserProfile extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'bio',
        'socials',
        'race',
        'contact',
        'emergency_contact',
        'emergency_phone',
        'street_address',
        'city',
        'zip',
        'state_id',
        'studio_id',
        'branch_id',
        'customer_id',
    ];

    /**
     * Get the user that owns the profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Alias for user relationship for consistency with existing code.
     */
    public function customer(): BelongsTo
    {
        return $this->user();
    }

    /**
     * Get the avatar media for GraphQL.
     */
    public function avatar()
    {
        return $this->morphOne(Media::class, 'model')
            ->where('collection_name', 'avatar');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }
}
