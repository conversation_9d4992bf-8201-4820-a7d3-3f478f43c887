<?php

namespace App\Enums;

use <PERSON>Sampo\Enum\Enum;

final class Permission extends Enum
{
    // Setting permission
    public const SAVE_SETTING = 'save-setting';

    // Branch permissions
    public const VIEW_BRANCH = 'view-branch';
    public const CREATE_BRANCH = 'create-branch';
    public const EDIT_BRANCH = 'edit-branch';
    public const DELETE_BRANCH = 'delete-branch';
    public const EDIT_BRANCH_PIN = 'edit-branch-pin';

    // Product permissions
    public const VIEW_PRODUCT = 'view-product';
    public const CREATE_PRODUCT = 'create-product';
    public const EDIT_PRODUCT = 'edit-product';
    public const DELETE_PRODUCT = 'delete-product';

    // Wallet permissions
    public const VIEW_WALLET_GROUP = 'view-wallet-group';
    public const EDIT_WALLET_GROUP = 'edit-wallet-group';
    public const VIEW_WALLET_RULE = 'view-wallet-rule';
    public const EDIT_WALLET_RULE = 'edit-wallet-rule';
    public const DELETE_WALLET_RULE = 'delete-wallet-rule';
    public const VIEW_WALLET = 'view-wallet';
    public const VIEW_WALLET_TRANSACTION = 'view-wallet-transaction';

    // Address permissions
    public const VIEW_ADDRESS = 'view-address';

    // Dashboard permissions
    public const VIEW_DASHBOARD = 'view-dashboard';

    // Banner permissions
    public const EDIT_BANNER = 'edit-banner';
    public const DELETE_BANNER = 'delete-banner';
    public const SEARCH_BANNER = 'search-banner';

    // Discount permissions
    public const VIEW_DISCOUNT = 'view-discount';
    public const EDIT_DISCOUNT = 'edit-discount';
    public const DELETE_DISCOUNT = 'delete-discount';

    // Customer group permissions
    public const EDIT_CUSTOMER_GROUP = 'edit-customer-group';

    // Category permissions
    public const CREATE_CATEGORY = 'create-category';
    public const EDIT_CATEGORY = 'edit-category';
    public const DELETE_CATEGORY = 'delete-category';

    // Order permissions
    public const VIEW_ORDER = 'view-order';
    public const CREATE_ORDER = 'create-order';
    public const EDIT_ORDER = 'edit-order';
    public const DELETE_ORDER = 'delete-order';
    public const EDIT_ORDER_STATUS = 'edit-order-status';

    // Forms permissions
    public const EDIT_FORMS = 'edit-forms';

    // Search term permissions
    public const VIEW_SEARCH_TERM = 'view-search-term';
    public const EDIT_SEARCH_TERM = 'edit-search-term';

    // Location permissions
    public const VIEW_LOCATION = 'view-location';
    public const EDIT_LOCATION = 'edit-location';
    public const DELETE_LOCATION = 'delete-location';

    // Notification template permissions
    public const CREATE_NOTIFICATION_TEMPLATE = 'create-notification-template';
    public const EDIT_NOTIFICATION_TEMPLATE = 'edit-notification-template';
    public const DELETE_NOTIFICATION_TEMPLATE = 'delete-notification-template';

    // Payment gateway permissions
    public const EDIT_PAYMENT_GATEWAY = 'edit-payment-gateway';
    public const EDIT_PAYMENT_METHOD = 'edit-payment-method';

    // Settings permissions
    public const EDIT_SETTING = 'edit-setting';

    // Shipping permissions
    public const EDIT_SHIPPING = 'edit-shipping';

    // Static block permissions
    public const EDIT_STATIC_BLOCK = 'edit-static-block';

    // Tag permissions
    public const EDIT_TAG = 'edit-tag';

    // Tax permissions
    public const EDIT_TAX = 'edit-tax';

    // User permissions
    public const CREATE_USER = 'create-user';
    public const VIEW_USER = 'view-user';
    public const EDIT_USER = 'edit-user';
    public const DELETE_USER = 'delete-user';

    // Role and permission management
    public const VIEW_ROLE_PERMISSION = 'view-role-permission';
    public const EDIT_ROLE_PERMISSION = 'edit-role-permission';

    // Blocked domain permissions
    public const EDIT_BLOCKED_DOMAIN = 'edit-blocked-domain';

    // Studio permissions
    public const EDIT_STUDIO = 'edit-studio';

    // Room permissions
    public const EDIT_ROOM = 'edit-room';

    // Instructor permissions
    public const string VIEW_INSTRUCTOR = 'view-instructor';
    public const string CREATE_INSTRUCTOR = 'create-instructor';
    public const string EDIT_INSTRUCTOR = 'edit-instructor';
    public const string DELETE_INSTRUCTOR = 'delete-instructor';

    // Designation permissions
    public const string VIEW_DESIGNATION = 'view-designation';
    public const string CREATE_DESIGNATION = 'create-designation';
    public const string EDIT_DESIGNATION = 'edit-designation';
    public const string DELETE_DESIGNATION = 'delete-designation';

    // Class type permissions
    public const CREATE_CLASS_TYPE = 'create-class-type';
    public const EDIT_CLASS_TYPE = 'edit-class-type';
    public const DELETE_CLASS_TYPE = 'delete-class-type';

    // Series type permissions
    public const EDIT_SERIES_TYPE = 'edit-series-type';

    // Series permissions
    public const EDIT_SERIES = 'edit-series';

    // Contract permissions
    public const EDIT_CONTRACTS = 'edit-contracts';

    // Private board permissions
    public const EDIT_PRIVATE_BOARDS = 'edit-private-boards';

    // Schedule settings permissions
    public const EDIT_SCHEDULE_SETTINGS = 'edit-schedule-settings';

    // Schedule permissions
    public const EDIT_SCHEDULES = 'edit-schedules';

    // Schedule booking permissions
    public const EDIT_SCHEDULE_BOOKINGS = 'edit-schedule-bookings';
    public const CANCEL_BOOKING_AFTER_CLASS = 'cancel-booking-after-class';

    // Gift card permissions
    public const EDIT_GIFT_CARD_TEMPLATES = 'edit-gift_card-templates';
    public const EDIT_GIFT_CARDS = 'edit-gift-cards';

    // User package permissions
    public const EDIT_USER_PACKAGES = 'edit-user-packages';

    // Difficulty level permissions
    public const EDIT_DIFFICULTY_LEVELS = 'edit-difficulty-levels';

    // Commission permissions
    public const EDIT_COMMISSION_GROUPS = 'edit-commission-groups';
    public const EDIT_COMMISSION_REPORTS = 'edit-commission-reports';

    // Customer permissions
    public const EDIT_CUSTOMER = 'edit-customer';
    public const EDIT_CUSTOMER_PASSWORD = 'edit-customer-password';
    public const SAVE_CUSTOMER_WITHOUT_OTP = 'save-customer-without-otp';

    // Supplier permissions
    public const EDIT_SUPPLIERS = 'edit-suppliers';

    // Purchase order permissions
    public const EDIT_PURCHASE_ORDERS = 'edit-purchase-orders';

    // Stock request permissions
    public const EDIT_STOCK_REQUESTS = 'edit-stock-requests';
    public const APPROVE_STOCK_REQUESTS = 'approve-stock-requests';

    // Stock transfer permissions
    public const EDIT_STOCK_TRANSFERS = 'edit-stock-transfers';

    // Salon used permissions
    public const EDIT_SALON_USED = 'edit-salon-used';

    // Customer view permissions
    public const VIEW_CUSTOMER = 'view-customer';
    public const VIEW_CUSTOMER_LISTING = 'view-customer-listing';

    // Report permissions
    public const VIEW_REPORT = 'view-report';
    public const EXPORT_CUSTOMER = 'export-customer';

    // Refund permissions
    public const CREATE_REFUND = 'create-refund';
    public const APPROVE_REFUND = 'approve-refund';
    public const PROCESS_REFUND = 'process-refund';

    // Designation permissions
    public const EDIT_DESIGNATIONS = 'edit-designations';

    // Warehouse permissions
    public const VIEW_WAREHOUSE = 'view-warehouse';
    public const CREATE_WAREHOUSE = 'create-warehouse';
    public const EDIT_WAREHOUSE = 'edit-warehouse';
    public const DELETE_WAREHOUSE = 'delete-warehouse';
}
