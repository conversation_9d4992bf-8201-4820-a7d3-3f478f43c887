<?php

namespace App\Enums;

use <PERSON>Sampo\Enum\Enum;

final class ExportHistoryModel extends Enum
{
    public const ORDER = 'order';
    public const USER = 'user';
    public const PRODUCT = 'product';
    public const SERVICE = 'service';
    public const SEARCH_TERM = 'search_term';
    public const WALLET_TRANSACTION = 'wallet_transaction';

    public const VOID_REPORT = 'void_report';
    public const PAYMENT_REPORT = 'payment_report';
    public const DAILY_SALE_REPORT = 'daily_sale_report';
    public const DAILY_CLOSING_REPORT = 'daily_closing_report';
    public const COUNTER_REPORT = 'counter_report';
    public const SALE_REPORT = 'sale_report';
    public const SALES_DETAIL_REPORT = 'sales_detail_report';
    public const PRODUCT_SALE_REPORT = 'product_sale_report';
    public const SERVICE_SALE_REPORT = 'service_sale_report';
    public const PACKAGE_SALE_REPORT = 'package_sale_report';
    public const TOP_PRODUCT_SALE_REPORT = 'top_product_sale_report';
    public const TOP_SERVICE_SALE_REPORT = 'top_service_sale_report';
    public const TOP_PACKAGE_SALE_REPORT = 'top_package_sale_report';
    public const PRODUCT_BRAND_REPORT = 'product_brand_report';
    public const EARNED_REVENUE_REPORT = 'earned_revenue_report';
    public const STYLIST_SALE_REPORT = 'stylist_sale_report';
    public const STYLIST_SALE_DETAIL_REPORT = 'stylist_sale_detail_report';
    public const STYLIST_COMMISSION_REPORT = 'stylist_commission_report';
    public const STYLIST_COMMISSION_DETAIL_REPORT = 'stylist_commission_detail_report';
    public const ATTENDANCE_REPORT = 'attendance_report';
    public const ATTENDANCE_REVENUE_REPORT = 'attendance_revenue_report';
    public const PACKAGE_VALUE_REPORT = 'package_value_report';
    public const CUSTOMER_BIRTHDAY_REPORT = 'customer_birthday_report';
    public const CUSTOMER_REPORT = 'customer_report';
    public const TOP_VISITED_CUSTOMER_REPORT = 'top_visited_customer_report';
    public const LESS_VISIT_CUSTOMER_REPORT = 'less_visit_customer_report';
    public const CUSTOMER_BALANCE_REPORT = 'customer_balance_report';
    public const LAST_VISIT_CUSTOMER_REPORT = 'last_visit_customer_report';
    public const CREDIT_VALUE_REPORT = 'store_credit_value_report';
    public const STOCK_BALANCE_REPORT = 'stock_balance_report';
    public const STOCK_BALANCE_ITEMISED_REPORT = 'stock_balance_itemised_report';
    public const STOCK_TRANSFER_REPORT = 'stock_transfer_report';
    public const STOCK_TRANSFER_ITEMISED_REPORT = 'stock_transfer_itemised_report';
    public const STOCK_LEDGER_REPORT = 'stock_ledger_report';
    public const STOCK_LEDGER_ITEMISED_REPORT = 'stock_ledger_itemised_report';
    public const PURCHASE_REPORT = 'purchase_report';
    public const PURCHASE_ITEMISED_REPORT = 'purchase_itemised_report';
    public const STOCK_HISTORY_REPORT = 'stock_history_report';
    public const STOCK_HISTORY_ITEMISED_REPORT = 'stock_history_itemised_report';
    public const STOCK_ADJUSTMENT_REPORT = 'stock_adjustment_report';
    public const STOCK_ADJUSTMENT_ITEMISED_REPORT = 'stock_adjustment_itemised_report';
    public const SALON_USED_REPORT = 'salon_used_report';
    public const SALON_USED_ITEMISED_REPORT = 'salon_used_itemised_report';

    public const ORDER_FILEPREFIX = 'order_summary';
    public const ORDER_DETAILED_FILEPREFIX = 'order_detailed';
    public const PRODUCT_FILEPREFIX = 'products';
    public const SERVICE_FILEPREFIX = 'services';
    public const USER_FILEPREFIX = 'users';
    public const SEARCH_TERM_FILEPREFIX = 'search_terms';
    public const WALLET_TRANSACTION_FILEPREFIX = 'wallet_transactions';

    public const VOID_REPORT_FILEPREFIX = 'void_report';
    public const PAYMENT_REPORT_FILEPREFIX = 'payment_report';
    public const DAILY_SALE_REPORT_FILEPREFIX = 'daily_sale_report';
    public const DAILY_CLOSING_REPORT_FILEPREFIX = 'daily_closing_report';
    public const COUNTER_REPORT_FILEPREFIX = 'counter_report';
    public const SALE_REPORT_FILEPREFIX = 'sale_report';
    public const SALES_DETAIL_REPORT_FILEPREFIX = 'sales_detail_report';
    public const PRODUCT_SALE_REPORT_FILEPREFIX = 'product_sale_report';
    public const SERVICE_SALE_REPORT_FILEPREFIX = 'service_sale_report';
    public const PACKAGE_SALE_REPORT_FILEPREFIX = 'package_sale_report';
    public const TOP_PRODUCT_SALE_REPORT_FILEPREFIX = 'top_product_sale_report';
    public const TOP_SERVICE_SALE_REPORT_FILEPREFIX = 'top_service_sale_report';
    public const TOP_PACKAGE_SALE_REPORT_FILEPREFIX = 'top_package_sale_report';
    public const PRODUCT_BRAND_REPORT_FILEPREFIX = 'product_brand_report';
    public const ATTENDANCE_REPORT_FILEPREFIX = 'attendance_report';
    public const ATTENDANCE_REVENUE_REPORT_FILEPREFIX = 'attendance_revenue_report';
    public const STYLIST_SALE_REPORT_FILEPREFIX = 'stylist_sale_report';
    public const STYLIST_SALE_DETAIL_REPORT_FILEPREFIX = 'stylist_sale_detail_report';
    public const STYLIST_COMMISSION_REPORT_FILEPREFIX = 'stylist_commission_report';
    public const STYLIST_COMMISSION_DETAIL_REPORT_FILEPREFIX = 'stylist_commission_detail_report';
    public const PACKAGE_VALUE_REPORT_FILEPREFIX = 'package_value_report';
    public const CUSTOMER_BIRTHDAY_REPORT_FILEPREFIX = 'customer_birthday_report';
    public const CUSTOMER_REPORT_FILEPREFIX = 'customer_report';
    public const TOP_VISITED_CUSTOMER_REPORT_FILEPREFIX = 'top_visited_customer_report';
    public const LESS_VISIT_CUSTOMER_REPORT_FILEPREFIX = 'less_visit_customer_report';
    public const CUSTOMER_BALANCE_REPORT_FILEPREFIX = 'customer_balance_report';
    public const LAST_VISIT_CUSTOMER_REPORT_FILEPREFIX = 'last_visit_customer_report';
    public const CREDIT_VALUE_REPORT_FILEPREFIX = 'store_credit_value_report';
    public const EARNED_REVENUE_REPORT_FILEPREFIX = 'earned_revenue_report';
    public const STOCK_BALANCE_REPORT_FILEPREFIX = 'stock_balance_report';
    public const STOCK_BALANCE_ITEMISED_REPORT_FILEPREFIX = 'stock_balance_itemised_report';
    public const STOCK_TRANSFER_REPORT_FILEPREFIX = 'stock_transfer_report';
    public const STOCK_TRANSFER_ITEMISED_REPORT_FILEPREFIX = 'stock_transfer_itemised_report';
    public const STOCK_LEDGER_REPORT_FILEPREFIX = 'stock_ledger_report';
    public const STOCK_LEDGER_ITEMISED_REPORT_FILEPREFIX = 'stock_ledger_itemised_report';
    public const PURCHASE_REPORT_FILEPREFIX = 'purchase_report';
    public const PURCHASE_ITEMISED_REPORT_FILEPREFIX = 'purchase_itemised_report';
    public const STOCK_HISTORY_REPORT_FILEPREFIX = 'stock_history_report';
    public const STOCK_HISTORY_ITEMISED_REPORT_FILEPREFIX = 'stock_history_itemised_report';
    public const STOCK_ADJUSTMENT_REPORT_FILEPREFIX = 'stock_adjustment_report';
    public const STOCK_ADJUSTMENT_ITEMISED_REPORT_FILEPREFIX = 'stock_adjustment_itemised_report';
    public const SALON_USED_REPORT_FILEPREFIX = 'salon_used_report';
    public const SALON_USED_ITEMISED_REPORT_FILEPREFIX = 'salon_used_itemised_report';

}
