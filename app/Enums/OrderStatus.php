<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

class OrderStatus extends Enum
{
    // Main workflow statuses
    public const PENDING = 'pending';
    public const PROCESSING = 'processing';
    public const PACKED = 'packed';
    public const DELIVERY = 'delivery';
    public const COMPLETE = 'complete';

    // Payment related statuses
    public const HOLDED = 'holded';
    public const PAYMENT_REVIEW = 'payment_review';
    public const PENDING_PAYMENT = 'pending_payment';

    // Negative statuses
    public const FRAUD = 'fraud';
    public const CANCELED = 'canceled';
    public const CLOSED = 'closed';

    // Refund statuses
    public const IN_REFUND_PROCESS_B = 'in_refund_process_b';
    public const IN_REFUND_PROCESS_SC = 'in_refund_process_sc';

    // Delivery specific statuses
    public const ARRIVE_AT_DROPZONE = 'arrive_at_dropzone';
    public const FAILED_DELIVERY = 'failed_delivery';

    /**
     * Get statuses grouped by category
     */
    public static function getStatusGroups(): array
    {
        return [
            'workflow' => [
                self::PENDING,
                self::PROCESSING,
                self::PACKED,
                self::DELIVERY,
                self::COMPLETE,
            ],
            'payment' => [
                self::HOLDED,
                self::PAYMENT_REVIEW,
                self::PENDING_PAYMENT,
            ],
            'negative' => [
                self::FRAUD,
                self::CANCELED,
                self::CLOSED,
            ],
            'refund' => [
                self::IN_REFUND_PROCESS_B,
                self::IN_REFUND_PROCESS_SC,
            ],
            'delivery' => [
                self::ARRIVE_AT_DROPZONE,
                self::FAILED_DELIVERY,
            ],
        ];
    }

    /**
     * Get the next possible statuses for a given status
     */
    public static function getNextStatuses(string $currentStatus): array
    {
        return match ($currentStatus) {
            self::PENDING => [self::PROCESSING, self::PAYMENT_REVIEW, self::CANCELED],
            self::PROCESSING => [self::PACKED, self::HOLDED, self::CANCELED],
            self::PACKED => [self::DELIVERY, self::CANCELED],
            self::DELIVERY => [self::COMPLETE, self::ARRIVE_AT_DROPZONE, self::FAILED_DELIVERY],
            self::PAYMENT_REVIEW => [self::PENDING, self::FRAUD, self::CANCELED],
            self::HOLDED => [self::PROCESSING, self::CANCELED],
            self::FAILED_DELIVERY => [self::DELIVERY, self::CANCELED],
            self::ARRIVE_AT_DROPZONE => [self::COMPLETE],
            default => [],
        };
    }

    /**
     * Check if status allows cancellation
     */
    public static function canBeCanceled(string $status): bool
    {
        return in_array($status, [
            self::PENDING,
            self::PROCESSING,
            self::PACKED,
            self::PAYMENT_REVIEW,
            self::HOLDED,
        ]);
    }

    /**
     * Check if status is final (no further changes allowed)
     */
    public static function isFinal(string $status): bool
    {
        return in_array($status, [
            self::COMPLETE,
            self::CANCELED,
            self::CLOSED,
            self::FRAUD,
        ]);
    }

    /**
     * Get default status for new orders
     */
    public static function getDefault(): string
    {
        return self::PENDING;
    }
}
