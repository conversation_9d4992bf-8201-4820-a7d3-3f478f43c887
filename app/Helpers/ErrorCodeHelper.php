<?php

namespace App\Helpers;

use Exception;

class ErrorCodeHelper
{
    const string LOCALE_FILE = 'system_error.';
    const string MASTER_DATA_ERROR = 'MASTER_DATA_ERROR';
    const string VALIDATION_ERROR = 'VALIDATION_ERROR';
    const string GENERIC_ERROR = 'GENERIC_ERROR';
    const string MAINTENANCE_ERROR = 'MAINTENANCE_ERROR';
    const string REPORT_ERROR = 'REPORT_ERROR';
    const string USER_ERROR = 'USER_ERROR';
    const string INSTRUCTOR_ERROR = 'INSTRUCTOR_ERROR';

    const string NOTIFICATION_ERROR = 'NOTIFICATION_ERROR';
    const string ORDER_ERROR = 'ORDER_ERROR';

    // VERY THE VERY IMPORTANT: PLEASE ALWAYS KEEP /lang/{locale}/system_error.php UP TO DATE
    const array ERROR_CODES = [
        self::GENERIC_ERROR => [
            '403' => '403',   // forbidden
            '404' => '404',   // not found
        ],
        self::MASTER_DATA_ERROR => [
            '10001' => '10001', // This data cannot be deleted because it is being used.
        ],
        self::VALIDATION_ERROR => [
            '11001' => '11001', // The phone number field must be a valid phone number.
            '11002' => '11002', // The email has already been taken.
            '11003' => '11003', // The phone has already been taken.
        ],
        self::MAINTENANCE_ERROR => [
            '12001' => '12001', // Site is currently under maintenance.
            '12002' => '12002', // New app version available. Kindly update to the latest version.
        ],
        self::REPORT_ERROR => [
            '13001' => '13001', //Please define a report adapter first.
        ],
        self::USER_ERROR => [
            '14001' => '14001', // Email and password are required
            '14002' => '14002', // Invalid credentials
            '14003' => '14003', // User does not have required roles
            '14004' => '14004', // User must be validated first
        ],
        self::INSTRUCTOR_ERROR => [
            '15001' => '15001', // User is linked with another stylist before.
            '15002' => '15002', // Only user with Instructor role allowed to link.
        ],
        self::NOTIFICATION_ERROR => [
            '16001' => '16001', //SendPulse API credentials are missing.
        ],
        self::ORDER_ERROR => [
            '17001' => '17001', //Order product does not match.
            '17002' => '17002', //Only processing order are enable to link products.
        ],

    ];

    /**
     * @throws Exception
     */
    public static function throwError(string $category, int $code, array $translation_attribute = []): void
    {
        throw new Exception(self::getTranslatedErrorMessage($category, $code, $translation_attribute), $code);
    }

    public static function getTranslatedErrorMessage(string $category, string $code, array $translation_attribute = []): string
    {
        return trans(self::LOCALE_FILE . self::ERROR_CODES[$category][$code], $translation_attribute);
    }

    public static function getSentryIgnoredErrorCodes(): array
    {
        $ignored_error_codes = [];
        foreach (self::ERROR_CODES as $codes) {
            foreach ($codes as $code => $message) {
                $ignored_error_codes[] = $code;
            }
        }

        return $ignored_error_codes;
    }
}
