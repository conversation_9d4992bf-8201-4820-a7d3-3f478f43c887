<?php

namespace App\Helpers;


class SanitizesInput
{
    public static function sanitizePhoneNumber(string $phone): string
    {
        return preg_replace('/[^0-9+]/', '', $phone);
    }

    /**
     * Sanitize name (trim, proper case, remove extra spaces)
     *
     * @param string $name
     * @return string
     */
    public static function sanitizeName(string $name): string
    {
        // Trim whitespace
        $name = trim($name);

        // Remove multiple consecutive spaces
        $name = preg_replace('/\s+/', ' ', $name);

        // Convert to proper case (first letter of each word capitalized)
        return ucwords(strtolower($name));
    }

    /**
     * Sanitize email (trim and lowercase)
     *
     * @param string $email
     * @return string
     */
    public static function sanitizeEmail(string $email): string
    {
        return strtolower(trim($email));
    }
}
