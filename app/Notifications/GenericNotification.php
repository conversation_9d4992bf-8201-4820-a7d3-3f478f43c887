<?php

namespace App\Notifications;

use App\Channels\SmsChannel;
use App\Channels\WhatsAppChannel;
use App\Enums\NotificationChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class GenericNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected string $templateName;
    protected array $templateData;
    protected array $channels;
    protected array $content;
    protected array $channelContents;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $templateName, array $template_data = [], array $channels = [], array $content = [], array $channelContents = [])
    {
        $this->templateName = $templateName;
        $this->templateData = $template_data;
        $this->channels = $channels;
        $this->content = $content;
        $this->channelContents = $channelContents;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        $channels = [];

        foreach ($this->channels as $channel) {
            switch ($channel) {
                case NotificationChannel::MAIL:
                    $channels[] = 'mail';
                    break;
                case NotificationChannel::SMS:
                    $channels[] = SmsChannel::class;
                    break;
                case NotificationChannel::WHATSAPP:
                    $channels[] = WhatsAppChannel::class;
                    break;
            }
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject($this->content['subject'] ?? 'Notification')
            ->line(new HtmlString($this->content['content']) ?? '');
    }

    /**
     * Get the SMS representation of the notification.
     */
    public function toSms($notifiable): string
    {
        return $this->content['content'] ?? '';
    }

    /**
     * Get the WhatsApp representation of the notification.
     */
    public function toWhatsApp($notifiable): string
    {
        return $this->content['content'] ?? '';
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'template_name' => $this->templateName,
            'template_data' => $this->templateData,
            'content' => $this->content,
        ];
    }
}
