<?php

/**
 * Simple runner for UnionPay test
 * 
 * Usage: php run_unionpay_test.php
 */

echo "=== UnionPay API Integration Test Runner ===\n\n";

// Check if the main test file exists
if (!file_exists(__DIR__ . '/test_unionpay_complete.php')) {
    echo "Error: test_unionpay_complete.php not found!\n";
    exit(1);
}

// Include and run the test
try {
    require_once __DIR__ . '/test_unionpay_complete.php';
    
    echo "Test completed successfully!\n";
} catch (Exception $e) {
    echo "Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
