<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    'sendpulse' => [
        'client_id' => env('SENDPULSE_CLIENT_ID'),
        'client_secret' => env('SENDPULSE_CLIENT_SECRET'),
        'whatsapp_bot_id' => env('SENDPULSE_WHATSAPP_BOT_ID'),
        'sms_sender' => env('SENDPULSE_SMS_SENDER'),
    ],

    'autocount' => [
        'base_url' => env('AUTOCOUNT_BASE_URL'),
    ],
    'sitegiant' => [
        'base_url' => env('SITEGIANT_BASE_URL'),
        'secret_key' => env('SITEGIANT_SECRET_KEY'),
        'store_email' => env('SITEGIANT_STORE_EMAIL'),
        'partner_token' => env('SITEGIANT_PARTNER_TOKEN'),
        'webhook_token' => env('SITEGIANT_WEBHOOK_TOKEN'),
    ],

    'unionpay' => [
        'base_url' => env('UNIONPAY_BASE_URL', 'https://apigateway.unionpayintl.com'),
        'app_id' => env('UNIONPAY_APP_ID'),
        'send_party_iin' => env('UNIONPAY_SEND_PARTY_IIN'),
        'forwarding_iin' => env('UNIONPAY_FORWARDING_IIN'),
        'sign_cert_serial' => env('UNIONPAY_SIGN_CERT_SERIAL'),
        'enc_cert_serial' => env('UNIONPAY_ENC_CERT_SERIAL'),
        'upi_signing_cert_data' => env('UNIONPAY_UPI_SIGNING_CERT_DATA'),
        'upi_encryption_cert_data' => env('UNIONPAY_UPI_ENCRYPTION_CERT_DATA'),
        'institution_private_key' => env('UNIONPAY_INSTITUTION_PRIVATE_KEY'),
    ],

];
