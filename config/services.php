<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    'sendpulse' => [
        'client_id' => env('SENDPULSE_CLIENT_ID'),
        'client_secret' => env('SENDPULSE_CLIENT_SECRET'),
        'whatsapp_bot_id' => env('SENDPULSE_WHATSAPP_BOT_ID'),
        'sms_sender' => env('SENDPULSE_SMS_SENDER'),
    ],

    'autocount' => [
        'base_url' => env('AUTOCOUNT_BASE_URL'),
    ],
    'sitegiant' => [
        'base_url' => env('SITEGIANT_BASE_URL'),
        'secret_key' => env('SITEGIANT_SECRET_KEY'),
        'store_email' => env('SITEGIANT_STORE_EMAIL'),
        'partner_token' => env('SITEGIANT_PARTNER_TOKEN'),
        'webhook_token' => env('SITEGIANT_WEBHOOK_TOKEN'),
    ],

    'unionpay' => [
        'base_url' => env('UNIONPAY_BASE_URL', 'https://apigateway.unionpayintl.com'),
        'app_id' => env('UNIONPAY_APP_ID', '22410702'),
        'send_party_iin' => env('UNIONPAY_SEND_PARTY_IIN', '22410702'),
        'forwarding_iin' => env('UNIONPAY_FORWARDING_IIN', '22410702'),
        'sign_cert_serial' => env('UNIONPAY_SIGN_CERT_SERIAL', '1562032880608'),
        'enc_cert_serial' => env('UNIONPAY_ENC_CERT_SERIAL', '1562032885962'),
        'upi_signing_cert_data' => env('UNIONPAY_UPI_SIGNING_CERT_DATA', 'MIIC9jCCAd6gAwIBAgIGAWuwalDKMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNVBAYTAkNOMREwDwYDVQQHEwhTaGVuemhlbjEMMAoGA1UEChMDVVBJMQ0wCwYDVQQDEwRVQUdTMB4XDTE5MDcwMjAyMDEyNVoXDTI5MDcwMjAyMDEyNVowPTELMAkGA1UEBhMCQ04xETAPBgNVBAcTCFNoZW56aGVuMQwwCgYDVQQKEwNVUEkxDTALBgNVBAMTBFVBR1MwggEgMA0GCSqGSIb3DQEBAQUAA4IBDQAwggEIAoIBAQCS8W0X6grXDmE7zHBcvxUXYONBq8xI2DszFyuxV5mle5HZ48T0/nNS4+hxrHJcD1fQR+fYdU4YKJN+bZSHJ33nsIHEUuUM1a1iv7INXQqUj0INK76yF4wrDIOukIghvLramqapTBej6iOAL4cIAyr+C0PoLFYzgOl+XHkuxml5QO+ASS6icX/3+gKbhSC5jzt9Fw7AUBVYG/WsdWAwWDTyZszjxiUB9wCAzt9kM2ULdrbheN7wSTCfeh3+mMNdGPq3gXV+Rfm69Am5XXxD9xXPNGD4Y7oBEsNV3DwBFHY6rm8SZurl9ug8tVwnxdPu7WXMwysCejAbOxzrnU4RNteRAgEDMA0GCSqGSIb3DQEBCwUAA4IBAQBzKs96ucqwZG6oVRHFgNLAoEsX//Xy3To7xRDUDyXz597Dvyu0HL/7sFZuWug4uS93VT7acmBwBzEd1u/6qqLTdGoLprz/EDCPyYo1G4Goo0cBRfP6CoPAKOXuh1TT5b8cNGUnSlJIdCqBQyTX6Yx90k8QqfMGxRdG02+A2TGyltcEKOCcF1KRuYqX4OJP2EY5sMtSttGIyMMu0VFJQn8K15UO9bjnvYEtx5lHCjmIx4ukXr5fZMuGLb530fb4X2RcaO/6fNNdAeA+aAitqTmmHef0USj6I7QtXlNG5H/MQVI7v8im42468er7OnPyV4sJXo/GdFKFYD59BoyrgwVm'),
        'upi_encryption_cert_data' => env('UNIONPAY_UPI_ENCRYPTION_CERT_DATA', 'MIIC9jCCAd6gAwIBAgIGAWuwajvgMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNVBAYTAkNOMREwDwYDVQQHEwhTaGVuemhlbjEMMAoGA1UEChMDVVBJMQ0wCwYDVQQDEwRVQUdTMB4XDTE5MDcwMjAyMDEyMFoXDTI5MDcwMjAyMDEyMFowPTELMAkGA1UEBhMCQ04xETAPBgNVBAcTCFNoZW56aGVuMQwwCgYDVQQKEwNVUEkxDTALBgNVBAMTBFVBR1MwggEgMA0GCSqGSIb3DQEBAQUAA4IBDQAwggEIAoIBAQDPSTmjjBRH7HA9ZTVxtO5iWft5jrK9Yvk1nEIRTzcm6/yr7KANS5MGZZlW9621eFOUCdNgOR7LPcqJqlBcFguY5fgWhvdu227Xplz9gDrqOOzPECWtbW5/D0GOa2Hdt3ilqICSrnH+ogG3VtotDboyg5qR7IdOiUI/xJyV13sfTJRx+rud0sNTLi43KvkmWrKepAI8ax/o+XwWVJ7wD/cziA/89W6Y8lN7uv2U2rXiTu6eH2rk2Ljw9pZij0UCPqaZmDIqA9hH8ACb7V5gh8izYztUfwLs4auq4QcdTJ78w8LyHwyOzLXvfD2fFu/stSQ/Zi0Dft45DyCPD/TDFh9jAgEDMA0GCSqGSIb3DQEBCwUAA4IBAQBL5JgdZqSo+Iz4uIfNNjzKSWiA1lr8CPtCPUDbeqciJGy3KOldnAFFuMj/0kA2llHZiXGSxjm0+3+xiaexBahu/tQ3Hl2KgtvMeejV9pEvOblJL+8Ded6zJIP/GhbapySjgBFEMdCT+Lp2LRdghGXpNoBAuUeLhrHqIaApeC7txGrAYRAGO3HwaiZH5dCLu3wNPyiRO8bbQ2ogh2nF8HyYCQs4pzpvNlpWOEnntfZ2tUY7MDXe5hErg7vSpi8csi7uL1GpZWqYz+tJ8iMq+eT4Ipp/Rvv7yRL+10fvjRAJnNZi+iYmjn/DrlzwwAOuNtZrWv/0F2As0HPjp+frEczp'),
        'institution_private_key' => env('UNIONPAY_INSTITUTION_PRIVATE_KEY', ''),
    ],

];
