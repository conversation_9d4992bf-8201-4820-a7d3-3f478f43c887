{"name": "redq/marvel", "type": "project", "description": "Marvel, a complete e-commerce engine for both rest and graphql", "keywords": ["graphql", "rest-api"], "license": "MIT", "require": {"php": "^8.3", "barryvdh/laravel-dompdf": "2.0.1", "bensampo/laravel-enum": "*", "cviebrock/eloquent-sluggable": "*", "doctrine/dbal": "3.7.1", "giggsey/libphonenumber-for-php": "^9.0", "guzzlehttp/guzzle": "7.8.0", "kalnoy/nestedset": "^6.0", "laravel/framework": "^10.0", "laravel/sanctum": "^3.3", "laravel/socialite": "5.10.0", "laravel/tinker": "2.8.2", "league/flysystem-aws-s3-v3": "^3.29", "maatwebsite/excel": "^3.1", "messagebird/php-rest-api": "3.1.4", "mll-lab/graphql-php-scalars": "^6.4", "nuwave/lighthouse": "*", "prettus/l5-repository": "*", "psr/log": "3.0.0", "sendpulse/rest-api": "^2.0", "spatie/laravel-medialibrary": "^11.14", "spatie/laravel-permission": "^6.21", "stevebauman/purify": "6.0.2", "symfony/http-client": "^6.0", "symfony/mailgun-mailer": "6.3.6"}, "require-dev": {"fakerphp/faker": "1.21.0", "laravel/sail": "1.21.0", "mockery/mockery": "1.5.1", "pestphp/pest": "^2.36", "squizlabs/php_codesniffer": "3.7.2"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}