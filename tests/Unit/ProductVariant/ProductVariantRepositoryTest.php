<?php

use App\Models\Product;
use App\Models\ProductVariant;
use App\Repositories\ProductVariantRepository;

beforeEach(function () {
    $this->product = Product::factory()->create();
    $this->variant = ProductVariant::factory()->create(['product_id' => $this->product->id]);
    $this->variantRepository = app(ProductVariantRepository::class);
});

test('model', function () {
    expect($this->variantRepository->model())->toBe(ProductVariant::class);
});
