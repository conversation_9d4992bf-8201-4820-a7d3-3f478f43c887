<?php

use App\Models\Product;
use App\Models\ProductVariant;
use App\Repositories\ProductVariantRepository;
use App\Services\ProductVariantService;

beforeEach(function () {
    $this->productVariantService = app(ProductVariantService::class);
    $this->table = ProductVariant::class;
});

test('check repository', function () {
    expect($this->productVariantService->getRepository())->toBeInstanceOf(ProductVariantRepository::class);
});

test('check relationship', function () {
    $product = Product::factory()->create();
    $product_variant = ProductVariant::factory()->create([
        'product_id' => $product->id,
    ]);

    $product_variant_model = $this->productVariantService->setModel($product_variant)->getModel();

    expect($product_variant_model->toArray())->toHaveKey('product');
});
