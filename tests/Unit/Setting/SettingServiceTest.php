<?php

use App\Enums\SettingType;
use App\Models\Branch;
use App\Models\Setting;
use App\Repositories\SettingRepository;
use App\Services\SettingService;

beforeEach(function () {
    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    $this->settingService = app(SettingService::class);

    $this->table = Setting::class;
});

test('check repository', function () {
    expect($this->settingService->getRepository())->toBeInstanceOf(SettingRepository::class);
});

test('all', function () {
    Setting::factory()->create([
        'type' => SettingType::AUTOCOUNT,
        'options' => [
            'accounts' => [
                $this->branch1->id => [
                    'book_id' => 'book_1234',
                    'key_id' => 'key_1234',
                    'api_key' => 'api_1234',
                ],
            ],
        ],
    ]);

    $settings = Setting::all();

    cache()->forever('settings', $settings);

    $response = $this->settingService->all();

    expect($response[0])->toMatchArray([
        'type' => SettingType::AUTOCOUNT,
        'options' => [
            'accounts' => [
                $this->branch1->id => [
                    'book_id' => 'book_1234',
                    'key_id' => 'key_1234',
                    'api_key' => 'api_1234',
                ],
            ],
        ],
    ]);
});

test('cache', function () {
    Setting::factory()->create([
        'type' => SettingType::AUTOCOUNT,
        'options' => [
            'accounts' => [
                $this->branch1->id => [
                    'book_id' => 'book_1234',
                    'key_id' => 'key_1234',
                    'api_key' => 'api_1234',
                ],
            ],
        ],
    ]);

    $this->settingService->cache();

    $response = cache('settings');

    expect($response[0])->toMatchArray([
        'type' => SettingType::AUTOCOUNT,
        'options' => [
            'accounts' => [
                $this->branch1->id => [
                    'book_id' => 'book_1234',
                    'key_id' => 'key_1234',
                    'api_key' => 'api_1234',
                ],
            ],
        ],
    ]);
});
