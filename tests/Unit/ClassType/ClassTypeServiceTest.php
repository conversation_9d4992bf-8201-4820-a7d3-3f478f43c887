<?php

use App\Enums\ClassTypePriceType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\Category;
use App\Models\ClassType;
use App\Models\Product;
use App\Services\ClassTypeService;

beforeEach(function () {
    $this->classTypeService = app(ClassTypeService::class);
    $this->productTable = Product::class;
});

test('create product', function () {
    $class_type = ClassType::factory()->create([
        'name' => 'Yoga Flow',
        'description' => 'A dynamic yoga sequence',
        'duration_in_minutes' => 60,
        'price_type' => ClassTypePriceType::FIXED,
        'price' => "25.00",
        'colour' => '#96CEB4',
        'is_active' => true,
    ]);

    $category1 = Category::factory()->create(['name' => 'Fitness']);
    $category2 = Category::factory()->create(['name' => 'Yoga']);

    $this->assertDatabaseCount($this->productTable, 0);

    $this->classTypeService
        ->setModel($class_type)
        ->createProduct([$category1->id, $category2->id]);

    $this->assertDatabaseCount($this->productTable, 1);

    $this->assertDatabaseHas($this->productTable, [
        'productable_id' => $class_type->id,
        'productable_type' => ClassType::class,
        'name' => $class_type->name,
        'description' => $class_type->description,
        'price' => $class_type->price,
    ]);

    expect($class_type->product->categories)->toHaveCount(2)
        ->and($class_type->product->categories->pluck('id'))->toContain($category1->id, $category2->id);
});

test('update product', function () {
    $class_type = ClassType::factory()->create([
        'name' => 'Yoga Flow',
        'description' => 'A dynamic yoga sequence',
        'duration_in_minutes' => 60,
        'price_type' => ClassTypePriceType::FIXED,
        'price' => "25.00",
        'colour' => '#96CEB4',
        'is_active' => true,
    ]);

    $product = Product::factory()->create([
        'productable_id' => $class_type->id,
        'productable_type' => ClassType::class,
        'name' => 'ddd',
        'description' => 'eee',
        'price' => '12.00',
        'type' => ProductType::CLASS_TYPE,
        'status' => ProductStatus::PUBLISH, // is_active = true
    ]);

    $category1 = Category::factory()->create(['name' => 'Fitness']);
    $category2 = Category::factory()->create(['name' => 'Yoga']);

    $product->categories()->sync([$category1->id]);

    $this->assertDatabaseCount($this->productTable, 1);

    $this->classTypeService
        ->setModel($class_type)
        ->updateProduct([$category2->id]);

    $this->assertDatabaseCount($this->productTable, 1);

    $this->assertDatabaseHas($this->productTable, [
        'productable_id' => $class_type->id,
        'productable_type' => ClassType::class,
        'name' => $class_type->name,
        'description' => $class_type->description,
        'price' => $class_type->price,
    ]);

    expect($class_type->product->categories)->toHaveCount(1)
        ->and($class_type->product->categories->pluck('id'))->toContain($category2->id);
});
