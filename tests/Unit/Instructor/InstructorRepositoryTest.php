<?php

use App\Models\Branch;
use App\Models\Instructor;
use App\Models\User;
use App\Repositories\InstructorRepository;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);
    $this->branch3 = Branch::factory()->create([
        'name' => 'Test Branch 3',
        'active' => true,
    ]);

    $this->instructor = Instructor::factory()->create();

    $this->instructorRepository = app(InstructorRepository::class);
    $this->mediaTable = Media::class;
});

test('model', function () {
    $response = $this->instructorRepository->model();

    expect($response)->toEqual(Instructor::class);
});

test('sync user', function () {
    $user = User::factory()->create();
    $result = $this->instructorRepository->syncUser($this->instructor, $user);

    expect($result)->toBeInstanceOf(InstructorRepository::class)
        ->and($this->instructor->user_id)->toBe($user->id);
});

test('sync branches method syncs instructor branches', function () {
    $branches = [$this->branch1->id, $this->branch2->id];

    $result = $this->instructorRepository->syncBranches($this->instructor, $branches);

    expect($result)->toBeInstanceOf(InstructorRepository::class)
        ->and($this->instructor->branches()->count())->toBe(2)
        ->and($this->instructor->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);
});

test('sync branches removes old branches and adds new ones', function () {
    // Initially assign branches
    $this->instructor->branches()->attach([$this->branch1->id, $this->branch2->id]);
    expect($this->instructor->branches()->count())->toBe(2);

    // Sync with different branches (should remove old and add new)
    $this->instructorRepository->syncBranches($this->instructor, [$this->branch3->id]);

    expect($this->instructor->branches()->count())->toBe(1)
        ->and($this->instructor->branches->first()->id)->toBe($this->branch3->id)
        ->and($this->instructor->branches->pluck('id')->toArray())->not()->toContain($this->branch1->id, $this->branch2->id);
});

test('sync branches works with empty branches array', function () {
    // Initially assign some branches
    $this->instructor->branches()->attach([$this->branch1->id, $this->branch2->id]);
    expect($this->instructor->branches()->count())->toBe(2);

    // Sync with empty array (should remove all branches)
    $result = $this->instructorRepository->syncBranches($this->instructor, []);

    expect($result)->toBeInstanceOf(InstructorRepository::class)
        ->and($this->instructor->branches()->count())->toBe(0);
});
