<?php

use App\Enums\Role as RoleEnum;
use App\Models\User;
use App\Repositories\UserRepository;
use App\Services\AuthService;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles for testing
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);

    $this->authService = app(AuthService::class);
});

test('check repository', function () {
    expect($this->authService->getRepository())->toBeInstanceOf(UserRepository::class);
});

test('validateCredentials validates correct credentials', function () {
    User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => true,
    ]);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $result = $this->authService
        ->setCredentials($credentials)
        ->validateCredentials();

    expect($result)->toBeInstanceOf(AuthService::class)
        ->and($result->getUser())->toBeInstanceOf(User::class)
        ->and($result->getUser()->email)->toBe('<EMAIL>');
});

test('validateCredentials throws exception for invalid email', function () {
    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    expect(function () use ($credentials) {
        $this->authService
            ->setCredentials($credentials)
            ->validateCredentials();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(14002);
    }, 'Invalid credentials.');
});

test('validateCredentials throws exception for invalid password', function () {
    User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'correctpassword', // Factory will hash this
        'is_active' => true,
    ]);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'wrongpassword',
    ];

    expect(function () use ($credentials) {
        $this->authService
            ->setCredentials($credentials)
            ->validateCredentials();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(14002);
    }, 'Invalid credentials.');
});

test('validateCredentials throws exception for inactive user', function () {
    User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => false,
    ]);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    expect(function () use ($credentials) {
        $this->authService
            ->setCredentials($credentials)
            ->validateCredentials();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(14002);
    }, 'Invalid credentials.');
});

test('checkRolePermissions allows login when no required roles set', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => true,
    ]);
    $user->assignRole(RoleEnum::CUSTOMER);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $result = $this->authService
        ->setCredentials($credentials)
        ->setRequiredRoles([])
        ->validateCredentials()
        ->checkRolePermissions();

    expect($result)->toBeInstanceOf(AuthService::class);
});

test('checkRolePermissions allows login when user has required role', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => true,
    ]);
    $user->assignRole(RoleEnum::STAFF);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $result = $this->authService
        ->setCredentials($credentials)
        ->setRequiredRoles([RoleEnum::STAFF, RoleEnum::INSTRUCTOR])
        ->validateCredentials()
        ->checkRolePermissions();

    expect($result)->toBeInstanceOf(AuthService::class);
});

test('checkRolePermissions throws exception when user lacks required role', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => true,
    ]);
    $user->assignRole(RoleEnum::CUSTOMER);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    expect(function () use ($credentials) {
        $this->authService
            ->setCredentials($credentials)
            ->setRequiredRoles([RoleEnum::STAFF, RoleEnum::INSTRUCTOR])
            ->validateCredentials()
            ->checkRolePermissions();
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(14003);
    }, 'User does not have required roles');
});

test('generateToken creates token for authenticated user', function () {
    User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => true,
    ]);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $result = $this->authService
        ->setCredentials($credentials)
        ->validateCredentials()
        ->generateToken();

    expect($result)->toBeInstanceOf(AuthService::class);

    $tokenData = $result->getTokenData();
    expect($tokenData)->toHaveKey('token')
        ->and($tokenData)->toHaveKey('token_type')
        ->and($tokenData['token_type'])->toBe('Bearer')
        ->and($tokenData['token'])->toBeString();
});

test('generateToken throws exception when no user is set', function () {
    expect(fn() => $this->authService->generateToken())
        ->toThrow(Error::class);
});

test('full authentication flow works correctly', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => 'password123', // Factory will hash this
        'is_active' => true,
    ]);
    $user->assignRole(RoleEnum::STAFF);

    $credentials = [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $tokenData = $this->authService
        ->setCredentials($credentials)
        ->setRequiredRoles([RoleEnum::STAFF])
        ->validateCredentials()
        ->checkRolePermissions()
        ->generateToken()
        ->getTokenData();

    expect($tokenData)->toHaveKey('token')
        ->and($tokenData)->toHaveKey('token_type')
        ->and($tokenData['token_type'])->toBe('Bearer')
        ->and($tokenData['token'])->toBeString();
});
