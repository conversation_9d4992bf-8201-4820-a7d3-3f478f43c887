<?php

use App\Models\User;
use App\Services\UserService;

beforeEach(function () {
    $this->user = User::factory()->create([
        'firstname' => 'Jane',
        'lastname' => 'Smith',
        'email' => '<EMAIL>',
    ]);

    $this->userService = app(UserService::class);

    $this->table = User::class;
});

describe('find via', function () {
    test('with empty key', function () {
        $user = $this->userService->findVia($this->user->id); //default will use id

        expect($user->id)->toBe($this->user->id);
    });

    test('with firstname key', function () {
        $user = $this->userService->findVia($this->user->firstname, 'firstname');

        expect($user->firstname)->toBe($this->user->firstname);
    });
});

test('all', function () {
    $users = $this->userService->all();

    expect($users)->toHaveCount(1)
        ->and($users->first()->toArray())->toMatchArray($this->user->toArray());
});


test('store', function () {
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'password' => bcrypt('password123'),
        'is_active' => true,
    ];

    $this->assertDatabaseCount($this->table, 1);

    $response = $this->userService->store($data);

    expect($response->getModel())->toMatchArray([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);

    $this->assertDatabaseCount($this->table, 2);
    $this->assertDatabaseHas($this->table, [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
});

test('update', function () {
    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'firstname' => 'Johnny',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ];

    $response = $this->userService
        ->setModel($this->user)
        ->update($data);

    expect($response->getModel())->toMatchArray($data);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, array_merge(['id' => $this->user->id], $data));
});

test('delete', function () {
    $this->assertDatabaseCount($this->table, 1);

    $this->userService
        ->setModel($this->user)
        ->delete();

    // User is soft deleted, so record still exists but is marked as deleted
    $this->assertDatabaseCount($this->table, 1);
    $this->assertSoftDeleted($this->table, ['id' => $this->user->id]);
});

describe('update or create', function () {
    test('with exist email', function () {
        $this->assertDatabaseCount($this->table, 1);

        $data = [
            'firstname' => 'Johnny',
            'lastname' => 'Doe',
        ];

        $response = $this->userService
            ->setModel($this->user)
            ->updateOrCreate(['email' => '<EMAIL>'], $data);

        expect($response->getModel())->toMatchArray([
            'id' => $this->user->id,
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseCount($this->table, 1);
        $this->assertDatabaseHas($this->table, [
            'id' => $this->user->id,
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => '<EMAIL>',
        ]);
    });

    test('with new email', function () {
        $this->assertDatabaseCount($this->table, 1);

        $data = [
            'firstname' => 'Johnny',
            'lastname' => 'Doe',
        ];

        $response = $this->userService
            ->setModel($this->user)
            ->updateOrCreate(['email' => '<EMAIL>'], $data);

        expect($response->getModel())->toMatchArray([
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseCount($this->table, 2);
        $this->assertDatabaseHas($this->table, [
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => '<EMAIL>',
        ]);
    });
});

test('set model', function () {
    $this->userService->setModel($this->user);

    $user = $this->userService->getModel();

    expect($user->toArray())->toMatchArray($this->user->toArray());
});

test('set model by id', function () {
    $this->userService->setModelById($this->user->id);

    $user = $this->userService->getModel();

    expect($user->toArray())->toMatchArray($this->user->toArray());
});

test('get model', function () {
    $this->userService->setModel($this->user);

    $user = $this->userService->getModel();

    expect($user->toArray())->toMatchArray($this->user->toArray());
});
