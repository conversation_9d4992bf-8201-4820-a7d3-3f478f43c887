<?php

use App\Models\PaymentGateway;
use App\Models\PaymentMethod;
use App\Repositories\PaymentMethodRepository;
use App\Services\PaymentMethodService;

beforeEach(function () {
    $this->paymentMethodService = app(PaymentMethodService::class);

    $this->table = PaymentMethod::class;

    $this->paymentGateway = PaymentGateway::factory()->create();
    $this->paymentGateway2 = PaymentGateway::factory()->create();
});

test('check repository', function () {
    expect($this->paymentMethodService->getRepository())->toBeInstanceOf(PaymentMethodRepository::class);
});
