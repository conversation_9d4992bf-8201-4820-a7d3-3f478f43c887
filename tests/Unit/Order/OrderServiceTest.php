<?php

use App\Enums\OrderStatus as OrderStatusEnum;
use App\Enums\SettingType;
use App\Models\Branch;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\Setting;
use App\Models\User;
use App\Repositories\OrderRepository;
use App\Services\OrderService;
use Database\Seeders\OrderStatusSeeder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $this->seed([
        OrderStatusSeeder::class,
    ]);

    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    $this->orderService = app(OrderService::class);

    $this->table = Order::class;

    $this->customer = User::factory()->create();
    $this->customer2 = User::factory()->create();

    $this->orderStatus = OrderStatus::factory()->create();
    $this->orderStatus2 = OrderStatus::factory()->create();

    Setting::factory()->create([
        'type' => SettingType::AUTOCOUNT,
        'options' => [
            'accounts' => [
                $this->branch1->id => [
                    'book_id' => 'book123',
                    'api_key' => 'test-api-key',
                    'key_id' => 'test-key-id',
                    'debtor_code' => 'test-debtor-code',
                    'debtor_name' => 'test-debtor-name',
                    'credit_term' => 'test-credit-terms',
                    'sales_location' => 'test-sales-location',
                    'purchase_account_number' => 'test-purchase-account-number',
                ],
            ],
        ],

    ]);
    // Set up AutoCount configuration for testing
    Config::set('services.autocount', [
        'base_url' => 'https://autocount-api.example.com',
    ]);

    Http::fake([
        'https://autocount-api.example.com/book12*/invoice' => Http::response('ok'),
    ]);
});

test('check repository', function () {
    expect($this->orderService->getRepository())->toBeInstanceOf(OrderRepository::class);
});

test('sync branches', function () {
    $order = Order::factory()->create();

    $branches = [$this->branch1->id, $this->branch2->id];

    $response = $this->orderService
        ->setModel($order)
        ->syncBranches($branches);

    expect($response)->toBeInstanceOf(OrderService::class)
        ->and($order->branches()->count())->toBe(2)
        ->and($order->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);

    // Test updating branches (removing one, keeping one)
    $updated_branches = [$this->branch1->id];

    $this->orderService
        ->setModel($order)
        ->syncBranches($updated_branches);

    $order->refresh();

    expect($order->branches()->count())->toBe(1)
        ->and($order->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

test('sync products', function () {
    $product = Product::factory()->create();
    $product2 = Product::factory()->create();

    $order = Order::factory()->create();

    $product_data = [
        [
            'sku' => 'sku1',
            'product_id' => $product->id,
            'name' => 'product1',
            'order_quantity' => 1,
            'invoiced_quantity' => 2,
            'unit_price' => 3.20,
            'tax' => 3.00,
            'subtotal' => 9.00,
        ],
        [
            'sku' => 'sku2',
            'product_id' => $product2->id,
            'name' => 'product2',
            'order_quantity' => 2,
            'invoiced_quantity' => 3,
            'unit_price' => 3.40,
            'tax' => 3.50,
            'subtotal' => 19.00,
        ],
    ];

    $this->orderService->setModel($order)
        ->syncProducts($product_data);

    $order->refresh();

    expect($order->products()->count())->toBe(2)
        ->and($order->products[0]->toArray())->toMatchArray($product_data[0])
        ->and($order->products[1]->toArray())->toMatchArray($product_data[1]);

    //sync new product will remove old one
    $product_data = [
        [
            'sku' => 'sku3',
            'product_id' => null,
            'name' => 'product3',
            'order_quantity' => 12,
            'invoiced_quantity' => 21,
            'unit_price' => 3.30,
            'tax' => 4.00,
            'subtotal' => 5.00,
        ],
    ];

    $this->orderService->setModel($order)
        ->syncProducts($product_data);

    $order->refresh();

    expect($order->products()->count())->toBe(1)
        ->and($order->products[0]->toArray())->toMatchArray($product_data[0]);
});

test('link products', function () {
    $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

    $order = Order::factory()->create([
        'order_status_id' => $processing_status->id,
    ]);

    $order->branches()->sync([$this->branch1->id]);

    $order_product = OrderProduct::factory()->create([
        'product_id' => null,
        'order_id' => $order->id,
    ]);

    OrderProduct::factory()->create([
        'product_id' => null,
        'order_id' => $order->id,
    ]);

    $product = Product::factory()->create();

    $data = [
        [
            'id' => $order_product->id,
            'product_id' => $product->id,
        ],
    ];

    $this->orderService->setModel($order)
        ->linkProducts($data);

    //order product link with product
    $this->assertDatabaseHas(OrderProduct::class, [
        'id' => $order_product->id,
        'product_id' => $product->id,
        'sku' => $product->sku,
    ]);
});

describe('validate order products', function () {
    test('validate order status', function () {
        $order_statuses = OrderStatusEnum::getValues();
        $product = Product::factory()->create();

        foreach ($order_statuses as $order_status) {
            $status = OrderStatus::query()->where('slug', $order_status)->first();

            $order = Order::factory()->create([
                'order_status_id' => $status->id,
            ]);

            $order_product = OrderProduct::factory()->create([
                'product_id' => null,
                'order_id' => $order->id,
            ]);

            $data = [
                [
                    'id' => $order_product->id,
                    'product_id' => $product->id,
                ],
            ];

            switch ($order_status) {
                case OrderStatusEnum::PROCESSING:
                case OrderStatusEnum::PACKED:
                    $response = $this->orderService->setModel($order)
                        ->validateOrderProducts($data);

                    expect($response)->toBeInstanceOf(OrderService::class);
                    break;
                default:
                    expect(function () use ($order, $data) {
                        $this->orderService->setModel($order)
                            ->validateOrderProducts($data);
                    })->toThrow(function (Exception $e) {
                        expect($e->getCode())->toBe(17002);
                    }, 'Only processing order are enable to link products.');
            }
        }
    });

    test('validate order product exist', function () {
        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order2 = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order_product = OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order2->id,
        ]);

        $product = Product::factory()->create();

        $data = [
            [
                'id' => $order_product->id,
                'product_id' => $product->id,
            ],
        ];

        expect(function () use ($order, $data) {
            $this->orderService->setModel($order)
                ->validateOrderProducts($data);
        })->toThrow(function (Exception $e) {
            expect($e->getCode())->toBe(17001);
        }, 'Order product does not match.');
    });

    test('validate order products must be exact as database', function () {
        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order_product = OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order->id,
        ]);

        OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order->id,
        ]);

        $product = Product::factory()->create();

        $data = [
            [
                'id' => $order_product->id,
                'product_id' => $product->id,
            ],
        ];

        expect(function () use ($order, $data) {
            $this->orderService->setModel($order)
                ->validateOrderProducts($data);
        })->toThrow(function (Exception $e) {
            expect($e->getCode())->toBe(17001);
        }, 'Order product does not match.');
    });

    test('update status', function () {
        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();
        $packed_status = OrderStatus::query()->where('slug', OrderStatusEnum::PACKED)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $this->orderService->setModel($order)
            ->updateStatus(OrderStatusEnum::PACKED);

        $this->assertDatabaseHas($this->table, [
            'id' => $order->id,
            'order_status_id' => $packed_status->id,
        ]);
    });
});

test('create autocount invoice', function () {
    $packed_status = OrderStatus::query()->where('slug', OrderStatusEnum::PACKED)->first();
    $complete_status = OrderStatus::query()->where('slug', OrderStatusEnum::COMPLETE)->first();

    $order = Order::factory()->create([
        'order_status_id' => $packed_status->id,
    ]);

    $order->branches()->sync([$this->branch1->id]);

    $product = Product::factory()->create();

    OrderProduct::factory()->create([
        'product_id' => $product->id,
        'order_id' => $order->id,
        'sku' => $product->sku,
    ]);

    $this->orderService->setModel($order)
        ->createAutoCountInvoice();

    //order status updated to complete
    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'order_status_id' => $complete_status->id,
    ]);
});
