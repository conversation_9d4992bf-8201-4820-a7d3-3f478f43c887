<?php

use App\Models\Branch;
use App\Models\User;
use App\Repositories\BranchRepository;

beforeEach(function () {
    $this->branchRepository = app(BranchRepository::class);
    $this->table = Branch::class;
});

test('model', function () {
    $response = $this->branchRepository->model();

    expect($response)->toEqual(Branch::class);
});

test('hasAssociatedModels returns false when branch has no associations', function () {
    $branch = Branch::factory()->create([
        'name' => 'Branch without associations',
    ]);

    $result = $this->branchRepository->hasAssociatedModels($branch);

    expect($result)->toBeFalse();
});

test('hasAssociatedModels returns true when branch has associated users', function () {
    $branch = Branch::factory()->create([
        'name' => 'Branch with users',
    ]);

    $user = User::factory()->create();

    // Associate user with branch
    $branch->users()->attach($user->id);

    $result = $this->branchRepository->hasAssociatedModels($branch);

    expect($result)->toBeTrue();
});

test('hasAssociatedModels returns false after removing all associations', function () {
    $branch = Branch::factory()->create([
        'name' => 'Branch with temporary associations',
    ]);

    $user = User::factory()->create();

    // Associate user with branch
    $branch->users()->attach($user->id);

    // Verify association exists
    expect($this->branchRepository->hasAssociatedModels($branch))->toBeTrue();

    // Remove association
    $branch->users()->detach($user->id);

    // Verify no associations remain
    expect($this->branchRepository->hasAssociatedModels($branch))->toBeFalse();
});

test('hasAssociatedModels returns true when branch has multiple associated users', function () {
    $branch = Branch::factory()->create([
        'name' => 'Branch with multiple users',
    ]);

    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    // Associate multiple users with branch
    $branch->users()->attach([$user1->id, $user2->id]);

    $result = $this->branchRepository->hasAssociatedModels($branch);

    expect($result)->toBe(true)
        ->and($branch->users()->count())->toBe(2);
});

test('hasAssociatedModels works with fresh branch instance', function () {
    $branch = Branch::factory()->create([
        'name' => 'Branch for fresh instance test',
    ]);

    $user = User::factory()->create();

    // Associate user with branch
    $branch->users()->attach($user->id);

    // Get fresh branch instance (simulating route model binding)
    $branch->refresh();

    $result = $this->branchRepository->hasAssociatedModels($branch);

    expect($result)->toBeTrue();
});
