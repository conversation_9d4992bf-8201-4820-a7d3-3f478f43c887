<?php

use App\Models\Branch;
use App\Models\BranchDetails;
use App\Models\User;
use App\Repositories\BranchRepository;
use App\Services\BranchService;
use Illuminate\Http\UploadedFile;

beforeEach(function () {
    $this->branchService = app(BranchService::class);
    $this->table = Branch::class;
    $this->detailsTable = BranchDetails::class;
});

test('check repository', function () {
    expect($this->branchService->getRepository())->toBeInstanceOf(BranchRepository::class);
});

test('check relationship', function () {
    $branch = Branch::factory()->create();
    BranchDetails::factory()->create([
        'branch_id' => $branch->id,
    ]);

    $branch_model = $this->branchService->setModel($branch)->getModel();

    expect($branch_model->toArray())->toHaveKey('details');
});

describe('store', function () {
    test('generates slug from name when not provided', function () {
        $data = [
            'name' => 'Downtown Branch',
            'active' => true,
        ];

        $response = $this->branchService->store($data);

        expect($response->getModel()->slug)->toBe('downtown-branch');

        $this->assertDatabaseHas($this->table, [
            'name' => 'Downtown Branch',
            'slug' => 'downtown-branch',
        ]);
    });

    test('generates unique slug when duplicate exists', function () {
        // Create first branch with slug
        Branch::factory()->create([
            'name' => 'Unique Test Branch',
            'slug' => 'unique-test-branch',
        ]);

        $data = [
            'name' => 'Unique Test Branch',
            'active' => true,
        ];

        $response = $this->branchService->store($data);

        // The slug should be unique and different from the original
        expect($response->getModel()->slug)->not()->toBe('unique-test-branch')
            ->and($response->getModel()->slug)->toStartWith('unique-test-branch-');

        // Verify database has the new unique slug
        $this->assertDatabaseHas($this->table, [
            'name' => 'Unique Test Branch',
            'slug' => $response->getModel()->slug,
        ]);
    });
});

describe('update', function () {
    test('generates new slug when name changes', function () {
        $branch = Branch::factory()->create([
            'name' => 'Old Branch',
            'slug' => 'old-branch',
        ]);

        $data = [
            'name' => 'New Branch Name',
            'active' => true,
        ];

        $response = $this->branchService
            ->setModel($branch)
            ->update($data);

        expect($response->getModel()->slug)->toBe('new-branch-name');

        $this->assertDatabaseHas($this->table, [
            'id' => $branch->id,
            'name' => 'New Branch Name',
            'slug' => 'new-branch-name',
        ]);
    });

    test('does not change slug when name unchanged', function () {
        $branch = Branch::factory()->create([
            'name' => 'Same Branch',
            'slug' => 'same-branch',
        ]);

        $data = [
            'name' => 'Same Branch',
            'active' => false,
        ];

        $response = $this->branchService
            ->setModel($branch)
            ->update($data);

        expect($response->getModel()->slug)->toBe('same-branch');

        $this->assertDatabaseHas($this->table, [
            'id' => $branch->id,
            'name' => 'Same Branch',
            'slug' => 'same-branch',
            'active' => false,
        ]);
    });
});

describe('delete', function () {
    test('removes branch when no associated models exist', function () {
        $branch = Branch::factory()->create([
            'name' => 'Delete Me',
        ]);

        $this->assertDatabaseCount($this->table, 1);

        $this->branchService
            ->setModel($branch)
            ->delete();

        $this->assertDatabaseCount($this->table, 0);
        $this->assertDatabaseMissing($this->table, ['id' => $branch->id]);
    });

    test('prevents deletion when branch has associated users', function () {
        $branch = Branch::factory()->create([
            'name' => 'Branch with Users',
        ]);

        $user = User::factory()->create();

        // Associate user with branch using the morphToMany relationship
        $branch->users()->attach($user->id);

        // Attempt to delete should throw an exception
        expect($branch->users()->count())->toBe(1)
            ->and(fn() => $this->branchService->setModel($branch)->delete())
            ->toThrow(function (Exception $e) {
                expect($e->getCode())->toBe(10001);
            }, 'This data cannot be deleted because it is being used.');


        // Verify branch still exists
        $this->assertDatabaseHas($this->table, ['id' => $branch->id]);
    });


    test('allows deletion after removing all associated models', function () {
        $branch = Branch::factory()->create([
            'name' => 'Branch to Clean and Delete',
        ]);

        $user = User::factory()->create();

        // Associate user with branch
        $branch->users()->attach($user->id);

        // Attempt to delete should fail
        expect($branch->users()->count())->toBe(1)
            ->and(fn() => $this->branchService->setModel($branch)->delete())
            ->toThrow(function (Exception $e) {
                expect($e->getCode())->toBe(10001);
            }, 'This data cannot be deleted because it is being used.');

        // Remove the associated user
        $branch->users()->detach($user->id);

        // Now deletion should succeed
        $this->branchService->setModel($branch)->delete();

        $this->assertDatabaseMissing($this->table, ['id' => $branch->id]);
    });
});

describe('sync details', function () {
    test('creates new details', function () {
        $branch = Branch::factory()->create();

        $data = [
            'company_name' => 'Test Company Ltd',
            'company_registration_no' => '1234567890',
            'company_address' => '123 Test Street',
            'company_phone' => '+1234567890',
            'company_email' => '<EMAIL>',
        ];

        $response = $this->branchService
            ->setModel($branch)
            ->syncDetails($data);

        expect($response)->toBeInstanceOf(BranchService::class);

        $this->assertDatabaseHas($this->detailsTable, array_merge([
            'branch_id' => $branch->id,
        ], $data));
    });

    test('updates existing details', function () {
        $branch = Branch::factory()->withDetails()->create();
        $existingDetails = $branch->details;

        $data = [
            'company_name' => 'Updated Company Ltd',
            'company_registration_no' => '0987654321',
            'company_address' => '456 Updated Street',
            'company_phone' => '+0987654321',
            'company_email' => '<EMAIL>',
        ];

        $response = $this->branchService
            ->setModel($branch)
            ->syncDetails($data);

        expect($response)->toBeInstanceOf(BranchService::class);

        // Should still be only one details record
        $this->assertDatabaseCount($this->detailsTable, 1);

        $this->assertDatabaseHas($this->detailsTable, array_merge([
            'id' => $existingDetails->id,
            'branch_id' => $branch->id,
        ], $data));
    });
});

test('sync media', function () {
    $branch = Branch::factory()->create();

    $file = UploadedFile::fake()->image('test-branch.png');

    // Use new simplified media structure
    $media_data = [
        'image' => [
            [
                'file' => $file,
                'order_column' => 1
            ]
        ]
    ];

    $result = $this->branchService
        ->setModel($branch)
        ->syncMedia($media_data);

    expect($result)->toBeInstanceOf(BranchService::class);

    // Verify media record was created in database
    $this->assertDatabaseHas('media', [
        'model_type' => Branch::class,
        'model_id' => $branch->id,
        'collection_name' => 'image',
        'file_name' => 'test-branch.png'
    ]);
});
