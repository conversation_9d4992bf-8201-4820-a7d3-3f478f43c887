<?php

use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Repositories\OrderProductRepository;
use App\Services\OrderProductService;

beforeEach(function () {
    $this->orderProductService = app(OrderProductService::class);

    $this->table = OrderProduct::class;

    $this->customer = User::factory()->create();
    $this->customer2 = User::factory()->create();

    $this->order = Order::factory()->create();
    $this->order2 = Order::factory()->create();

    $this->orderStatus = OrderStatus::factory()->create();
    $this->orderStatus2 = OrderStatus::factory()->create();

    $this->product1 = Product::factory()->create();
    $this->product2 = Product::factory()->create();

    $this->productVariant1 = ProductVariant::factory()->create();
    $this->productVariant2 = ProductVariant::factory()->create();
});

test('check repository', function () {
    expect($this->orderProductService->getRepository())->toBeInstanceOf(OrderProductRepository::class);
});
