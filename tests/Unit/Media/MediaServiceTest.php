<?php

use App\Models\Product;
use App\Services\MediaService;
use Illuminate\Http\UploadedFile;

beforeEach(function () {
    $this->mediaService = new MediaService();

    // Configure filesystem for testing
    config(['filesystems.default' => 'public']);

    // Create a real product model that implements HasMedia
    $this->product = Product::factory()->create();
});

test('handleMedia function creates and deletes media correctly', function () {


    // Create existing media to test delete
    $existingFile = UploadedFile::fake()->image('existing.png');
    $existingMedia = $this->product->addMedia($existingFile)
        ->toMediaCollection('gallery');

    $newFile = UploadedFile::fake()->image('new.png');

    $media_data = [
        'gallery' => [
            [
                'file' => $newFile,
                'order_column' => 1,
            ],
        ],
        'delete' => [$existingMedia->id],
    ];

    $this->mediaService->handleMedia($this->product, $media_data);

    // Assert old media deleted and new media created
    $allMedia = $this->product->fresh()->getMedia('gallery');
    expect($allMedia)->toHaveCount(1);
    expect($allMedia->first()->order_column)->toBe(1);
    expect($allMedia->first()->id)->not->toBe($existingMedia->id);
});

test('createMedia function creates media with order column', function () {


    $file = UploadedFile::fake()->image('test.png');
    $media_items = [
        [
            'file' => $file,
            'order_column' => 5,
        ],
    ];

    // Use reflection to test protected method
    $reflection = new ReflectionClass($this->mediaService);
    $method = $reflection->getMethod('createMedia');
    $method->setAccessible(true);

    $method->invoke($this->mediaService, $this->product, $media_items, 'gallery');

    $media = $this->product->getMedia('gallery')->first();
    expect($media)->not->toBeNull();
    expect($media->order_column)->toBe(5);
    expect($media->collection_name)->toBe('gallery');
});

test('deleteMediaByIds function deletes media by IDs', function () {


    // Create multiple media items
    $file1 = UploadedFile::fake()->image('image1.png');
    $file2 = UploadedFile::fake()->image('image2.png');

    $media1 = $this->product->addMedia($file1)
        ->toMediaCollection('gallery');
    $media2 = $this->product->addMedia($file2)
        ->toMediaCollection('image');

    expect($this->product->getMedia("*"))->toHaveCount(2);

    // Use reflection to test protected method
    $reflection = new ReflectionClass($this->mediaService);
    $method = $reflection->getMethod('deleteMediaByIds');
    $method->setAccessible(true);

    $method->invoke($this->mediaService, $this->product, [$media1->id, $media2->id]);

    expect($this->product->fresh()->getMedia())->toHaveCount(0);
});
