<?php

use App\Models\ExportHistory;
use App\Repositories\ExportHistoryRepository;
use App\Services\ExportHistoryService;

beforeEach(function () {
    $this->exportHistoryService = app(ExportHistoryService::class);

    $this->table = ExportHistory::class;
});

test('check repository', function () {
    expect($this->exportHistoryService->getRepository())->toBeInstanceOf(ExportHistoryRepository::class);
});
