<?php

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductBundleItem;
use App\Models\ProductVariant;
use App\Models\Warehouse;
use App\Repositories\ProductRepository;
use App\Services\ProductService;
use Illuminate\Http\UploadedFile;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->productService = app(ProductService::class);
    $this->table = Product::class;
    $this->variantTable = ProductVariant::class;
    $this->mediaTable = Media::class;
    $this->bundleItemTable = ProductBundleItem::class;
});

test('check repository', function () {
    expect($this->productService->getRepository())->toBeInstanceOf(ProductRepository::class);
});

test('check relationship', function () {
    $product = Product::factory()->create();
    ProductVariant::factory()->create(['product_id' => $product->id]);
    ProductBundleItem::factory()->create(['bundle_product_id' => $product->id]);
    $warehouse = Warehouse::factory()->create();
    $product->warehouses()->sync($warehouse->id);

    $product_model = $this->productService->setModel($product)->getModel();
    expect($product_model->toArray())->toHaveKeys(['variants', 'bundle_items', 'warehouses']);
});

describe('sync variants', function () {
    test('sync new variants', function () {
        // Create a product first
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        $variants = [
            [
                'title' => 'Small',
                'price' => '50.00',
                'sku' => 'PROD-SMALL',
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'sku' => 'PROD-LARGE',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were created in database
        $this->assertDatabaseCount($this->variantTable, 2);
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Small',
            'price' => '50.00',
            'sku' => 'PROD-SMALL',
        ]);
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Large',
            'price' => '80.00',
            'sku' => 'PROD-LARGE',
        ]);
    });

    test('sync existing variants', function () {
        $product = Product::factory()->create();

        // Create existing variants
        $variant1 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Small',
            'price' => '40.00',
        ]);
        $variant2 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Large',
            'price' => '70.00',
        ]);

        $variants = [
            [
                'id' => $variant1->id,
                'title' => 'Updated Small',
                'price' => '55.00',
                'sku' => 'PROD-SMALL-UPDATED',
            ],
            [
                'id' => $variant2->id,
                'title' => 'Updated Large',
                'price' => '85.00',
                'sku' => 'PROD-LARGE-UPDATED',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were updated
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant1->id,
            'title' => 'Updated Small',
            'price' => '55.00',
            'sku' => 'PROD-SMALL-UPDATED',
        ]);
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant2->id,
            'title' => 'Updated Large',
            'price' => '85.00',
            'sku' => 'PROD-LARGE-UPDATED',
        ]);
    });

    test('sync new and existing variants', function () {
        $product = Product::factory()->create();

        // Create one existing variant
        $existing_variant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Existing Medium',
            'price' => '60.00',
        ]);

        $variants = [
            [
                'id' => $existing_variant->id,
                'title' => 'Updated Medium',
                'price' => '65.00',
            ],
            [
                'title' => 'New Extra Large',
                'price' => '100.00',
                'sku' => 'PROD-XL',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify existing variant was updated
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $existing_variant->id,
            'title' => 'Updated Medium',
            'price' => '65.00',
        ]);

        // Verify new variant was created
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'New Extra Large',
            'price' => '100.00',
            'sku' => 'PROD-XL',
        ]);

        $this->assertDatabaseCount($this->variantTable, 2);
    });

    test('sync empty variants', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->syncVariants([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        // No variants should be created
        $this->assertDatabaseCount($this->variantTable, 0);
    });
});


describe('sync bundle items', function () {
    test('creates new bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        $this->assertDatabaseCount($this->bundleItemTable, 0);

        $items = [
            ['item_product_id' => $item1->id, 'quantity' => 2],
            ['item_product_id' => $item2->id, 'quantity' => 3],
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify bundle items were created
        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 3,
        ]);
    });

    test('updates existing bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 2);

        // Update with new quantities
        $items = [
            ['item_product_id' => $item1->id, 'quantity' => 5],
            ['item_product_id' => $item2->id, 'quantity' => 7],
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify quantities were updated
        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 5,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 7,
        ]);
    });

    test('deletes removed bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 3);

        // Sync with only item1 and item2 (item3 should be deleted)
        $items = [
            ['item_product_id' => $item1->id, 'quantity' => 1],
            ['item_product_id' => $item2->id, 'quantity' => 2],
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify item3 was deleted
        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
        ]);

        // Verify item1 and item2 still exist
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);
    });

    test('handles mixed create, update, and delete operations', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();
        $item4 = Product::factory()->create();

        // Create initial bundle items (item1 and item2)
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 2);

        // Sync: update item1, delete item2, create item3 and item4
        $items = [
            ['item_product_id' => $item1->id, 'quantity' => 10], // update
            ['item_product_id' => $item3->id, 'quantity' => 3],  // create
            ['item_product_id' => $item4->id, 'quantity' => 4],  // create
            // item2 is omitted, so it should be deleted
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseCount($this->bundleItemTable, 3);

        // Verify item1 was updated
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 10,
        ]);

        // Verify item2 was deleted
        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);

        // Verify item3 and item4 were created
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item4->id,
            'quantity' => 4,
        ]);
    });

    test('handles empty items array', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();

        // Create initial bundle item
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 1);

        // Sync with empty array (should delete all items)
        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseCount($this->bundleItemTable, 0);
    });
});

describe('sync categories', function () {
    test('set empty categories', function () {
        $product = Product::factory()->create();

        $this->productService->setModel($product)
            ->syncCategories([]);

        expect($product->categories)->toHaveCount(0);
    });

    test('set categories', function () {
        $product = Product::factory()->create();

        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $this->productService->setModel($product)
            ->syncCategories([$category1->id, $category2->id]);

        expect($product->categories)->toHaveCount(2)
            ->and($product->categories->pluck('id'))->toContain($category1->id, $category2->id);
    });


    test('replace categories', function () {
        $product = Product::factory()->create();

        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $product->categories()->sync([$category1->id]);

        $this->productService->setModel($product)
            ->syncCategories([$category2->id]);

        expect($product->categories)->toHaveCount(1)
            ->and($product->categories->pluck('id'))->toContain($category2->id);
    });
});

describe('sync warehouses', function () {
    test('create warehouses with label', function () {
        // Create a product first
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        // Create existing warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $warehouses = [
            [
                'id' => $warehouse1->id,
                'label' => 'Test Product Warehouse label 1',
            ],
            [
                'id' => $warehouse2->id,
                'label' => 'Test Product Warehouse label 1',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncWarehouses($warehouses);

        expect($result)->toBeInstanceOf(ProductService::class)
            ->and($product->warehouses)->toHaveCount(2)
            ->and($product->warehouses->pluck('id'))->toContain($warehouse1->id, $warehouse2->id)
            ->and($product->warehouses->pluck('pivot.label'))->toContain($warehouses[0]['label'], $warehouses[1]['label']);
    });

    test('set warehouses without label', function () {
        // Create a product first
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        // Create existing warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $warehouses = [
            [
                'id' => $warehouse1->id,
            ],
            [
                'id' => $warehouse2->id,
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncWarehouses($warehouses);

        expect($result)->toBeInstanceOf(ProductService::class)
            ->and($product->warehouses)->toHaveCount(2)
            ->and($product->warehouses->pluck('id'))->toContain($warehouse1->id, $warehouse2->id);
    });

    test('replace warehouses', function () {
        // Create a product first
        $product = Product::factory()->create();

        // Create existing warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $product->warehouses()->sync($warehouse1->id);

        $warehouses = [
            [
                'id' => $warehouse2->id,
                'label' => 'Test Product Warehouse label 1',
            ],
        ];

        $this->productService->setModel($product)
            ->syncWarehouses($warehouses);

        expect($product->warehouses)->toHaveCount(1)
            ->and($product->warehouses->pluck('id'))->toContain($warehouse2->id)
            ->and($product->warehouses->pluck('pivot.label'))->toContain($warehouses[0]['label']);
    });

    test('replace warehouses without label', function () {
        // Create a product first
        $product = Product::factory()->create();

        // Create existing warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $product->warehouses()->sync($warehouse1->id);

        $warehouses = [
            [
                'id' => $warehouse2->id,
            ],
        ];

        $this->productService->setModel($product)
            ->syncWarehouses($warehouses);

        expect($product->warehouses)->toHaveCount(1)
            ->and($product->warehouses->pluck('id'))->toContain($warehouse2->id);
    });

    test('set empty warehouses', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->syncWarehouses([]);

        expect($result)->toBeInstanceOf(ProductService::class)
            ->and($product->warehouses)->toHaveCount(0);
    });
});
