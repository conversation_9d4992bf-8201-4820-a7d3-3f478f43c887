<?php

use App\Models\Product;
use App\Models\ProductBundleItem;
use App\Models\ProductVariant;
use App\Models\Warehouse;
use App\Repositories\ProductRepository;

beforeEach(function () {
    $this->product = Product::factory()->create();
    $this->productRepository = app(ProductRepository::class);

    $this->variantTable = ProductVariant::class;
    $this->bundleItemTable = ProductBundleItem::class;
});

test('model', function () {
    expect($this->productRepository->model())->toBe(Product::class);
});


describe('delete variants', function () {
    test('clear all', function () {
        $product_variants = ProductVariant::factory(2)->create([
            'product_id' => $this->product->id,
        ]);

        $this->productRepository->deleteVariants($this->product);

        $this->assertSoftDeleted($this->variantTable, [
            'id' => $product_variants[0]->id,
        ]);

        $this->assertSoftDeleted($this->variantTable, [
            'id' => $product_variants[1]->id,
        ]);
    });

    test('by ids', function () {
        $product_variants = ProductVariant::factory(2)->create([
            'product_id' => $this->product->id,
        ]);

        $this->productRepository->deleteVariants($this->product, [$product_variants[0]->id]);

        $this->assertSoftDeleted($this->variantTable, [
            'id' => $product_variants[0]->id,
        ]);
    });
});

describe('delete bundle items', function () {
    test('clear all', function () {
        ProductBundleItem::factory(2)->create([
            'bundle_product_id' => $this->product->id,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $this->productRepository->deleteBundleItems($this->product);

        $this->assertDatabaseCount($this->bundleItemTable, 0);
    });

    test('by ids', function () {
        $bundle_items = ProductBundleItem::factory(2)->create([
            'bundle_product_id' => $this->product->id,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $this->productRepository->deleteBundleItems($this->product, [$bundle_items[0]->item_product_id]);

        $this->assertDatabaseCount($this->bundleItemTable, 1);

        $this->assertDatabaseMissing($this->bundleItemTable, [
            'item_product_id' => $bundle_items[0]->id,
        ]);
    });
});

describe('sync warehouses', function () {
    test('with label', function () {
        $warehouse = Warehouse::factory()->create();

        $warehouses = [
            $warehouse->id => ['label' => 'Test Product Warehouse label 1'],
        ];

        $this->productRepository->syncWarehouses($this->product, $warehouses);

        expect($this->product->warehouses->pluck('id')->toArray())
            ->toContain($warehouse->id)
            ->and($this->product->warehouses->pluck('pivot.label')->toArray())
            ->toContain($warehouses[$warehouse->id]['label']);
    });

    test('without label', function () {
        $warehouse = Warehouse::factory()->create();

        $warehouses = [
            $warehouse->id => ['label' => null],
        ];

        $this->productRepository->syncWarehouses($this->product, $warehouses);

        expect($this->product->warehouses->pluck('id')->toArray())->toContain($warehouse->id);
    });
});
