<?php

use App\Models\Product;
use App\Models\Warehouse;
use App\Repositories\WarehouseRepository;
use App\Services\WarehouseService;

beforeEach(function () {
    $this->warehouseService = app(WarehouseService::class);
    $this->table = Warehouse::class;
});

test('check repository', function () {
    expect($this->warehouseService->getRepository())->toBeInstanceOf(WarehouseRepository::class);
});

test('check relationship', function () {
    $product = Product::factory()->create();
    $warehouse = Warehouse::factory()->create();
    $product->warehouses()->sync($warehouse->id);

    $warehouse_model = $this->warehouseService->setModel($warehouse)->getModel();

    expect($warehouse_model->toArray())->toHaveKey('products');
});
