<?php

use App\Enums\Role as RoleEnum;
use App\Models\Branch;
use App\Models\User;
use App\Repositories\UserRepository;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles for testing
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);

    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);
    $this->branch3 = Branch::factory()->create([
        'name' => 'Test Branch 3',
        'active' => true,
    ]);

    $this->userRepository = app(UserRepository::class);
});

test('model', function () {
    $response = $this->userRepository->model();

    expect($response)->toEqual(User::class);
});

test('syncRoles method syncs user roles', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    $roles = [RoleEnum::CUSTOMER, RoleEnum::STAFF];

    $result = $this->userRepository->syncRoles($user, $roles);

    expect($result)->toBeInstanceOf(UserRepository::class)
        ->and($user->hasRole([RoleEnum::CUSTOMER, RoleEnum::STAFF]))->toBeTrue()
        ->and($user->hasRole(RoleEnum::SUPER_ADMIN))->toBeFalse();
});

test('syncRoles removes old roles and adds new ones', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    // Initially assign customer role
    $user->assignRole(RoleEnum::CUSTOMER);
    expect($user->hasRole(RoleEnum::CUSTOMER))->toBeTrue();

    // Sync with staff role (should remove customer and add staff)
    $this->userRepository->syncRoles($user, [RoleEnum::STAFF]);

    $user->refresh();

    expect($user->hasRole(RoleEnum::CUSTOMER))->toBeFalse()
        ->and($user->hasRole(RoleEnum::STAFF))->toBeTrue();
});

test('syncRoles works with empty roles array', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    // Initially assign some roles
    $user->assignRole([RoleEnum::CUSTOMER, RoleEnum::STAFF]);
    expect($user->hasRole([RoleEnum::CUSTOMER,RoleEnum::STAFF]))->toBeTrue();

    // Sync with empty array (should remove all roles)
    $result = $this->userRepository->syncRoles($user, []);

    $user->refresh();

    expect($result)->toBeInstanceOf(UserRepository::class)
        ->and($user->hasRole([RoleEnum::CUSTOMER, RoleEnum::STAFF]))->toBeFalse();

});

test('syncBranches method syncs user branches', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    $branches = [$this->branch1->id, $this->branch2->id];

    $result = $this->userRepository->syncBranches($user, $branches);

    expect($result)->toBeInstanceOf(UserRepository::class)
        ->and($user->branches()->count())->toBe(2)
        ->and($user->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);
});

test('syncBranches removes old branches and adds new ones', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    // Initially assign branches
    $user->branches()->attach([$this->branch1->id, $this->branch2->id]);
    expect($user->branches()->count())->toBe(2);

    // Sync with different branches (should remove old and add new)
    $this->userRepository->syncBranches($user, [$this->branch3->id]);

    $user->refresh();
    expect($user->branches()->count())->toBe(1)
        ->and($user->branches->first()->id)->toBe($this->branch3->id)
        ->and($user->branches->pluck('id')->toArray())->not()->toContain($this->branch1->id, $this->branch2->id);
});

test('syncBranches works with empty branches array', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    // Initially assign some branches
    $user->branches()->attach([$this->branch1->id, $this->branch2->id]);
    expect($user->branches()->count())->toBe(2);

    // Sync with empty array (should remove all branches)
    $result = $this->userRepository->syncBranches($user, []);

    expect($result)->toBeInstanceOf(UserRepository::class);
    $user->refresh();
    expect($user->branches()->count())->toBe(0);
});
