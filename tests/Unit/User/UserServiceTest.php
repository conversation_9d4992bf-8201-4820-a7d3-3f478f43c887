<?php

use App\Enums\NotificationChannel;
use App\Enums\Role as RoleEnum;
use App\Models\Branch;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Models\UserProfile;
use App\Notifications\GenericNotification;
use App\Repositories\UserRepository;
use App\Services\UserService;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles for testing
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);

    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    // Create welcome notification template for testing
    NotificationTemplate::factory()->create([
        'name' => 'welcome_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Welcome, {{user_name}}!',
        'content' => 'Hello {{user_name}}, welcome! Phone: {{phone_number}}, Email: {{email}}',
        'is_active' => true,
    ]);

    $this->userService = app(UserService::class);

    $this->table = User::class;
    $this->userProfileTable = UserProfile::class;
});

test('check repository', function () {
    expect($this->userService->getRepository())->toBeInstanceOf(UserRepository::class);
});

test('check relationship', function () {
    $user = User::factory()->create();
    UserProfile::factory()->create([
        'customer_id' => $user->id,
    ]);
    $user->branches()->sync([$this->branch1->id]);

    $user_model = $this->userService->setModel($user)->getModel();

    expect($user_model->toArray())->toHaveKeys(['profile', 'branches']);
});

test('sync roles', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    $roles = [RoleEnum::CUSTOMER, RoleEnum::STAFF];

    $response = $this->userService
        ->setModel($user)
        ->syncRoles($roles);

    expect($response)->toBeInstanceOf(UserService::class)
        ->and($user->hasRole([RoleEnum::CUSTOMER, RoleEnum::STAFF]))->toBeTrue()
        ->and($user->hasRole(RoleEnum::SUPER_ADMIN))->toBeFalse();
});

test('sync branches', function () {
    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
    ]);

    $branches = [$this->branch1->id, $this->branch2->id];

    $response = $this->userService
        ->setModel($user)
        ->syncBranches($branches);

    expect($response)->toBeInstanceOf(UserService::class)
        ->and($user->branches()->count())->toBe(2)
        ->and($user->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);

    // Test updating branches (removing one, keeping one)
    $updatedBranches = [$this->branch1->id];

    $this->userService
        ->setModel($user)
        ->syncBranches($updatedBranches);

    $user->refresh();
    expect($user->branches()->count())->toBe(1)
        ->and($user->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

describe('sync profile', function () {
    test('create profile', function () {
        $user = User::factory()->create([
            'firstname' => 'John',
            'lastname' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        $data = [
            'bio' => 'Test bio',
            'contact' => '+1234567890',
            'city' => 'New York',
        ];

        $response = $this->userService
            ->setModel($user)
            ->syncProfile($data);

        expect($response)->toBeInstanceOf(UserService::class);

        $this->assertDatabaseHas($this->userProfileTable, array_merge([
            'customer_id' => $user->id,
        ], $data));
    });

    test('update profile', function () {
        $user = User::factory()->create();

        // First create a profile using factory
        $existing_profile = UserProfile::factory()->create([
            'customer_id' => $user->id,
        ]);

        // Verify profile was created
        $this->assertDatabaseCount($this->userProfileTable, 1);

        // Now update with new data
        $data = [
            'bio' => 'Updated bio',
            'contact' => '+9876543210',
            'city' => 'Updated City',
            'emergency_contact' => 'Updated Emergency Contact',
            'emergency_phone' => '+9876543210',
            'street_address' => '456 Updated Street',
            'zip' => '67890',
            'race' => 'Updated Race',
        ];

        $response = $this->userService
            ->setModel($user)
            ->syncProfile($data);

        expect($response)->toBeInstanceOf(UserService::class);

        // Verify profile count is still 1 (updated, not created new)
        $this->assertDatabaseCount($this->userProfileTable, 1);

        // Verify the profile was updated with new data
        $this->assertDatabaseHas($this->userProfileTable, array_merge([
            'id' => $existing_profile->id,
            'customer_id' => $user->id,
        ], $data));

        // Verify old data is no longer in database
        $this->assertDatabaseMissing($this->userProfileTable, [
            'bio' => 'Original bio',
            'contact' => '+1111111111',
            'city' => 'Original City',
        ]);
    });
});

test('send welcome notification', function () {
    Notification::fake();

    $user = User::factory()->create([
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
    ]);

    $result = $this->userService
        ->setModel($user)
        ->sendWelcomeNotification();

    expect($result)->toBeInstanceOf(UserService::class);

    Notification::assertSentTo(
        $user,
        GenericNotification::class,
        function ($notification) use ($user) {
            // Verify notification contains correct template data
            $notificationArray = $notification->toArray($user);
            $templateData = $notificationArray['template_data'];
            return $templateData['user_name'] === $user->name &&
                $templateData['phone_number'] === $user->phone &&
                $templateData['email'] === $user->email;
        }
    );
});

test('sync media', function () {
    $user = User::factory()->create();
    UserProfile::factory()->create([
        'customer_id' => $user->id,
    ]);
    $file = \Illuminate\Http\UploadedFile::fake()->image('test-avatar.png');

    // Use new simplified media structure
    $media_data = [
        'avatar' => [
            [
                'file' => $file,
                'order_column' => 1
            ]
        ]
    ];

    $result = $this->userService
        ->setModel($user)
        ->syncMedia($media_data);

    expect($result)->toBeInstanceOf(UserService::class);

    // Verify user profile was created
    $user->refresh();
    expect($user->profile)->not->toBeNull();

    // Verify media record was created in database for user profile
    $this->assertDatabaseHas('media', [
        'model_type' => UserProfile::class,
        'model_id' => $user->profile->id,
        'collection_name' => 'avatar',
        'file_name' => 'test-avatar.png'
    ]);
});
