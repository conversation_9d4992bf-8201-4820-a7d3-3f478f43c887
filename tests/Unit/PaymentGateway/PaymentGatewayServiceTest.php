<?php

use App\Models\PaymentGateway;
use App\Repositories\PaymentGatewayRepository;
use App\Services\PaymentGatewayService;

beforeEach(function () {
    $this->paymentGatewayService = app(PaymentGatewayService::class);

    $this->table = PaymentGateway::class;
});

test('check repository', function () {
    expect($this->paymentGatewayService->getRepository())->toBeInstanceOf(PaymentGatewayRepository::class);
});
