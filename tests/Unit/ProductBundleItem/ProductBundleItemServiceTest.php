<?php

use App\Models\ProductBundleItem;
use App\Repositories\ProductBundleItemRepository;
use App\Services\ProductBundleItemService;

beforeEach(function () {
    $this->bundleItemService = app(ProductBundleItemService::class);
    $this->table = ProductBundleItem::class;
});

test('check repository', function () {
    expect($this->bundleItemService->getRepository())->toBeInstanceOf(ProductBundleItemRepository::class);
});
