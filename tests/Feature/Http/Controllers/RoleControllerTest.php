<?php

use App\Models\Role;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;

beforeEach(function () {
    setupRoles();
    setupPermissions(['edit-role-permission']);

    $users = setupTestUsers(['edit-role-permission']);
    $this->adminUser = $users['admin_user'];
    $this->staffUser = $users['staff_user'];
    $this->unauthorizedUser = $users['unauthorized_user'];

    $this->routeNamePrefix = 'roles';
});

test('unauthenticated and unauthorized user cannot access role endpoints', function () {
    $role = Role::factory()->create(['guard_name' => 'api']);

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['role' => $role->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['role' => $role->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

describe('create', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $permission = Permission::create(['name' => 'test-permission', 'guard_name' => 'api']);

        $payload = [
            'name' => 'test-role',
            'permissions' => [$permission->name],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $payload)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray(['name' => $payload['name']]);

        // Verify role was created in database

        $role = Role::query()->where('name', 'test-role')->where('guard_name', 'api')->first();
        expect($role)->not->toBeNull()
            ->and($role->hasPermissionTo($permission->name))->toBeTrue();
    });

    test('with assigned users', function () {
        Sanctum::actingAs($this->adminUser);

        $permission = Permission::create(['name' => 'test-permission-users', 'guard_name' => 'api']);
        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);

        $payload = [
            'name' => 'test-role-with-users',
            'permissions' => [$permission->name],
            'user_ids' => [$user1->id, $user2->id],
        ];

        $this->postJson(route("{$this->routeNamePrefix}.store"), $payload)->json();

        // Verify users were assigned the role
        $user1->refresh();
        $user2->refresh();
        expect($user1->hasRole('test-role-with-users'))->toBeTrue()
            ->and($user2->hasRole('test-role-with-users'))->toBeTrue();
    });

    test('validation fails with invalid data', function () {
        Sanctum::actingAs($this->adminUser);

        // Test missing name
        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The role name is required.'],
        ]);
        // Test duplicate name
        Role::create(['name' => 'existing-role', 'guard_name' => 'api']);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'existing-role',
        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['A role with this name already exists.'],
        ]);
        // Test invalid permission
        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'test-role-invalid',
            'permissions' => ['non-existent-permission'],
        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'permissions.0' => ['One or more selected permissions do not exist.'],
        ]);
    });

    test('validation fails with invalid user_ids', function () {
        Sanctum::actingAs($this->adminUser);

        // Test with non-existent user ID
        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'test-role-invalid-users',
            'user_ids' => [99999], // Non-existent user ID
        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'user_ids.0' => ['One or more selected users do not exist.'],
        ]);
    });
});

describe('update', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $role = Role::create(['name' => 'update-test-role', 'guard_name' => 'api']);
        $permission = Permission::create(['name' => 'update-test-permission', 'guard_name' => 'api']);

        $payload = [
            'name' => 'updated-role-name',
            'permissions' => [$permission->name],
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['role' => $role->id]), $payload)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray(['name' => $payload['name']]);

        // Verify role was updated in database
        $role->refresh();

        expect($role->name)->toBe('updated-role-name')
            ->and($role->hasPermissionTo($permission->name))->toBeTrue();
    });

    test('with new user assignments', function () {
        Sanctum::actingAs($this->adminUser);

        $role = Role::create(['name' => 'update-test-role-users', 'guard_name' => 'api']);
        $permission = Permission::create(['name' => 'update-test-permission-users', 'guard_name' => 'api']);

        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);
        $user3 = User::factory()->create(['email' => '<EMAIL>']);

        // Initially assign role to user1
        $user1->assignRole($role->name);

        $payload = [
            'name' => 'updated-role-name-users',
            'permissions' => [$permission->name],
            'user_ids' => [$user2->id, $user3->id], // Remove user1, add user2 and user3
        ];

        $this->putJson(route("{$this->routeNamePrefix}.update", ['role' => $role->id]), $payload)->json();

        // Verify user assignments were updated
        $user1->refresh();
        $user2->refresh();
        $user3->refresh();
        expect($user1->hasRole('updated-role-name-users'))->toBeFalse() //Removed
        ->and($user2->hasRole('updated-role-name-users'))->toBeTrue() //Added
        ->and($user3->hasRole('updated-role-name-users'))->toBeTrue(); // Added
    });

    test('validation fails with invalid data', function () {
        Sanctum::actingAs($this->adminUser);

        $role = Role::create(['name' => 'test-role-update', 'guard_name' => 'api']);
        Role::create(['name' => 'existing-role-update', 'guard_name' => 'api']);

        // Test duplicate name
        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['role' => $role->id]), [
            'name' => 'existing-role-update',
        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['A role with this name already exists.'],
        ]);
    });

    test('validation fails with invalid user_ids', function () {
        Sanctum::actingAs($this->adminUser);

        $role = Role::create(['name' => 'test-role-update', 'guard_name' => 'api']);

        // Test with non-existent user ID
        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['role' => $role->id]), [
            'name' => 'test-role-invalid-users',
            'user_ids' => [99999], // Non-existent user ID
        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'user_ids.0' => ['One or more selected users do not exist.'],
        ]);
    });
});


test('delete', function () {
    Sanctum::actingAs($this->adminUser);

    $role = Role::create(['name' => 'delete-test-role', 'guard_name' => 'api']);

    $permission = Permission::create(['name' => 'update-test-permission-users', 'guard_name' => 'api']);

    $user1 = User::factory()->create(['email' => '<EMAIL>']);

    $user1->assignRole($role->name);
    $permission->assignRole($role);

    $this->assertDatabaseHas('role_has_permissions', [
        'role_id' => $role->id,
        'permission_id' => $permission->id,
    ]);

    $this->assertDatabaseHas('model_has_roles', [
        'role_id' => $role->id,
        'model_id' => $user1->id,
        'model_type' => User::class,
    ]);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['role' => $role->id]))->json();

    // Verify role was deleted from database
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseMissing('role_has_permissions', [
        'role_id' => $role->id,
        'permission_id' => $permission->id,
    ]);

    $this->assertDatabaseMissing('model_has_roles', [
        'role_id' => $role->id,
        'model_id' => $user1->id,
        'model_type' => User::class,
    ]);
});
