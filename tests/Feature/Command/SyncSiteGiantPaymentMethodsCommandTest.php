<?php

use App\Models\PaymentMethod;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $base_url = config('services.sitegiant.base_url');

    Http::fake([
        $base_url . '/getAccessToken' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => [
                'message' => 'success update token',
                'access_token' => 'token',
            ],
        ]),
        $base_url . '/paymentMethods' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => [
                'payment_methods' => [
                    "Cash on delivery",
                    "Bank Transfer",
                ],
            ],
        ]),
    ]);
});

test('sitegiant sync payment methods', function () {
    PaymentMethod::factory()->create([
        'title' => 'Cash on delivery',
    ]);

    $this->assertDatabaseCount(PaymentMethod::class, 1);

    Artisan::call('sitegiant:sync-payment-methods');

    $this->assertDatabaseHas(PaymentMethod::class, [
        'title' => 'Cash on delivery',
    ]);

    $this->assertDatabaseHas(PaymentMethod::class, [
        'title' => 'Bank Transfer',
    ]);

    //no duplicate payment method created
    $this->assertDatabaseCount(PaymentMethod::class, 2);
});
