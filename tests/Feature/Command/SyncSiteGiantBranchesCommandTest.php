<?php

use App\Models\Branch;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $base_url = config('services.sitegiant.base_url');

    Http::fake([
        $base_url . '/getAccessToken' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => [
                'message' => 'success update token',
                'access_token' => 'token',
            ],
        ]),
        $base_url . '/channels' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => [
                'channel_list' => [
                    [
                        'id' => 1,
                        'name' => 'Branch 1',
                        'type' => 'ShopeeMY',
                        'country' => 'Malaysia',
                    ],
                    [
                        'id' => 2,
                        'name' => 'Branch 2',
                        'type' => 'LazadaMY',
                        'country' => 'Malaysia',
                    ],
                ],
            ],
        ]),
    ]);
});

test('sitegiant sync branches', function () {
    Branch::factory()->create([
        'code' => 'sitegiant_1',
        'name' => 'Branch 1',
    ]);
    $this->assertDatabaseCount(Branch::class, 1);

    Artisan::call('sitegiant:sync-branches');

    $this->assertDatabaseHas(Branch::class, [
        'code' => 'sitegiant_1',
        'name' => 'Branch 1',
    ]);

    $this->assertDatabaseHas(Branch::class, [
        'code' => 'sitegiant_2',
        'name' => 'Branch 2',
    ]);

    //no duplicate branch created
    $this->assertDatabaseCount(Branch::class, 2);
});
