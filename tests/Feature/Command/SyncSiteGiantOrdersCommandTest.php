<?php

use App\Enums\OrderStatus as OrderStatusEnum;
use App\Enums\OrderType;
use App\Enums\SiteGiantOrderStatus;
use App\Models\Branch;
use App\Models\BranchModel;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\PaymentGateway;
use App\Models\PaymentMethod;
use Database\Seeders\OrderStatusSeeder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Http;

beforeEach(function () {
    $this->seed([
        OrderStatusSeeder::class,
    ]);

    PaymentGateway::factory()->create([
        'title' => 'SiteGiant',
        'code' => 'sitegiant',
    ]);

    $base_url = config('services.sitegiant.base_url');

    $this->sitegiant_order_data1 = [
        "order_id" => 1,
        "marketplace_order_id" => null,
        "currency" => "MYR",
        "default_currency" => "SGD",
        "currency_value" => "0.3226",
        "total_amount" => "20",
        "marketplace_order_vouchers" => [
            [
                "name" => "Platform Voucher",
                "value" => "20.00",
            ],
            [
                "name" => "Platform Shipping Discount",
                "value" => "40.00",
            ],
        ],
        "order_status" => SiteGiantOrderStatus::PAID,
        "channel" => [
            "channel_id" => 1,
            "channel_type" => null,
            "channel_name" => null,
            "channel_country" => null,
        ],
        "shipping_carrier" => "J&T Express",
        "shipping_tracking" => "1234",
        "payment_method" => "Cash on delivery",
        "shipping_fee" => "10",
        "comment" => null,
        "order_time" => "2021-08-17 11:48:40",
        "last_modified_date" => "2021-08-20 12:11:50",
        "ship_by_date" => "2021-08-20 23:59:59",
        "customer_id" => 12,
        "buyer_name" => "Support Team",
        "billing_address" => [
            "company" => "Sitegiant Sdn Bhd",
            "name" => "Support Team",
            "telephone" => "0123456789",
            "email" => "<EMAIL>",
            "address1" => "1, Support Team, Jalan Support",
            "address2" => "Taman Support",
            "city" => "Bayan Baru",
            "postal_code" => "10470",
            "state" => "Pulau Pinang",
            "country" => "Malaysia",
            "country_code" => "MY",
        ],
        "recipient_address" => [
            "company" => "Sitegiant Sdn Bhd",
            "name" => "Support Team",
            "telephone" => "0123456789",
            "email" => "<EMAIL>",
            "address1" => "1, Support Team, Jalan Support",
            "address2" => "Taman Support",
            "city" => "Bayan Baru",
            "postal_code" => "10470",
            "state" => "Pulau Pinang",
            "country" => "Malaysia",
            "country_code" => "MY",
        ],
        "products" => [
            [
                "order_product_id" => 12,
                "is_cancelled" => false,
                "product_name" => "Stand Fan",
                "product_sku" => "fan001",
                "quantity_purchased" => 1,
                "price" => "10",
                "tax" => "0",
                "total" => "10",
                "cost" => "8",
                "variation" => "Color: Black",
                "item_id" => 1,
                "isku" => "Pan-Fan",
                "warehouse" => [
                    "warehouse_id" => 1,
                    "warehouse_name" => "Default",
                ],
                "product_serial_number" => [
                    [
                        "item_id" => 1,
                        "item_parent_id" => 2,
                        "serial_number" => "123",
                    ],
                ],
                "stock_status" => "locked",
                "is_bundle" => false,
                "is_kit" => "yes",
                "kit_items" => [
                    [
                        "item_id" => 1,
                        "item_name" => "Stand Fan Single",
                        "isku" => "fan-single-001",
                        "item_quantity" => 1,
                        "racks" => [
                            [
                                "rack_id" => 1,
                                "rack_name" => "Rack A",
                                "batches" => [
                                    [
                                        "batch_id" => 3,
                                        "batch_name" => "240922SA881",
                                        "quantity" => 1,
                                    ],
                                ],
                            ],
                        ],
                    ],
                    [
                        "item_id" => 3,
                        "item_name" => "Remote Control Stand Fan",
                        "isku" => "remote-control-fan-single-001",
                        "item_quantity" => 1,
                        "racks" => [
                            [
                                "rack_id" => 2,
                                "rack_name" => "Rack B",
                                "batches" => [
                                    [
                                        "batch_id" => 5,
                                        "batch_name" => "240922SA111",
                                        "quantity" => 1,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
                "racks" => [
                    [
                        "rack_id" => 4,
                        "rack_name" => "Rack E",
                        "batches" => [],
                    ],
                ],
            ],
        ],
        "promotion_info" => [
            [
                "code" => "coupon",
                "title" => "Seller Voucher",
                "total" => 0,
            ],
            [
                "code" => "platform",
                "title" => "Platform Voucher",
                "total" => 0,
            ],
            [
                "code" => "bundle",
                "title" => "Bundle Deal",
                "total" => 0,
            ],
            [
                "code" => "voucher",
                "title" => "Voucher",
                "total" => 0,
            ],
        ],
        "invoice_no" => "INV-1",
        "tax" => "0",
        "tax_info" => [
            "shipping_taxable" => "no",
            "product_includes_tax" => "no",
            "product_tax" => 0.6,
            "shipping_tax" => 0,
        ],
        "fulfillment_status" => "fulfilled",
        "order_remark" => [
            "order_remark_id" => 1,
            "name" => "Pending Process",
        ],
        "order_packages" => [
            [
                "order_shipment_id" => 15,
                "package_id" => 1200,
                "courier_code" => "jt",
                "courier_company" => "J&T Express",
                "tracking_no" => "12444233444",
                "products" => [
                    [
                        "order_product_id" => 12,
                        "product_name" => "Stand Fan",
                        "product_sku" => "fan001",
                        "quantity" => 1,
                    ],
                ],
            ],
        ],
        "dropship_fulfillment_files" => [
            [
                "file_id" => 190,
                "file_url" => "https=>//sglitelocal.s3.ap-southeast-1.amazonaws.com/SiteGiant/images/order/order-1/dropship_fulfillment/zmowmcZb64c9bbe1eaff1_1690942433.pdf",
                "created_at" => "2021-08-20 12=>11=>50",
            ],
        ],
        "einvoice_status" => true,
    ];

    $this->sitegiant_order_data2 = [
        "order_id" => 3,
        "marketplace_order_id" => null,
        "currency" => "MYR",
        "default_currency" => "SGD",
        "currency_value" => "0.3226",
        "total_amount" => "30.30",
        "marketplace_order_vouchers" => [
            [
                "name" => "Platform Voucher",
                "value" => "10.00",
            ],
            [
                "name" => "Platform Shipping Discount",
                "value" => "30.00",
            ],
        ],
        "order_status" => SiteGiantOrderStatus::SHIPPED,
        "channel" => [
            "channel_id" => 1,
            "channel_type" => null,
            "channel_name" => 'Shopee',
            "channel_country" => 'MY',
        ],
        "shipping_carrier" => "J&T Express",
        "shipping_tracking" => "1234",
        "payment_method" => "Cash on delivery",
        "shipping_fee" => "10.20",
        "comment" => null,
        "order_time" => "2021-08-17 11:48:40",
        "last_modified_date" => "2021-08-20 12:11:50",
        "ship_by_date" => "2021-08-20 23:59:59",
        "customer_id" => 12,
        "buyer_name" => "Support Team",
        "billing_address" => [
            "company" => "Sitegiant Sdn Bhd",
            "name" => "Support Team",
            "telephone" => "0123456789",
            "email" => "<EMAIL>",
            "address1" => "1, Support Team, Jalan Support",
            "address2" => "Taman Support",
            "city" => "Bayan Baru",
            "postal_code" => "10470",
            "state" => "Pulau Pinang",
            "country" => "Malaysia",
            "country_code" => "MY",
        ],
        "recipient_address" => [
            "company" => "Sitegiant Sdn Bhd",
            "name" => "Support Team",
            "telephone" => "0123456789",
            "email" => "<EMAIL>",
            "address1" => "1, Support Team, Jalan Support",
            "address2" => "Taman Support",
            "city" => "Bayan Baru",
            "postal_code" => "10470",
            "state" => "Pulau Pinang",
            "country" => "Malaysia",
            "country_code" => "MY",
        ],
        "products" => [
            [
                "order_product_id" => 12,
                "is_cancelled" => false,
                "product_name" => "Stand Fan",
                "product_sku" => "fan001",
                "quantity_purchased" => 1,
                "price" => "10",
                "tax" => "0",
                "total" => "10",
                "cost" => "8",
                "variation" => "Color: Black",
                "item_id" => 1,
                "isku" => "Pan-Fan",
                "warehouse" => [
                    "warehouse_id" => 1,
                    "warehouse_name" => "Default",
                ],
                "product_serial_number" => [
                    [
                        "item_id" => 1,
                        "item_parent_id" => 2,
                        "serial_number" => "123",
                    ],
                ],
                "stock_status" => "locked",
                "is_bundle" => false,
                "is_kit" => "yes",
                "kit_items" => [
                    [
                        "item_id" => 1,
                        "item_name" => "Stand Fan Single",
                        "isku" => "fan-single-001",
                        "item_quantity" => 1,
                        "racks" => [
                            [
                                "rack_id" => 1,
                                "rack_name" => "Rack A",
                                "batches" => [
                                    [
                                        "batch_id" => 3,
                                        "batch_name" => "240922SA881",
                                        "quantity" => 1,
                                    ],
                                ],
                            ],
                        ],
                    ],
                    [
                        "item_id" => 3,
                        "item_name" => "Remote Control Stand Fan",
                        "isku" => "remote-control-fan-single-001",
                        "item_quantity" => 1,
                        "racks" => [
                            [
                                "rack_id" => 2,
                                "rack_name" => "Rack B",
                                "batches" => [
                                    [
                                        "batch_id" => 5,
                                        "batch_name" => "240922SA111",
                                        "quantity" => 1,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
                "racks" => [
                    [
                        "rack_id" => 4,
                        "rack_name" => "Rack E",
                        "batches" => [],
                    ],
                ],
            ],
        ],
        "promotion_info" => [
            [
                "code" => "coupon",
                "title" => "Seller Voucher",
                "total" => 0,
            ],
            [
                "code" => "platform",
                "title" => "Platform Voucher",
                "total" => 0,
            ],
            [
                "code" => "bundle",
                "title" => "Bundle Deal",
                "total" => 0,
            ],
            [
                "code" => "voucher",
                "title" => "Voucher",
                "total" => 0,
            ],
        ],
        "invoice_no" => "INV-1",
        "tax" => "0",
        "tax_info" => [
            "shipping_taxable" => "no",
            "product_includes_tax" => "no",
            "product_tax" => 0.6,
            "shipping_tax" => 0,
        ],
        "fulfillment_status" => "fulfilled",
        "order_remark" => [
            "order_remark_id" => 1,
            "name" => "Pending Process",
        ],
        "order_packages" => [
            [
                "order_shipment_id" => 15,
                "package_id" => 1200,
                "courier_code" => "jt",
                "courier_company" => "J&T Express",
                "tracking_no" => "12444233444",
                "products" => [
                    [
                        "order_product_id" => 12,
                        "product_name" => "Stand Fan",
                        "product_sku" => "fan001",
                        "quantity" => 1,
                    ],
                ],
            ],
        ],
        "dropship_fulfillment_files" => [
            [
                "file_id" => 190,
                "file_url" => "https=>//sglitelocal.s3.ap-southeast-1.amazonaws.com/SiteGiant/images/order/order-1/dropship_fulfillment/zmowmcZb64c9bbe1eaff1_1690942433.pdf",
                "created_at" => "2021-08-20 12:11:50",
            ],
        ],
        "einvoice_status" => true,
    ];

    Http::fake([
        $base_url . '/getAccessToken' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => [
                'message' => 'success update token',
                'access_token' => 'token',
            ],
        ]),
        $base_url . '/orders?*' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => [
                "more" => false,
                "total" => 2,
                'order_list' => [
                    [
                        "order_id" => 1,
                        "marketplace_order_id" => "577035802893322853",
                        "currency" => "MYR",
                        "default_currency" => "SGD",
                        "currency_value" => "0.3226",
                        "total_amount" => "20",
                        "order_status" => SiteGiantOrderStatus::PAID,
                        "fulfillment_status" => "fulfilled",
                        "order_remark" => [
                            "order_remark_id" => 1,
                            "name" => "Pending Process",
                        ],
                        "last_modified_date" => "2021-08-20 12:11:50",
                        "einvoice_status" => true,
                    ],
                    [
                        "order_id" => 3,
                        "marketplace_order_id" => "577035802893322855",
                        "currency" => "MYR",
                        "default_currency" => "SGD",
                        "currency_value" => "0.3226",
                        "total_amount" => "30.30",
                        "order_status" => SiteGiantOrderStatus::SHIPPED,
                        "fulfillment_status" => "fulfilled",
                        "order_remark" => [
                            "order_remark_id" => 1,
                            "name" => "Pending Process",
                        ],
                        "last_modified_date" => "2021-08-20 12:11:50",
                        "einvoice_status" => true,
                    ],
                ],
            ],
        ]),
        $base_url . '/orders/1' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => $this->sitegiant_order_data1,
        ]),
        $base_url . '/orders/3' => Http::response([
            'code' => 200,
            'error' => [],
            'response' => $this->sitegiant_order_data2,
        ]),
        '*' => function ($request) {
            return Http::response([
                'code' => 200,
                'error' => [],
                'response' => [
                    'request' => $request->url(),
                    'order_list' => [],
                    'more' => false,
                ],
            ]); // default response
        },
    ]);
});

test('sitegiant sync orders', function () {
    $branch = Branch::factory()->create([
        'code' => 'sitegiant_1',
        'name' => 'Branch 1',
    ]);

    Order::factory()->create([
        'display_id' => 'sitegiant_3',
        'order_status_id' => OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first()->id,
    ]);

    $this->assertDatabaseCount(Order::class, 1);

    Artisan::call('sitegiant:sync-orders');

    //Create new order
    $this->assertDatabaseHas(Order::class, [
        'display_id' => 'sitegiant_1',
        'type' => OrderType::SIMPLE,
        'require_shipping' => true,
        'customer_name' => $this->sitegiant_order_data1['buyer_name'],
        'order_status_id' => OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first()->id,
        'amount' => $this->sitegiant_order_data1['total_amount'],
        'sales_tax' => $this->sitegiant_order_data1['tax'],
        'paid_total' => $this->sitegiant_order_data1['total_amount'],
        'total' => $this->sitegiant_order_data1['total_amount'],
        'total_invoiced' => $this->sitegiant_order_data1['total_amount'],
        'payment_method_id' => PaymentMethod::query()->where('title', $this->sitegiant_order_data1['payment_method'])->first()->id,
        'billing_address->company' => $this->sitegiant_order_data1['billing_address']['company'],
        'billing_address->name' => $this->sitegiant_order_data1['billing_address']['name'],
        'billing_address->telephone' => $this->sitegiant_order_data1['billing_address']['telephone'],
        'billing_address->email' => $this->sitegiant_order_data1['billing_address']['email'],
        'billing_address->address1' => $this->sitegiant_order_data1['billing_address']['address1'],
        'billing_address->address2' => $this->sitegiant_order_data1['billing_address']['address2'],
        'billing_address->city' => $this->sitegiant_order_data1['billing_address']['city'],
        'billing_address->postal_code' => $this->sitegiant_order_data1['billing_address']['postal_code'],
        'billing_address->state' => $this->sitegiant_order_data1['billing_address']['state'],
        'billing_address->country' => $this->sitegiant_order_data1['billing_address']['country'],
        'billing_address->country_code' => $this->sitegiant_order_data1['billing_address']['country_code'],

        'shipping_address->company' => $this->sitegiant_order_data1['recipient_address']['company'],
        'shipping_address->name' => $this->sitegiant_order_data1['recipient_address']['name'],
        'shipping_address->telephone' => $this->sitegiant_order_data1['recipient_address']['telephone'],
        'shipping_address->email' => $this->sitegiant_order_data1['recipient_address']['email'],
        'shipping_address->address1' => $this->sitegiant_order_data1['recipient_address']['address1'],
        'shipping_address->address2' => $this->sitegiant_order_data1['recipient_address']['address2'],
        'shipping_address->city' => $this->sitegiant_order_data1['recipient_address']['city'],
        'shipping_address->postal_code' => $this->sitegiant_order_data1['recipient_address']['postal_code'],
        'shipping_address->state' => $this->sitegiant_order_data1['recipient_address']['state'],
        'shipping_address->country' => $this->sitegiant_order_data1['recipient_address']['country'],
        'shipping_address->country_code' => $this->sitegiant_order_data1['recipient_address']['country_code'],
        'delivery_fee' => $this->sitegiant_order_data1['shipping_fee'],
        'delivery_time' => $this->sitegiant_order_data1['ship_by_date'],
    ]);

    $this->assertDatabaseHas(BranchModel::class, [
        'branch_id' => $branch->id,
        'model_type' => Order::class,
        'model_id' => Order::query()->where('display_id', 'sitegiant_1')->first()->id,
    ]);

    //Update existing order status
    $this->assertDatabaseHas(Order::class, [
        'display_id' => 'sitegiant_3',
        'order_status_id' => OrderStatus::query()->where('slug', OrderStatusEnum::DELIVERY)->first()->id,
    ]);

    //no duplicate order created
    $this->assertDatabaseCount(Order::class, 2);
});
