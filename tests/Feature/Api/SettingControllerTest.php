<?php

use App\Enums\Permission;
use App\Enums\SettingType;
use App\Models\Branch;
use App\Models\Instructor;
use App\Models\Setting;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::SAVE_SETTING,
    ], [Permission::SAVE_SETTING]);

    $this->branch1 = Branch::factory()->create();
    $this->branch2 = Branch::factory()->create();

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    $this->routeNamePrefix = 'settings';
    $this->table = Setting::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access setting endpoints', function () {
    $instructor = Instructor::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.save")],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

describe('save', function () {
    test('create setting', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'type' => SettingType::AUTOCOUNT,
            'options' => [
                'accounts' => [
                    $this->branch1->id => [
                        'book_id' => 'book_1234',
                        'key_id' => 'key_1234',
                        'api_key' => 'api_1234',
                    ],
                    $this->branch2->id => [
                        'book_id' => 'book_1235',
                        'key_id' => 'key_1235',
                        'api_key' => 'api_1235',
                    ],
                ],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.save"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'][0])->toMatchArray([
                'type' => SettingType::AUTOCOUNT,
                'options' => $data['options'],
            ]);

        $this->assertDatabaseCount($this->table, 1);

        $setting = Setting::query()->where('type', SettingType::AUTOCOUNT)->first();

        expect($setting)->toMatchArray([
            'type' => SettingType::AUTOCOUNT,
            'options' => $data['options'],
        ]);
    });

    test('update setting', function () {
        Sanctum::actingAs($this->adminUser);

        Setting::factory()->create([
            'type' => SettingType::AUTOCOUNT,
            'options' => [
                'accounts' => [
                    $this->branch1->id => [
                        'book_id' => 'book_1234',
                        'key_id' => 'key_1234',
                        'api_key' => 'api_1234',
                    ],
                ],
            ],
        ]);

        $this->assertDatabaseCount($this->table, 1);

        $data = [
            'type' => SettingType::AUTOCOUNT,
            'options' => [
                'accounts' => [
                    $this->branch1->id => [
                        'book_id' => 'book_1234',
                        'key_id' => 'key_1234',
                        'api_key' => 'api_1234',
                    ],
                    $this->branch2->id => [
                        'book_id' => 'book_1235',
                        'key_id' => 'key_1235',
                        'api_key' => 'api_1235',
                    ],
                ],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.save"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'][0])->toMatchArray([
                'type' => SettingType::AUTOCOUNT,
                'options' => $data['options'],
            ]);

        $this->assertDatabaseCount($this->table, 1);

        $setting = Setting::query()->where('type', SettingType::AUTOCOUNT)->first();

        expect($setting)->toMatchArray([
            'type' => SettingType::AUTOCOUNT,
            'options' => $data['options'],
        ]);
    });

    test('validate setting type', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'type' => 'invalid',
            'options' => [
                'accounts' => [
                    $this->branch1->id => [
                        'book_id' => 'book_1234',
                        'key_id' => 'key_1234',
                        'api_key' => 'api_1234',
                    ],
                    $this->branch2->id => [
                        'book_id' => 'book_1235',
                        'key_id' => 'key_1235',
                        'api_key' => 'api_1235',
                    ],
                ],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.save"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'type' => ['The selected type is invalid.'],
        ]);
    });

    test('validate setting options', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'type' => SettingType::AUTOCOUNT,
            'options' => 'string',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.save"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'options' => ['The options must be an array.'],
        ]);
    });
});

test('get settings', function () {
    Setting::factory()->create([
        'type' => SettingType::AUTOCOUNT,
        'options' => [
            'accounts' => [
                $this->branch1->id => [
                    'book_id' => 'book_1234',
                    'key_id' => 'key_1234',
                    'api_key' => 'api_1234',
                ],
            ],
        ],
    ]);

    $settings = Setting::all();

    cache()->forever('settings', $settings);

    $response = $this->getJson(route("{$this->routeNamePrefix}.index"))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toMatchArray([
            'type' => SettingType::AUTOCOUNT,
            'options' => [
                'accounts' => [
                    $this->branch1->id => [
                        'book_id' => 'book_1234',
                        'key_id' => 'key_1234',
                        'api_key' => 'api_1234',
                    ],
                ],
            ],
        ]);
});
