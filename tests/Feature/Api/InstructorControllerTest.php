<?php

use App\Enums\Permission;
use App\Enums\Role;
use App\Models\Branch;
use App\Models\Designation;
use App\Models\Instructor;
use App\Models\User;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_INSTRUCTOR,
        Permission::EDIT_INSTRUCTOR,
        Permission::DELETE_INSTRUCTOR,
        Permission::VIEW_INSTRUCTOR,
    ], [Permission::VIEW_INSTRUCTOR]);

    // Create test branches for branch-related tests
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    $this->designation1 = Designation::factory()->create([
        'name' => 'Test Designation 1',
    ]);

    $this->designation2 = Designation::factory()->create([
        'name' => 'Test Designation 2',
    ]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    $this->routeNamePrefix = 'instructors';
    $this->table = Instructor::class;
    $this->mediaTable = Media::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access instructor endpoints', function () {
    $instructor = Instructor::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['instructor' => $instructor->id])],
        ['POST', route("{$this->routeNamePrefix}.reorder")],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create instructor without user', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'bio' => $data['bio'],
            'priority' => $data['priority'],
            'designation_id' => $data['designation_id'],
            'active' => $data['active'],

        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'bio' => $data['bio'],
        'priority' => $data['priority'],
        'designation_id' => $data['designation_id'],
        'active' => $data['active'],
    ]);

    $instructor = Instructor::query()->find($response['data']['id']);

    expect($instructor->branches()->count())->toBe(2)
        ->and($instructor->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);
});

test('create instructor with link existing user', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor_user = User::factory()->create();
    $instructor_user->assignRole([Role::INSTRUCTOR]);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'id' => $instructor_user->id,
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'bio' => $data['bio'],
            'priority' => $data['priority'],
            'designation_id' => $data['designation_id'],
            'active' => $data['active'],
            'user_id' => $data['user']['id'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'bio' => $data['bio'],
        'priority' => $data['priority'],
        'designation_id' => $data['designation_id'],
        'active' => $data['active'],
        'user_id' => $data['user']['id'],
    ]);
});

test('create instructor with link existing user that is linked to another instructor', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor_user = User::factory()->create();
    $instructor_user->assignRole([Role::INSTRUCTOR]);

    Instructor::factory()->create([
        'user_id' => $instructor_user->id,
    ]);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'id' => $instructor_user->id,
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedGeneralResponse(15001, 'User is linked with another stylist before.');
});

test('create instructor with link new user', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'email' => '<EMAIL>',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'phone' => '+60182938313',
            'password' => '123455678',
            'country_code' => 'MY',
            'dob' => '1990-01-01',
            'roles' => [Role::CUSTOMER],
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'bio' => $data['bio'],
            'priority' => $data['priority'],
            'designation_id' => $data['designation_id'],
            'active' => $data['active'],
        ])
        ->and($response['data']['user_id'])->not->toBeNull();


    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'bio' => $data['bio'],
        'priority' => $data['priority'],
        'designation_id' => $data['designation_id'],
        'active' => $data['active'],
    ]);

    $instructor = Instructor::query()->find($response['data']['id']);

    expect($instructor->user()->count())->toBe(1)
        ->and($instructor->user->toArray())->toMatchArray([
            'email' => $data['user']['email'],
            'phone' => $data['user']['phone'],
            'firstname' => $data['user']['firstname'],
            'lastname' => $data['user']['lastname'],
            'country_code' => $data['user']['country_code'],
        ])
        ->and($instructor->user->dob->toDateString())->toBe(Carbon::parse($data['user']['dob'])->toDateString())
        //Instructor role will automatic assigned to new created user
        ->and($instructor->user->roles->pluck('name')->toArray())->toContain(Role::INSTRUCTOR, Role::CUSTOMER);
});

test('create instructor with link existing user without role instructor', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor_user = User::factory()->create();
    $instructor_user->assignRole([Role::CUSTOMER]);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'id' => $instructor_user->id,
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedGeneralResponse(15002, 'Only user with Instructor role allowed to link.');
});

test('create instructor validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
        'user' => [
            'email' => '<EMAIL>',
        ],
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
        'active' => ['The active field is required.'],
        'user.firstname' => ['The user.firstname field is required.'],
        'user.lastname' => ['The user.lastname field is required.'],
        'user.password' => ['The user.password field is required.'],
    ]);
});

test('create instructor validates with non-existent fields', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Tony',
        'active' => true,
        'designation_id' => 9999,
        'branches' => [9999],
        'user' => [
            'id' => 9999,
            'phone' => '+60182938313',
            'roles' => ['non-existent-role'],
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'branches.0' => ['The selected branches.0 is invalid.'],
        'user.id' => ['The selected user.id is invalid.'],
        'designation_id' => ['The selected designation id is invalid.'],
        'user.roles.0' => ['The selected user.roles.0 is invalid.'],
    ]);
});

test('create instructor with new user validates email', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'email' => 'xx-xxx',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'user.email' => ['The user.email must be a valid email address.'],
    ]);

    //duplicate email
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'email' => $this->adminUser->email,
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'user.email' => ['The user.email has already been taken.'],
    ]);
});

test('create instructor with new user Gmail address', function () {
    Sanctum::actingAs($this->adminUser);

    // First create a user with a Gmail address
    User::factory()->create([
        'email' => '<EMAIL>',
        'firstname' => 'Existing',
        'lastname' => 'User',
    ]);

    // Try to create another user with the same Gmail but with dots
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $email_list = [
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
    ];

    foreach ($email_list as $email) {
        $data['user']['email'] = $email;

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'user.email' => ['The email has already been taken (Gmail addresses with dots/plus signs are considered identical).'],
        ]);
    }

    //Success
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'email' => '<EMAIL>',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('create instructor with new user validates phone number', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $phone_list = [
        '************', // Invalid format without country code
        'phone' => '+1234', // Too short
    ];

    foreach ($phone_list as $phone) {
        $data['user']['phone'] = $phone;

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'user.phone' => ['The user.phone is not a valid phone number.'],
        ]);
    }

    //duplicate phone
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'phone' => $this->adminUser->phone,
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'user.phone' => ['The user.phone has already been taken.'],
    ]);

    //Success
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'phone' => '+60182938313',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});



test('update instructor without user', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create([
        'name' => 'Tony Old',
        'bio' => 'Tony the best Old',
        'priority' => 2,
        'designation_id' => $this->designation2->id,
        'active' => false,
    ]);

    $instructor->branches()->sync([$this->branch2->id]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'bio' => $data['bio'],
            'priority' => $data['priority'],
            'designation_id' => $data['designation_id'],
            'active' => $data['active'],

        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'bio' => $data['bio'],
        'priority' => $data['priority'],
        'designation_id' => $data['designation_id'],
        'active' => $data['active'],
    ]);

    expect($instructor->branches()->count())->toBe(1)
        ->and($instructor->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

test('update instructor with link existing user', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create([
        'name' => 'Tony Old',
        'bio' => 'Tony the best Old',
        'priority' => 2,
        'designation_id' => $this->designation2->id,
        'active' => false,
    ]);

    $instructor_user = User::factory()->create();
    $instructor_user->assignRole([Role::INSTRUCTOR]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'id' => $instructor_user->id,
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'bio' => $data['bio'],
            'priority' => $data['priority'],
            'designation_id' => $data['designation_id'],
            'active' => $data['active'],
            'user_id' => $data['user']['id'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'bio' => $data['bio'],
        'priority' => $data['priority'],
        'designation_id' => $data['designation_id'],
        'active' => $data['active'],
        'user_id' => $data['user']['id'],
    ]);
});

test('update instructor with link existing user that is linked to another instructor', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $instructor_user = User::factory()->create();
    $instructor_user->assignRole([Role::INSTRUCTOR]);

    Instructor::factory()->create([
        'user_id' => $instructor_user->id,
    ]);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'id' => $instructor_user->id,
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor]), $data)->json();

    expect($response)->toHaveFailedGeneralResponse(15001, 'User is linked with another stylist before.');
});

test('update instructor with link new user', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create([
        'name' => 'Tony Old',
        'bio' => 'Tony the best Old',
        'priority' => 2,
        'designation_id' => $this->designation2->id,
        'active' => false,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'email' => '<EMAIL>',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'phone' => '+60182938313',
            'password' => '123455678',
            'country_code' => 'MY',
            'dob' => '1990-01-01',
            'roles' => [Role::CUSTOMER],
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'bio' => $data['bio'],
            'priority' => $data['priority'],
            'designation_id' => $data['designation_id'],
            'active' => $data['active'],
        ])
        ->and($response['data']['user_id'])->not->toBeNull();


    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'bio' => $data['bio'],
        'priority' => $data['priority'],
        'designation_id' => $data['designation_id'],
        'active' => $data['active'],
    ]);

    $instructor = Instructor::query()->find($response['data']['id']);

    expect($instructor->user()->count())->toBe(1)
        ->and($instructor->user->toArray())->toMatchArray([
            'email' => $data['user']['email'],
            'phone' => $data['user']['phone'],
            'firstname' => $data['user']['firstname'],
            'lastname' => $data['user']['lastname'],
            'country_code' => $data['user']['country_code'],
        ])
        ->and($instructor->user->dob->toDateString())->toBe(Carbon::parse($data['user']['dob'])->toDateString())
        //Instructor role will automatic assigned to new created user
        ->and($instructor->user->roles->pluck('name')->toArray())->toContain(Role::INSTRUCTOR, Role::CUSTOMER);
});

test('update instructor with link existing user without role instructor', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $instructor_user = User::factory()->create();
    $instructor_user->assignRole([Role::CUSTOMER]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'name' => 'Tony',
        'bio' => 'Tony the best',
        'branches' => [
            $this->branch1->id,
            $this->branch2->id,
        ],
        'priority' => 1,
        'designation_id' => $this->designation1->id,
        'active' => true,
        'user' => [
            'id' => $instructor_user->id,
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveFailedGeneralResponse(15002, 'Only user with Instructor role allowed to link.');
});

test('update instructor validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), [
        'user' => [
            'email' => '<EMAIL>',
        ],
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
        'active' => ['The active field is required.'],
        'user.firstname' => ['The user.firstname field is required.'],
        'user.lastname' => ['The user.lastname field is required.'],
        'user.password' => ['The user.password field is required.'],
    ]);
});

test('update instructor validates with non-existent fields', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $data = [
        'name' => 'Tony',
        'active' => true,
        'designation_id' => 9999,
        'branches' => [9999],
        'user' => [
            'id' => 9999,
            'phone' => '+60182938313',
            'roles' => ['non-existent-role'],
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'branches.0' => ['The selected branches.0 is invalid.'],
        'user.id' => ['The selected user.id is invalid.'],
        'designation_id' => ['The selected designation id is invalid.'],
        'user.roles.0' => ['The selected user.roles.0 is invalid.'],
    ]);
});

test('update instructor with new user validates email', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'email' => 'xx-xxx',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'user.email' => ['The user.email must be a valid email address.'],
    ]);

    //duplicate email
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'email' => $this->adminUser->email,
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'user.email' => ['The user.email has already been taken.'],
    ]);
});

test('update instructor with new user Gmail address', function () {
    Sanctum::actingAs($this->adminUser);

    // First create a user with a Gmail address
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'firstname' => 'Existing',
        'lastname' => 'User',
    ]);

    $instructor = Instructor::factory()->create([
        'user_id' => $user->id,
    ]);

    // Try to create another user with the same Gmail but with dots
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $email_list = [
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
    ];

    foreach ($email_list as $email) {
        $data['user']['email'] = $email;

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'user.email' => ['The email has already been taken (Gmail addresses with dots/plus signs are considered identical).'],
        ]);
    }

    //Success
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'email' => '<EMAIL>',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('update instructor with new user validates phone number', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $phone_list = [
        '************', // Invalid format without country code
        'phone' => '+1234', // Too short
    ];

    foreach ($phone_list as $phone) {
        $data['user']['phone'] = $phone;

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'user.phone' => ['The user.phone is not a valid phone number.'],
        ]);
    }

    //duplicate phone
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'phone' => $this->adminUser->phone,
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'user.phone' => ['The user.phone has already been taken.'],
    ]);

    //Success
    $data = [
        'name' => 'Tony',
        'active' => true,
        'user' => [
            'phone' => '+60182938313',
            'firstname' => 'Tony',
            'lastname' => 'Wong',
            'password' => '123455678',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['instructor' => $instructor->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});




test('delete instructor', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor = Instructor::factory()->create();

    $this->assertDatabaseCount($this->table, 1);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['instructor' => $instructor->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 0);

    $this->assertDatabaseMissing($this->table, [
        'id' => $instructor->id,
    ]);
});

test('delete nonexistent instructor returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['instructor' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('reorder instructors', function () {
    Sanctum::actingAs($this->adminUser);

    $instructor1 = Instructor::factory()->create([
        'order_column' => 1,
    ]);

    $instructor2 = Instructor::factory()->create([
        'order_column' => 2,
    ]);

    $instructor3 = Instructor::factory()->create([
        'order_column' => 3,
    ]);

    $data = [
        [
            'id' => $instructor1->id,
            'order_column' => 2,
        ],
        [
            'id' => $instructor2->id,
            'order_column' => 3,
        ],
        [
            'id' => $instructor3->id,
            'order_column' => 1,
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.reorder"), [
        'instructors' => $data,
    ])->json();

    expect($response)->toHaveSuccessGeneralResponse();

    foreach ($data as $item) {
        $this->assertDatabaseHas($this->table, [
            'id' => $item['id'],
            'order_column' => $item['order_column'],
        ]);
    }
});

test('reorder instructors validate required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.reorder"), [
        'instructors' => [],
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'instructors' => [
            'The instructors field is required.',
        ],
    ]);

    $response = $this->postJson(route("{$this->routeNamePrefix}.reorder"), [
        'instructors' => [
            [],
        ],
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'instructors.0.id' => [
            "The instructors.0.id field is required.",
        ],
        'instructors.0.order_column' => [
            "The instructors.0.order_column field is required.",
        ],
    ]);
});

test('reorder instructors validate invalid instructors', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.reorder"), [
        'instructors' => [],
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'instructors' => [
            'The instructors field is required.',
        ],
    ]);

    $response = $this->postJson(route("{$this->routeNamePrefix}.reorder"), [
        'instructors' => [
            [
                'id' => 9999,
                'order_column' => 1,
            ],
        ],
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'instructors.0.id' => [
            "The selected instructors.0.id is invalid.",
        ],
    ]);
});

describe('media', function () {
    test('can create instructor with media', function () {
        Sanctum::actingAs($this->adminUser);

        $file = \Illuminate\Http\UploadedFile::fake()->image('test-instructor.png');

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'John Doe',
            'bio' => 'Test instructor bio',
            'active' => true,
            'media' => [
                'avatar' => [
                    [
                        'file' => $file,
                        'order_column' => 1
                    ]
                ]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $instructor = Instructor::latest()->first();
        expect($instructor->getMedia('avatar'))->toHaveCount(1);
        expect($instructor->getFirstMediaUrl('avatar'))->not()->toBeEmpty();
    });

    test('can update instructor with media', function () {
        Sanctum::actingAs($this->adminUser);

        $instructor = Instructor::factory()->create();
        $file = \Illuminate\Http\UploadedFile::fake()->image('updated-instructor.png');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $instructor->id), [
            'name' => 'Updated John Doe',
            'bio' => $instructor->bio,
            'active' => $instructor->active,
            'media' => [
                'avatar' => [
                    [
                        'file' => $file,
                        'order_column' => 1
                    ]
                ]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $instructor->refresh();
        expect($instructor->getMedia('avatar'))->toHaveCount(1);
        expect($instructor->getFirstMediaUrl('avatar'))->not()->toBeEmpty();
    });

    test('can delete media when updating instructor', function () {
        Sanctum::actingAs($this->adminUser);

        $instructor = Instructor::factory()->create();

        // First add media using fake file
        $file = \Illuminate\Http\UploadedFile::fake()->image('test-instructor.png');
        $instructor->addMedia($file->getPathname())
            ->usingFileName('test-instructor.png')
            ->toMediaCollection('avatar');

        $media = $instructor->getFirstMedia('avatar');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $instructor->id), [
            'name' => $instructor->name,
            'bio' => $instructor->bio,
            'active' => $instructor->active,
            'media' => [
                'delete' => [$media->id]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $instructor->refresh();
        expect($instructor->getMedia('avatar'))->toHaveCount(0);
    });
});
