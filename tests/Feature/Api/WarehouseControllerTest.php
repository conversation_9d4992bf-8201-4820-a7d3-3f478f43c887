<?php

use App\Enums\Permission;
use App\Models\Warehouse;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_WAREHOUSE,
        Permission::EDIT_WAREHOUSE,
        Permission::DELETE_WAREHOUSE,
        Permission::VIEW_WAREHOUSE,
    ], [Permission::VIEW_WAREHOUSE]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // User-specific setup
    $this->routeNamePrefix = 'warehouses';
    $this->table = Warehouse::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access warehouse endpoints', function () {
    $warehouse = Warehouse::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['warehouse' => $warehouse->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['warehouse' => $warehouse->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

describe('create', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'location' => $data['location'],
                'description' => $data['description'],
            ]);

        $this->assertDatabaseCount($this->table, 1);

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'location' => $data['location'],
            'description' => $data['description'],
        ]);
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
            'location' => ['The location field is required.'],
        ]);
    });

    test('with empty description', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => null,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'location' => $data['location'],
                'description' => $data['description'],
            ]);

        $this->assertDatabaseCount($this->table, 1);

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'location' => $data['location'],
            'description' => $data['description'],
        ]);
    });
});

describe('update', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);

        $this->assertDatabaseCount($this->table, 1);

        $data = [
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['warehouse' => $warehouse->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'location' => $data['location'],
                'description' => $data['description'],
            ]);

        $this->assertDatabaseCount($this->table, 1);
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);

        $data = [];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['warehouse' => $warehouse->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
            'location' => ['The location field is required.'],
        ]);
    });

    test('with empty description', function () {
        Sanctum::actingAs($this->adminUser);

        $warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);

        $this->assertDatabaseCount($this->table, 1);

        $data = [
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => null,
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['warehouse' => $warehouse->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'location' => $data['location'],
                'description' => $data['description'],
            ]);

        $this->assertDatabaseCount($this->table, 1);
    });
});

describe('delete', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $warehouse = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);

        $this->assertDatabaseCount($this->table, 1);

        $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['warehouse' => $warehouse->id]))->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, [
            'id' => $warehouse->id,
        ]);
    });

    test('nonexistent warehouse', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['warehouse' => 9999]))->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});

