<?php

use App\Enums\NotificationChannel;
use App\Enums\Permission;
use App\Enums\Role as RoleEnum;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Notifications\GenericNotification;
use Illuminate\Support\Facades\Notification;
use <PERSON>vel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);

    // Create permissions
    PermissionModel::create(['name' => Permission::VIEW_USER, 'guard_name' => 'api']);
    PermissionModel::create(['name' => Permission::EDIT_USER, 'guard_name' => 'api']);

    // Create welcome notification template for testing
    NotificationTemplate::factory()->create([
        'name' => 'welcome_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Welcome, {{user_name}}!',
        'content' => 'Hello {{user_name}}, welcome! Phone: {{phone_number}}, Email: {{email}}',
        'is_active' => true,
    ]);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::SUPER_ADMIN);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);

    $this->instructorUser = User::factory()->create([
        'firstname' => 'Instructor',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $this->instructorUser->assignRole(RoleEnum::INSTRUCTOR);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    $this->inactiveUser = User::factory()->create([
        'firstname' => 'Inactive',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => false,
    ]);
    $this->inactiveUser->assignRole(RoleEnum::STAFF);

    $this->routeNamePrefix = 'auth';
});

test('admin can login successfully', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ])->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['token'])->not->toBeEmpty();
});

test('staff can login successfully', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ])->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['token'])->not->toBeEmpty();
});

test('instructor can login successfully', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ])->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['token'])->not->toBeEmpty();
});

test('login fails with invalid credentials', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
        'password' => 'wrongpassword',
    ])->json();

    expect($response)->toHaveFailedGeneralResponse(14002, 'Invalid credentials.');
});

test('login fails with inactive user', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ])->json();

    expect($response)->toHaveFailedGeneralResponse(14002, 'Invalid credentials.');
});

test('login fails with missing email', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'password' => 'password123',
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email field is required.'],
    ]);
});

test('login fails with missing password', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'password' => ['The password field is required.'],
    ]);
});

test('login fails with invalid email format', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => 'invalid-email',
        'password' => 'password123',
    ])->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email must be a valid email address.'],
    ]);
});

test('authenticated user can logout', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("$this->routeNamePrefix.logout"))->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('unauthenticated user cannot logout', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.logout"))->json();

    expect($response)->toHaveUnauthenticatedResponse();
});

test('login returns valid token structure', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.login"), [
        'email' => '<EMAIL>',
        'password' => 'password123',
    ]);

    $token = $response->json('data.token');

    // Verify token can be used for authentication
    $response = $this->postJson(route("$this->routeNamePrefix.logout"), headers: [
        'Authorization' => 'Bearer ' . $token,
    ])->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

// Register Tests
test('register creates new user with customer role using existing service methods', function () {
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '+60123456789',
        'dob' => '1990-01-01',
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['token'])->not->toBeEmpty()
        ->and($response['data']['token_type'])->toBe('Bearer');

    // Verify user was created with correct data
    $this->assertDatabaseHas('users', [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'phone' => '+60123456789',
        'is_active' => true,
    ]);

    // Verify user has customer role assigned by UserService
    $user = User::where('email', '<EMAIL>')->first();
    expect($user->hasRole(RoleEnum::CUSTOMER))->toBe(true);
});

test('register validates required fields', function () {
    $response = $this->postJson(route("$this->routeNamePrefix.register"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'firstname' => ['The firstname field is required.'],
        'lastname' => ['The lastname field is required.'],
        'email' => ['The email field is required.'],
        'password' => ['The password field is required.'],
    ]);
});

test('register validates unique email', function () {
    // Create existing user
    User::factory()->create(['email' => '<EMAIL>']);

    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email has already been taken.'],
    ]);
});

test('register validates password minimum length', function () {
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'password' => '123', // Too short
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'password' => ['The password must be at least 8 characters.'],
    ]);
});

test('register returns valid token that can be used for authentication', function () {
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
    $token = $response['data']['token'];

    // Test that the token works for authenticated requests
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token,
    ])->postJson(route("$this->routeNamePrefix.logout"))->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('register validate gmail address', function () {
    // First create a user with a Gmail address
    User::factory()->create([
        'email' => '<EMAIL>',
        'firstname' => 'Existing',
        'lastname' => 'User',
    ]);

    // Try to create another user with the same Gmail but with dots
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'password' => 'password123',
    ];

    $email_list = [
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized,
    ];

    foreach ($email_list as $email) {
        $data['email'] = $email;

        $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'email' => ['The email has already been taken (Gmail addresses with dots/plus signs are considered identical).'],
        ]);
    }

    //case sensitive
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'password' => 'password123',
        'email' => '<EMAIL>', //case sensitive
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => [
            'The email has already been taken.',
            'The email has already been taken (Gmail addresses with dots/plus signs are considered identical).',
        ],
    ]);

    //create user with unique Gmail address succeeds
    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'password' => 'password123',
        'email' => '<EMAIL>',
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('register sends welcome email notification', function () {
    Notification::fake();

    $data = [
        'firstname' => 'John',
        'lastname' => 'Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '+60123456789',
        'dob' => '1990-01-01',
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.register"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['token'])->not->toBeEmpty();

    // Verify user was created
    $user = User::where('email', '<EMAIL>')->first();
    expect($user)->not->toBeNull();

    // Verify welcome notification was sent
    Notification::assertSentTo(
        $user,
        GenericNotification::class,
        function ($notification) use ($user) {
            // Verify notification contains correct template data
            $notificationArray = $notification->toArray($user);
            $templateData = $notificationArray['template_data'];
            return $templateData['user_name'] === $user->name &&
                $templateData['phone_number'] === $user->phone &&
                $templateData['email'] === $user->email;
        }
    );
});
