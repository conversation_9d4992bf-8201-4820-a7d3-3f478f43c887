<?php

use App\Enums\Permission;
use App\Models\Branch;
use App\Models\BranchDetails;
use App\Models\User;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_BRANCH,
        Permission::EDIT_BRANCH,
        Permission::DELETE_BRANCH,
        Permission::VIEW_BRANCH,
    ], [Permission::VIEW_BRANCH]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->staffUser = $users->get('staff_user');
    $this->customerUser = $users->get('customer_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // Branch-specific setup
    $this->routeNamePrefix = 'branches';
    $this->table = Branch::class;
    $this->detailsTable = BranchDetails::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('authorized user can create branch', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Downtown Branch',
        'geolocation' => [
            'lat' => 40.7128,
            'lng' => -74.0060,
            'address' => '123 Main Street, New York, NY 10001',
        ],
        'active' => true,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response['data'])->toMatchArray([
        'name' => $data['name'],
        'active' => $data['active'],
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'active' => $data['active'],
    ]);
});

test('create branch validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
    ]);
});

test('create branch validates geolocation structure', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Downtown Branch',
        'geolocation' => [
            'lat' => 'invalid-latitude', // Should be numeric
            'lng' => 200, // Should be between -180 and 180
        ],
        'active' => true,
        'virtual' => false,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'geolocation.lat' => ['The geolocation.lat must be a number.'],
        'geolocation.lng' => ['The geolocation.lng must be between -180 and 180.'],
    ]);
});

test('create branch with valid geolocation passes', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Downtown Branch',
        'geolocation' => [
            'lat' => 40.7128,
            'lng' => -74.0060,
            'address' => '123 Main Street, New York, NY 10001',
        ],
        'active' => true,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response['data'])->toMatchArray([
        'name' => $data['name'],
        'active' => $data['active'],
    ]);
});

test('authorized user can update branch', function () {
    Sanctum::actingAs($this->adminUser);

    $branch = Branch::factory()->create([
        'name' => 'Old Branch',
        'active' => false,
    ]);

    $data = [
        'name' => 'Updated Branch',
        'active' => true,
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['branch' => $branch->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'active' => $data['active'],
        ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $branch->id,
        'name' => $data['name'],
        'active' => $data['active'],
    ]);
});

test('authorized user can delete branch', function () {
    Sanctum::actingAs($this->adminUser);

    $branch = Branch::factory()->create([
        'name' => 'Delete Me',
    ]);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['branch' => $branch->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseMissing($this->table, ['id' => $branch->id]);
});

test('delete nonexistent branch returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['branch' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('authorized user can create branch with details', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Downtown Branch',
        'active' => true,
        'details' => [
            'company_name' => 'Downtown Branch LLC',
            'company_registration_no' => '1234567890',
            'company_address' => '123 Main Street, New York, NY 10001',
            'company_phone' => '+12125551234',
            'company_email' => '<EMAIL>',
        ],
    ];

    $this->assertDatabaseCount($this->table, 0);
    $this->assertDatabaseCount($this->detailsTable, 0);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response['data'])->toMatchArray([
        'name' => $data['name'],
    ])
        ->and($response['data']['details'])->toMatchArray([
            'company_name' => $data['details']['company_name'],
            'company_registration_no' => $data['details']['company_registration_no'],
            'company_address' => $data['details']['company_address'],
            'company_phone' => $data['details']['company_phone'],
            'company_email' => $data['details']['company_email'],
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->detailsTable, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
    ]);

    $this->assertDatabaseHas($this->detailsTable, [
        'company_name' => $data['details']['company_name'],
        'company_registration_no' => $data['details']['company_registration_no'],
        'company_address' => $data['details']['company_address'],
        'company_phone' => $data['details']['company_phone'],
        'company_email' => $data['details']['company_email'],
    ]);
});

test('authorized user can update branch with details', function () {
    Sanctum::actingAs($this->adminUser);

    $branch = Branch::factory()->withDetails()->create();

    $data = [
        'name' => 'Updated Branch',
        'active' => true,
        'details' => [
            'company_name' => 'Updated Branch LLC',
            'company_registration_no' => '0987654321',
            'company_address' => '456 Updated Street, New York, NY 10002',
            'company_phone' => '+12125559876',
            'company_email' => '<EMAIL>',
        ],
    ];

    $this->assertDatabaseCount($this->detailsTable, 1);

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['branch' => $branch->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
        ])
        ->and($response['data']['details'])->toMatchArray([
            'company_name' => $data['details']['company_name'],
            'company_registration_no' => $data['details']['company_registration_no'],
            'company_address' => $data['details']['company_address'],
            'company_phone' => $data['details']['company_phone'],
            'company_email' => $data['details']['company_email'],
        ]);

    // Should still be only one details record (updated, not created new)
    $this->assertDatabaseCount($this->detailsTable, 1);

    $this->assertDatabaseHas($this->table, [
        'id' => $branch->id,
        'name' => $data['name'],
    ]);

    $this->assertDatabaseHas($this->detailsTable, [
        'branch_id' => $branch->id,
        'company_name' => $data['details']['company_name'],
        'company_registration_no' => $data['details']['company_registration_no'],
        'company_address' => $data['details']['company_address'],
        'company_phone' => $data['details']['company_phone'],
        'company_email' => $data['details']['company_email'],
    ]);
});

test('create branch validates details fields', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Downtown Branch',
        'details' => [
            'company_name' => '', // Required field
            'company_email' => 'invalid-email', // Invalid email format
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'details.company_name' => ['The details.company name field is required when details is present.'],
        'details.company_email' => ['The details.company email must be a valid email address.'],
    ]);
});

test('create branch accepts nullable and short company registration number', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Test Branch',
        'active' => true,
        'details' => [
            'company_name' => 'Test Company',
            'company_registration_no' => '123', // Short registration number should be accepted
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response['data'])->toMatchArray([
        'name' => $data['name'],
    ])
        ->and($response['data']['details']['company_registration_no'])->toBe('123');

    // Test with null/missing registration number
    $data2 = [
        'name' => 'Test Branch 2',
        'active' => true,
        'details' => [
            'company_name' => 'Test Company 2',
            // company_registration_no is omitted (nullable)
        ],
    ];

    $response2 = $this->postJson(route("{$this->routeNamePrefix}.store"), $data2)->json();

    expect($response2['data'])->toMatchArray([
        'name' => $data2['name'],
    ])
        ->and($response2['data']['details']['company_registration_no'])->toBeNull();
});

test('unauthenticated user cannot access branch endpoints', function () {
    $branch = Branch::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['branch' => $branch->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['branch' => $branch->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create branch generates slug from name', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'Downtown Branch Location',
        'active' => true,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response['data']['slug'])->toBe('downtown-branch-location');

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'slug' => 'downtown-branch-location',
    ]);
});

test('create branch generates unique slug when duplicate exists', function () {
    Sanctum::actingAs($this->adminUser);

    // Create first branch
    Branch::factory()->create([
        'name' => 'Downtown Branch',
        'slug' => 'downtown-branch',
    ]);

    $data = [
        'name' => 'Downtown Branch',
        'active' => true,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response['data']['slug'])->not()->toBe('downtown-branch')
        ->and($response['data']['slug'])->toStartWith('downtown-branch-');

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'slug' => $response['data']['slug'],
    ]);
});

test('delete branch is prevented when it has associated users', function () {
    Sanctum::actingAs($this->adminUser);

    $branch = Branch::factory()->create([
        'name' => 'Branch with Users',
    ]);

    $user = User::factory()->create();

    // Associate user with branch
    $branch->users()->attach($user->id);

    // Attempt to delete via API
    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['branch' => $branch->id]))->json();

    // Should return error response
    expect($response)->toHaveFailedGeneralResponse(10001, 'This data cannot be deleted because it is being used.');

    // Verify branch still exists
    $this->assertDatabaseHas($this->table, ['id' => $branch->id]);
});

test('delete branch succeeds when no models are associated', function () {
    Sanctum::actingAs($this->adminUser);

    $branch = Branch::factory()->create([
        'name' => 'Branch without associations',
    ]);

    // Delete via API
    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['branch' => $branch->id]))->json();

    // Should succeed
    expect($response)->toHaveSuccessGeneralResponse();

    // Verify branch is deleted
    $this->assertDatabaseMissing($this->table, ['id' => $branch->id]);
});

test('delete branch succeeds after removing all associations', function () {
    Sanctum::actingAs($this->adminUser);

    $branch = Branch::factory()->create([
        'name' => 'Branch to clean and delete',
    ]);

    $user = User::factory()->create();

    // Associate user with branch
    $branch->users()->attach($user->id);

    // First attempt should fail
    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['branch' => $branch->id]))->json();

    expect($response)->toHaveFailedGeneralResponse(10001, 'This data cannot be deleted because it is being used.');

    // Remove the association
    $branch->users()->detach($user->id);

    // Second attempt should succeed
    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['branch' => $branch->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();
    $this->assertDatabaseMissing($this->table, ['id' => $branch->id]);
});

describe('media', function () {
    test('can create branch with media', function () {
        Sanctum::actingAs($this->adminUser);

        $file = \Illuminate\Http\UploadedFile::fake()->image('test-branch.png');

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'Test Branch',
            'geolocation' => [
                'lat' => 40.7128,
                'lng' => -74.0060,
                'address' => '123 Test Street, Test City, Test State 12345',
            ],
            'active' => true,
            'media' => [
                'image' => [
                    [
                        'file' => $file,
                        'order_column' => 1
                    ]
                ]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $branch = Branch::latest()->first();
        expect($branch->getMedia('image'))->toHaveCount(1);
        expect($branch->getFirstMediaUrl('image'))->not()->toBeEmpty();
    });

    test('can update branch with media', function () {
        Sanctum::actingAs($this->adminUser);

        $branch = Branch::factory()->create();
        $file = \Illuminate\Http\UploadedFile::fake()->image('updated-branch.png');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $branch->id), [
            'name' => 'Updated Branch',
            'geolocation' => $branch->geolocation,
            'active' => $branch->active,
            'media' => [
                'image' => [
                    [
                        'file' => $file,
                        'order_column' => 1
                    ]
                ]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $branch->refresh();
        expect($branch->getMedia('image'))->toHaveCount(1);
        expect($branch->getFirstMediaUrl('image'))->not()->toBeEmpty();
    });

    test('can delete media when updating branch', function () {
        Sanctum::actingAs($this->adminUser);

        $branch = Branch::factory()->create();

        // First add media using fake file
        $file = \Illuminate\Http\UploadedFile::fake()->image('test-branch.png');
        $branch->addMedia($file->getPathname())
            ->usingFileName('test-branch.png')
            ->toMediaCollection('image');

        $media = $branch->getFirstMedia('image');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $branch->id), [
            'name' => $branch->name,
            'geolocation' => $branch->geolocation,
            'active' => $branch->active,
            'media' => [
                'delete' => [$media->id]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $branch->refresh();
        expect($branch->getMedia('image'))->toHaveCount(0);
    });
});
