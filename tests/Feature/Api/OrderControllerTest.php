<?php

use App\Enums\OrderStatus as OrderStatusEnum;
use App\Enums\Permission;
use App\Enums\SettingType;
use App\Models\Branch;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\Setting;
use Carbon\Carbon;
use Database\Seeders\OrderStatusSeeder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        OrderStatusSeeder::class,
    ]);
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::VIEW_ORDER,
        Permission::CREATE_ORDER,
        Permission::EDIT_ORDER,
        Permission::DELETE_ORDER,
    ], [Permission::VIEW_ORDER]);

    // Create test branch for order-related tests
    $this->branch = Branch::factory()->create([
        'name' => 'Test Branch',
        'active' => true,
    ]);

    // Create test order status
    $this->orderStatus = OrderStatus::where('slug', OrderStatusEnum::PENDING)->first();

    // Create test products
    $this->product1 = Product::factory()->create([
        'name' => 'Test Product 1',
        'price' => 100.00,
    ]);

    $this->product2 = Product::factory()->create([
        'name' => 'Test Product 2',
        'price' => 50.00,
    ]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');
    $this->customerUser = $users->get('customer_user');

    $this->routeNamePrefix = 'orders';
    $this->table = Order::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access order endpoints', function () {
    $order = Order::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['order' => $order->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['order' => $order->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

describe('store', function () {
    test('with customer and products', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'branch_id' => $this->branch->id,
            'customer' => [
                'id' => $this->customerUser->id,
            ],
            'products' => [
                [
                    'id' => $this->product1->id,
                    'quantity' => 2,
                    'price' => 100.00,
                ],
                [
                    'id' => $this->product2->id,
                    'quantity' => 1,
                    'price' => 50.00,
                ],
            ],
            'order_status_id' => $this->orderStatus->id,
            'customer_name' => 'Test Customer',
            'customer_contact' => '+60123456789',
            'customer_email' => '<EMAIL>',
            'amount' => 250.00,
            'total' => 275.00,
            'billing_address' => [
                'street' => '123 Main St',
                'city' => 'Test City',
                'state' => 'Test State',
                'postal_code' => '12345',
                'country' => 'Test Country',
            ],
            'shipping_address' => [
                'street' => '456 Oak Ave',
                'city' => 'Test City',
                'state' => 'Test State',
                'postal_code' => '12345',
                'country' => 'Test Country',
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toHaveKey('id')
            ->and($response['data'])->toHaveKey('customer_name', 'Test Customer')
            ->and($response['data'])->toHaveKey('customer_email', '<EMAIL>')
            ->and($response['data'])->toHaveKey('amount', 250)
            ->and($response['data'])->toHaveKey('total', '275.00');

        $this->assertDatabaseCount($this->table, 1);

        $order = Order::query()->find($response['data']['id']);
        expect($order)->not->toBeNull()
            ->and($order->customer_name)->toBe('Test Customer')
            ->and($order->customer_email)->toBe('<EMAIL>');
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response)->toHaveFailedValidationResponse([
            'branch_id' => ['The branch id field is required.'],
            'customer' => ['The customer field is required.'],
            'products' => ['The products field is required.'],
        ]);
    });

    test('validates non-existent branch', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'branch_id' => 9999,
            'customer' => [
                'id' => $this->customerUser->id,
            ],
            'products' => [
                [
                    'id' => $this->product1->id,
                    'quantity' => 1,
                    'price' => 100.00,
                ],
            ],
            'order_status_id' => $this->orderStatus->id,
            'amount' => 100.00,
            'total' => 110.00,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'branch_id' => ['The selected branch id is invalid.'],
        ]);
    });

    test('validates non-existent product', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'branch_id' => $this->branch->id,
            'customer' => [
                'id' => $this->customerUser->id,
            ],
            'products' => [
                [
                    'id' => 9999,
                    'quantity' => 1,
                    'price' => 100.00,
                ],
            ],
            'order_status_id' => $this->orderStatus->id,
            'amount' => 100.00,
            'total' => 110.00,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'products.0.id' => ['The selected products.0.id is invalid.'],
        ]);
    });

    test('with new customer', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'branch_id' => $this->branch->id,
            'customer' => [
                'email' => '<EMAIL>',
                'phone' => '+60123456789',
            ],
            'products' => [
                [
                    'id' => $this->product1->id,
                    'quantity' => 1,
                    'price' => 100.00,
                ],
            ],
            'order_status_id' => $this->orderStatus->id,
            'customer_name' => 'New Customer',
            'customer_contact' => '+60123456789',
            'customer_email' => '<EMAIL>',
            'amount' => 100.00,
            'total' => 110.00,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toHaveKey('id')
            ->and($response['data'])->toHaveKey('customer_email', '<EMAIL>')
            ->and($response['data'])->toHaveKey('customer_contact', '+60123456789');

        $this->assertDatabaseCount($this->table, 1);
    });
});

describe('update', function () {
    test('update order', function () {
        Sanctum::actingAs($this->adminUser);

        $order = Order::factory()->create([
            'customer_id' => $this->customerUser->id,
            'amount' => 100.00,
            'total' => 110.00,
        ]);

        $this->assertDatabaseCount($this->table, 1);

        $data = [
            'customer_name' => 'Updated Customer Name',
            'customer_email' => '<EMAIL>',
            'customer_contact' => '+60123456789',
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['order' => $order->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'customer_name' => $data['customer_name'],
                'customer_email' => $data['customer_email'],
                'customer_contact' => $data['customer_contact'],
            ]);

        $this->assertDatabaseCount($this->table, 1);

        $this->assertDatabaseHas($this->table, [
            'id' => $order->id,
            'customer_name' => $data['customer_name'],
            'customer_email' => $data['customer_email'],
            'customer_contact' => $data['customer_contact'],
        ]);
    });

    test('update nonexistent order returns 404', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'customer_name' => 'Updated Customer Name',
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['order' => 9999]), $data)->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});

describe('delete', function () {
    test('delete order', function () {
        Sanctum::actingAs($this->adminUser);

        $order = Order::factory()->create();

        $this->assertDatabaseCount($this->table, 1);

        $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['order' => $order->id]))->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Order uses SoftDeletes, so it's still in database but marked as deleted
        $this->assertDatabaseCount($this->table, 1);

        $this->assertSoftDeleted($this->table, [
            'id' => $order->id,
        ]);
    });

    test('delete nonexistent order returns 404', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['order' => 9999]))->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});

describe('link products ', function () {
    beforeEach(function () {
        Setting::factory()->create([
            'type' => SettingType::AUTOCOUNT,
            'options' => [
                'accounts' => [
                    $this->branch->id => [
                        'book_id' => 'book123',
                        'api_key' => 'test-api-key',
                        'key_id' => 'test-key-id',
                        'debtor_code' => 'test-debtor-code',
                        'debtor_name' => 'test-debtor-name',
                        'credit_term' => 'test-credit-terms',
                        'sales_location' => 'test-sales-location',
                        'purchase_account_number' => 'test-purchase-account-number',
                    ],
                ],
            ],

        ]);
        // Set up AutoCount configuration for testing
        Config::set('services.autocount', [
            'base_url' => 'https://autocount-api.example.com',
        ]);

        Http::fake([
            'https://autocount-api.example.com/book12*/invoice' => Http::response('ok'),
        ]);
    });

    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();
        $complete_status = OrderStatus::query()->where('slug', OrderStatusEnum::COMPLETE)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order->branches()->sync([$this->branch->id]);

        $order_product = OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order->id,
        ]);

        $product = Product::factory()->create();

        $response = $this->postJson(route("{$this->routeNamePrefix}.link-products", ['order' => $order->id]), [
            'order_products' => [
                [
                    'id' => $order_product->id,
                    'product_id' => $product->id,
                ],
            ],
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        //order status updated to complete
        $this->assertDatabaseHas($this->table, [
            'id' => $order->id,
            'order_status_id' => $complete_status->id,
        ]);

        //order product link with product
        $this->assertDatabaseHas(OrderProduct::class, [
            'id' => $order_product->id,
            'product_id' => $product->id,
            'sku' => $product->sku,
        ]);
    });

    test('validate order status', function () {
        Sanctum::actingAs($this->adminUser);

        $order_statuses = OrderStatusEnum::getValues();
        $product = Product::factory()->create();

        foreach ($order_statuses as $order_status) {
            $status = OrderStatus::query()->where('slug', $order_status)->first();

            $order = Order::factory()->create([
                'order_status_id' => $status->id,
            ]);

            $order_product = OrderProduct::factory()->create([
                'product_id' => null,
                'order_id' => $order->id,
            ]);

            $response = $this->postJson(route("{$this->routeNamePrefix}.link-products", ['order' => $order->id]), [
                'order_products' => [
                    [
                        'id' => $order_product->id,
                        'product_id' => $product->id,
                    ],
                ],
            ])->json();

            switch ($order_status) {
                case OrderStatusEnum::PROCESSING:
                case OrderStatusEnum::PACKED:
                    expect($response)->toHaveSuccessGeneralResponse();
                    break;
                default:
                    expect($response)->toHaveFailedGeneralResponse(17002, 'Only processing order are enable to link products.');
            }
        }
    });

    test('validate order product exist', function () {
        Sanctum::actingAs($this->adminUser);

        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order2 = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order_product = OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order2->id,
        ]);

        $product = Product::factory()->create();

        $response = $this->postJson(route("{$this->routeNamePrefix}.link-products", ['order' => $order->id]), [
            'order_products' => [
                [
                    'id' => $order_product->id,
                    'product_id' => $product->id,
                ],
            ],
        ])->json();

        expect($response)->toHaveFailedGeneralResponse(17001, 'Order product does not match.');
    });

    test('validate order products must be exact as database', function () {
        Sanctum::actingAs($this->adminUser);

        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $order_product = OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order->id,
        ]);

        OrderProduct::factory()->create([
            'product_id' => null,
            'order_id' => $order->id,
        ]);

        $product = Product::factory()->create();

        $response = $this->postJson(route("{$this->routeNamePrefix}.link-products", ['order' => $order->id]), [
            'order_products' => [
                [
                    'id' => $order_product->id,
                    'product_id' => $product->id,
                ],
            ],
        ])->json();

        expect($response)->toHaveFailedGeneralResponse(17001, 'Order product does not match.');
    });

    test('validate exist', function () {
        Sanctum::actingAs($this->adminUser);

        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $response = $this->postJson(route("{$this->routeNamePrefix}.link-products", ['order' => $order->id]), [
            'order_products' => [
                [
                    'id' => 9999,
                    'product_id' => 9999,
                ],
            ],
        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'order_products.0.product_id' => ['The selected order_products.0.product_id is invalid.'],
            'order_products.0.id' => ['The selected order_products.0.id is invalid.'],
        ]);
    });

    test('validate required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $processing_status = OrderStatus::query()->where('slug', OrderStatusEnum::PROCESSING)->first();

        $order = Order::factory()->create([
            'order_status_id' => $processing_status->id,
        ]);

        $response = $this->postJson(route("{$this->routeNamePrefix}.link-products", ['order' => $order->id]), [

        ])->json();

        expect($response)->toHaveFailedValidationResponse([
            'order_products' => [
                'The order products field is required.',
            ],
        ]);
    });
});
