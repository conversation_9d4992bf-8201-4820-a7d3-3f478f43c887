<?php

use App\Enums\Permission;
use App\Models\Product;
use App\Models\ProductBundleItem;
use App\Models\ProductVariant;
use App\Models\Warehouse;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_PRODUCT,
        Permission::EDIT_PRODUCT,
        Permission::DELETE_PRODUCT,
        Permission::VIEW_PRODUCT,
    ], [Permission::VIEW_PRODUCT]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->staffUser = $users->get('staff_user');
    $this->customerUser = $users->get('customer_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // Product-specific setup
    $this->routeNamePrefix = 'products';
    $this->table = Product::class;
    $this->productVariantTable = ProductVariant::class;
    $this->mediaTable = Media::class;
    $this->bundleItemTable = ProductBundleItem::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access product endpoints', function () {
    $product = Product::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['product' => $product->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['product' => $product->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

describe('create', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Test Product',
            'description' => 'This is a test product description',
            'price' => 99.99,
            'sale_price' => 79.99,
            'sku' => 'TEST-SKU-001',
            'barcode' => '1234567890123',
            'is_taxable' => true,
            'status' => 'publish',
            'unit' => 'piece',
            'height' => 10.5,
            'width' => 5.2,
            'length' => 15.0,
            'weight' => 2.500,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'description' => $data['description'],
                'price' => $data['price'],
                'sale_price' => $data['sale_price'],
                'sku' => $data['sku'],
                'barcode' => $data['barcode'],
                'is_taxable' => $data['is_taxable'],
                'status' => $data['status'],
                'unit' => $data['unit'],
                'height' => $data['height'],
                'width' => $data['width'],
                'length' => $data['length'],
                'weight' => $data['weight'],
            ]);

        $this->assertDatabaseCount($this->table, 1);

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'description' => $data['description'],
            'sku' => $data['sku'],
            'barcode' => $data['barcode'],
            'is_taxable' => $data['is_taxable'],
            'status' => $data['status'],
            'price' => $data['price'],
            'sale_price' => $data['sale_price'],
            'unit' => $data['unit'],
            'height' => $data['height'],
            'width' => $data['width'],
            'length' => $data['length'],
            'weight' => $data['weight'],
        ]);
    });

    test('with variants', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Product with Variants',
            'status' => 'publish',
            'variants' => [
                [
                    'title' => 'Small',
                    'price' => '50.00',
                    'sale_price' => '40.00',
                    'sku' => 'TEST-SMALL',
                    'is_active' => true,
                ],
                [
                    'title' => 'Large',
                    'price' => '80.00',
                    'sku' => 'TEST-LARGE',
                    'is_active' => true,
                ],
            ],
        ];

        $this->assertDatabaseCount($this->productVariantTable, 0);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Get the created product ID from response
        $product_id = $response['data']['id'];

        $this->assertDatabaseCount($this->productVariantTable, 2);

        foreach ($data['variants'] as $variant) {
            $this->assertDatabaseHas($this->productVariantTable, [
                'product_id' => $product_id,
                'title' => $variant['title'],
                'price' => $variant['price'],
                'sku' => $variant['sku'],
                'is_active' => $variant['is_active'],
            ]);
        }
    });

    test('with meta data', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Product with Meta',
            'status' => 'publish',
            'meta' => [
                'featured' => true,
                'tags' => ['electronics', 'gadget'],
                'color' => 'blue',
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();
        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['meta'])->toMatchArray($data['meta']);

        $this->assertDatabaseHas($this->table, [
            'id' => $response['data']['id'],
            'meta->featured' => $data['meta']['featured'],
            'meta->tags' => json_encode($data['meta']['tags']),
            'meta->color' => $data['meta']['color'],
        ]);
    });

    test('create bundle product with bundle items', function () {
        Sanctum::actingAs($this->adminUser);

        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        $this->assertDatabaseCount($this->bundleItemTable, 0);

        $data = [
            'name' => 'Test Bundle Product',
            'status' => 'publish',
            'is_bundle' => true,
            'bundle_items' => [
                ['item_product_id' => (string)$item1->id, 'quantity' => 2],
                ['item_product_id' => (string)$item2->id, 'quantity' => 3],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['is_bundle'])->toBeTrue();

        $product_id = $response['data']['id'];

        // Verify bundle items were created
        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $product_id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $product_id,
            'item_product_id' => $item2->id,
            'quantity' => 3,
        ]);
    });

    test('create bundle product with multiple different items', function () {
        Sanctum::actingAs($this->adminUser);

        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();

        $this->assertDatabaseCount($this->bundleItemTable, 0);

        $data = [
            'name' => 'Bundle with Multiple Items',
            'status' => 'publish',
            'is_bundle' => true,
            'bundle_items' => [
                ['item_product_id' => (string)$item1->id, 'quantity' => 2],
                ['item_product_id' => (string)$item2->id, 'quantity' => 1],
                ['item_product_id' => (string)$item3->id, 'quantity' => 3],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $product_id = $response['data']['id'];

        // Should create all three different items
        $this->assertDatabaseCount($this->bundleItemTable, 3);

        // Verify each item was created with correct quantity
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $product_id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $product_id,
            'item_product_id' => $item2->id,
            'quantity' => 1,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $product_id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);
    });

    test('create bundle product with empty bundle items', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Bundle with No Items',
            'status' => 'publish',
            'is_bundle' => true,
            'bundle_items' => [],
        ];

        $this->assertDatabaseCount($this->bundleItemTable, 0);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['is_bundle'])->toBeTrue();

        $product_id = $response['data']['id'];

        // Verify no bundle items were created
        $this->assertDatabaseCount($this->bundleItemTable, 0);
    });

    test('create regular product ignores bundle items', function () {
        Sanctum::actingAs($this->adminUser);

        $item1 = Product::factory()->create();

        $data = [
            'name' => 'Regular Product',
            'status' => 'publish',
            'is_bundle' => false,
            'bundle_items' => [
                ['item_product_id' => (string)$item1->id, 'quantity' => 2],
            ],
        ];

        $this->assertDatabaseCount($this->bundleItemTable, 0);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['is_bundle'])->toBeFalse();

        $product_id = $response['data']['id'];

        // Bundle items are ignored for regular products (controller enforces bundle requirement)
        $this->assertDatabaseCount($this->bundleItemTable, 0);
    });

    test('create bundle product validates bundle items', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Bundle with Invalid Items',
            'status' => 'publish',
            'is_bundle' => true,
            'bundle_items' => [
                ['item_product_id' => 999999, 'quantity' => 2], // Non-existent product
                ['item_product_id' => '', 'quantity' => 3], // Empty product_id
                ['quantity' => 1], // Missing product_id
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'bundle_items.0.item_product_id' => ['The selected bundle_items.0.item_product_id is invalid.'],
            'bundle_items.1.item_product_id' => ['The bundle_items.1.item_product_id field is required when bundle items is present.'],
            'bundle_items.2.item_product_id' => ['The bundle_items.2.item_product_id field is required when bundle items is present.'],
        ]);
    });

    test('create bundle product validates bundle item quantities', function () {
        Sanctum::actingAs($this->adminUser);

        $item1 = Product::factory()->create();

        $data = [
            'name' => 'Bundle with Invalid Quantities',
            'status' => 'publish',
            'is_bundle' => true,
            'bundle_items' => [
                ['item_product_id' => (string)$item1->id, 'quantity' => 'invalid'], // Non-numeric quantity
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'bundle_items.0.quantity' => ['The bundle_items.0.quantity must be an integer.'],
        ]);
    });

    test('with warehouses', function () {
        Sanctum::actingAs($this->adminUser);

        // Create existing warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $data = [
            'name' => 'Test Product with Product Warehouses',
            'status' => 'publish',
            'warehouses' => [
                [
                    'id' => $warehouse1->id,
                    'label' => 'Test Product Warehouse label 1',
                ],
                [
                    'id' => $warehouse2->id,
                    'label' => 'Test Product Warehouse label 2',
                ],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and(array_column($response['data']['warehouses'], 'id'))
            ->toEqualCanonicalizing([$warehouse1->id, $warehouse2->id]);
    });

    test('with invalid warehouses', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Product with Product Warehouses',
            'status' => 'publish',
            'warehouses' => [
                [
                    'id' => 999999,
                    'label' => 'Test Product Warehouse label 1',
                ],
            ],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'warehouses.0.id' => ['The selected warehouses.0.id is invalid.'],
        ]);
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
        ]);
    });

    test('validates name length', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => str_repeat('a', 256), // Exceeds max length of 255
            'status' => 'publish',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name may not be greater than 255 characters.'],
        ]);
    });

    test('validates status values', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Product',
            'status' => 'invalid-status',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'status' => ['The selected status is invalid.'],
        ]);
    });

    test('validates price values', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Product',
            'status' => 'publish',
            'price' => -10, // Negative price
            'sale_price' => 150, // Sale price higher than price
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'price' => ['The price must be at least 0.'],
            'sale_price' => ['The sale price must be less than -10.'],
        ]);
    });

    test('validates unique SKU', function () {
        Sanctum::actingAs($this->adminUser);

        // Create a product with a specific SKU
        Product::factory()->create(['sku' => 'EXISTING-SKU']);

        $data = [
            'name' => 'Test Product',
            'status' => 'publish',
            'sku' => 'EXISTING-SKU', // Duplicate SKU
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'sku' => ['The sku has already been taken.'],
        ]);
    });

    test('validates description length', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Product',
            'status' => 'publish',
            'description' => str_repeat('a', 10001), // Exceeds max length of 10000
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'description' => ['The description may not be greater than 10000 characters.'],
        ]);
    });

    test('validates numeric fields', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Product',
            'status' => 'publish',
            'price' => 'not-a-number',
            'height' => -5,
            'width' => 'invalid',
            'weight' => -1,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'price' => ['The price must be a number.'],
            'height' => ['The height must be at least 0.'],
            'width' => ['The width must be a number.'],
            'weight' => ['The weight must be at least 0.'],
        ]);
    });
});

describe('update', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create([
            'name' => 'Original Product',
            'description' => 'Original description',
            'price' => 50.00,
            'status' => 'draft',
        ]);

        $data = [
            'name' => 'Updated Product',
            'description' => 'Updated description',
            'price' => 75.00,
            'sale_price' => 60.00,
            'status' => 'publish',
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'description' => $data['description'],
                'price' => $data['price'],
                'sale_price' => $data['sale_price'],
                'status' => $data['status'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'id' => $product->id,
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => $data['price'],
            'sale_price' => $data['sale_price'],
            'status' => $data['status'],
        ]);
    });

    test('with variants', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create();

        // Create existing variant
        $existingVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Variant',
            'price' => '30.00',
        ]);

        $data = [
            'name' => 'Updated Product with Variants',
            'status' => 'publish',
            'variants' => [
                [
                    'id' => $existingVariant->id,
                    'title' => 'Updated Variant',
                    'price' => '35.00',
                    'is_active' => true,
                ],
                [
                    'title' => 'New Variant',
                    'price' => '45.00',
                    'is_active' => true,
                ],
            ],
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        foreach ($data['variants'] as $variant) {
            $this->assertDatabaseHas($this->productVariantTable, [
                'product_id' => $product->id,
                'title' => $variant['title'],
                'price' => $variant['price'],
                'is_active' => $variant['is_active'],
            ]);
        }
    });

    test('update bundle product with bundle items', function () {
        Sanctum::actingAs($this->adminUser);

        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseCount($this->bundleItemTable, 2);

        $data = [
            'name' => 'Updated Bundle Product',
            'status' => 'publish',
            'is_bundle' => true,
            'bundle_items' => [
                ['item_product_id' => (string)$item1->id, 'quantity' => 5], // update
                ['item_product_id' => (string)$item3->id, 'quantity' => 3], // create new
                // item2 is omitted, so it should be deleted
            ],
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $bundle_product->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['is_bundle'])->toBeTrue();

        $this->assertDatabaseCount($this->bundleItemTable, 2);

        // Verify item1 was updated
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 5,
        ]);

        // Verify item3 was created
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        // Verify item2 was deleted
        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);
    });

    test('with warehouses', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create();

        // Create existing warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $product->warehouses()->sync($warehouse1->id);

        $data = [
            'name' => 'Updated Product with Product Warehouses',
            'status' => 'publish',
            'warehouses' => [
                [
                    'id' => $warehouse2->id,
                    'label' => 'Test Product Warehouse label 2',
                ],
            ],
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and(array_column($response['data']['warehouses'], 'id'))
            ->toEqualCanonicalizing([$warehouse2->id]);
    });

    test('with invalid warehouses', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create();

        $data = [
            'name' => 'Updated Product with Product Warehouses',
            'status' => 'publish',
            'warehouses' => [
                [
                    'id' => 999999,
                    'label' => 'Test Product Warehouse label 2',
                ],
            ],
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'warehouses.0.id' => ['The selected warehouses.0.id is invalid.'],
        ]);
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create();

        $data = [
            'name' => '', // Empty name
            'status' => '', // Empty status
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
            'status' => ['The status must be a string.', 'The selected status is invalid.'],
        ]);
    });

    test('validates unique SKU excluding current product', function () {
        Sanctum::actingAs($this->adminUser);

        // Create two products with different SKUs
        $product1 = Product::factory()->create(['sku' => 'SKU-001']);
        $product2 = Product::factory()->create(['sku' => 'SKU-002']);

        // Try to update product2 with product1's SKU
        $data = [
            'name' => 'Updated Product',
            'status' => 'publish',
            'sku' => 'SKU-001', // Duplicate SKU
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product2->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'sku' => ['The sku has already been taken.'],
        ]);
    });

    test('allows same SKU for same product', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create(['sku' => 'SAME-SKU']);

        $data = [
            'name' => 'Updated Product',
            'status' => 'publish',
            'sku' => 'SAME-SKU', // Same SKU as current product
        ];

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['product' => $product->id]), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();
    });
});

describe('delete', function () {
    test('success', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create([
            'name' => 'Product to Delete',
            'status' => 'publish',
        ]);

        $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['product' => $product->id]))->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Verify product is soft deleted
        $this->assertSoftDeleted($this->table, [
            'id' => $product->id,
        ]);
    });

    test('with nonexistent product', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['product' => 9999]))->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});

describe('media', function () {
    test('can create product with media', function () {
        Sanctum::actingAs($this->adminUser);

        $imageFile = \Illuminate\Http\UploadedFile::fake()->image('test-product.png');
        $galleryFile = \Illuminate\Http\UploadedFile::fake()->image('test-gallery.png');

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'name' => 'Test Product',
            'description' => 'Test product description',
            'price' => 99.99,
            'status' => 'publish',
            'unit' => 'piece',
            'is_taxable' => true,
            'media' => [
                'image' => [
                    [
                        'file' => $imageFile,
                        'order_column' => 1,
                    ],
                ],
                'gallery' => [
                    [
                        'file' => $galleryFile,
                        'order_column' => 1,
                    ],
                ],
            ],
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $product = Product::latest()->first();
        expect($product->getMedia('image'))->toHaveCount(1);
        expect($product->getMedia('gallery'))->toHaveCount(1);
        expect($product->getFirstMediaUrl('image'))->not()->toBeEmpty();
        expect($product->getFirstMediaUrl('gallery'))->not()->toBeEmpty();
    });

    test('can update product with media', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create();
        $imageFile = \Illuminate\Http\UploadedFile::fake()->image('updated-product.png');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $product->id), [
            'name' => 'Updated Product',
            'description' => $product->description,
            'price' => $product->price,
            'status' => $product->status,
            'unit' => $product->unit,
            'is_taxable' => $product->is_taxable,
            'media' => [
                'image' => [
                    [
                        'file' => $imageFile,
                        'order_column' => 1,
                    ],
                ],
            ],
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $product->refresh();
        expect($product->getMedia('image'))->toHaveCount(1);
        expect($product->getFirstMediaUrl('image'))->not()->toBeEmpty();
    });

    test('can delete media when updating product', function () {
        Sanctum::actingAs($this->adminUser);

        $product = Product::factory()->create();

        // First add media using fake file
        $file = \Illuminate\Http\UploadedFile::fake()->image('test-product.png');
        $product->addMedia($file->getPathname())
            ->usingFileName('test-product.png')
            ->toMediaCollection('image');

        $media = $product->getFirstMedia('image');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $product->id), [
            'name' => $product->name,
            'description' => $product->description,
            'price' => $product->price,
            'status' => $product->status,
            'unit' => $product->unit,
            'is_taxable' => $product->is_taxable,
            'media' => [
                'delete' => [$media->id],
            ],
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $product->refresh();
        expect($product->getMedia('image'))->toHaveCount(0);
    });
});
