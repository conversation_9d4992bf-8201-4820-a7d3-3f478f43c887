<?php

use App\Enums\Permission;
use App\Models\Designation;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_DESIGNATION,
        Permission::EDIT_DESIGNATION,
        Permission::DELETE_DESIGNATION,
        Permission::VIEW_DESIGNATION,
    ], [Permission::VIEW_DESIGNATION]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // User-specific setup
    $this->routeNamePrefix = 'designations';
    $this->table = Designation::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access designation endpoints', function () {
    $designation = Designation::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['designation' => $designation->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['designation' => $designation->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create designation', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'Director',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
    ]);
});

test('create designation validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
    ]);
});

test('update designation', function () {
    Sanctum::actingAs($this->adminUser);

    $designation = Designation::factory()->create([
        'name' => 'Director',
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'name' => 'Stylist',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['designation' => $designation->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
        ]);

    $this->assertDatabaseCount($this->table, 1);
});

test('update designation validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $designation = Designation::factory()->create([
        'name' => 'Director',
    ]);

    $data = [];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['designation' => $designation->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
    ]);
});

test('delete designation', function () {
    Sanctum::actingAs($this->adminUser);

    $designation = Designation::factory()->create([
        'name' => 'Director',
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['designation' => $designation->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 0);

    $this->assertDatabaseMissing($this->table, [
        'id' => $designation->id,
    ]);
});

test('delete nonexistent designation returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['designation' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});
