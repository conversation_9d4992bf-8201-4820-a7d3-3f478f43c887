<?php

use App\Enums\NotificationChannel;
use App\Enums\Permission;
use App\Models\NotificationTemplate;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_NOTIFICATION_TEMPLATE,
        Permission::EDIT_NOTIFICATION_TEMPLATE,
        Permission::DELETE_NOTIFICATION_TEMPLATE,
    ]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->staffUser = $users->get('staff_user');
    $this->customerUser = $users->get('customer_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // Controller-specific setup
    $this->routeNamePrefix = 'notification-templates';
    $this->table = NotificationTemplate::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access notification template endpoints', function () {
    $notification_template = NotificationTemplate::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['notification_template' => $notification_template->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['notification_template' => $notification_template->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create notification template', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'name' => 'test_notification',
        'description' => 'Test notification template',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Test Subject',
        'content' => 'Test content with {{variable}}',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'description' => $data['description'],
            'channel' => $data['channel'],
            'locale' => $data['locale'],
            'subject' => $data['subject'],
            'content' => $data['content'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'description' => $data['description'],
        'channel' => $data['channel'],
        'locale' => $data['locale'],
        'subject' => $data['subject'],
        'content' => $data['content'],
        'is_active' => true,
    ]);
});

test('create notification template validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
        'channel' => ['The channel field is required.'],
        'locale' => ['The locale field is required.'],
        'content' => ['The content field is required.'],
    ]);
});

test('create notification template validates channel values', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'test_notification',
        'channel' => 'invalid_channel',
        'locale' => 'en',
        'content' => 'Test content',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'channel' => ['The selected channel is invalid.'],
    ]);
});

test('create notification template validates locale format', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'test_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'invalid_locale',
        'content' => 'Test content',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'locale' => ['The locale must be 2 characters.'],
    ]);
});

test('create notification template validates field lengths', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => str_repeat('a', 256), // Too long
        'description' => str_repeat('a', 501), // Too long
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => str_repeat('a', 256), // Too long
        'content' => 'Test content',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name may not be greater than 255 characters.'],
        'description' => ['The description may not be greater than 500 characters.'],
        'subject' => ['The subject may not be greater than 255 characters.'],
    ]);
});

test('create notification template with SMS channel without subject', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'sms_notification',
        'description' => 'SMS notification template',
        'channel' => NotificationChannel::SMS,
        'locale' => 'en',
        'content' => 'SMS content without subject',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'description' => $data['description'],
        'channel' => $data['channel'],
        'locale' => $data['locale'],
        'subject' => null,
        'content' => $data['content'],
        'is_active' => true,
    ]);
});

test('create notification template with WhatsApp channel without subject', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'whatsapp_notification',
        'description' => 'WhatsApp notification template',
        'channel' => NotificationChannel::WHATSAPP,
        'locale' => 'en',
        'content' => 'WhatsApp content without subject',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->table, [
        'name' => $data['name'],
        'description' => $data['description'],
        'channel' => $data['channel'],
        'locale' => $data['locale'],
        'subject' => null,
        'content' => $data['content'],
        'is_active' => true,
    ]);
});

test('update notification template', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->create([
        'name' => 'original_name',
        'description' => 'Original description',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Original Subject',
        'content' => 'Original content',
        'is_active' => true,
    ]);

    $data = [
        'name' => 'updated_name',
        'description' => 'Updated description',
        'channel' => NotificationChannel::SMS,
        'locale' => 'es',
        'subject' => 'Updated Subject',
        'content' => 'Updated content',
        'is_active' => false,
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => $template->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $data['name'],
            'description' => $data['description'],
            'channel' => $data['channel'],
            'locale' => $data['locale'],
            'subject' => $data['subject'],
            'content' => $data['content'],
            'is_active' => $data['is_active'],
        ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $template->id,
        'name' => $data['name'],
        'description' => $data['description'],
        'channel' => $data['channel'],
        'locale' => $data['locale'],
        'subject' => $data['subject'],
        'content' => $data['content'],
        'is_active' => $data['is_active'],
    ]);
});

test('update notification template validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->create();

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => $template->id]), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name field is required.'],
        'channel' => ['The channel field is required.'],
        'locale' => ['The locale field is required.'],
        'content' => ['The content field is required.'],
    ]);
});

test('update notification template validates channel values', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->create();

    $data = [
        'name' => 'updated_name',
        'channel' => 'invalid_channel',
        'locale' => 'en',
        'content' => 'Updated content',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => $template->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'channel' => ['The selected channel is invalid.'],
    ]);
});

test('update notification template validates locale format', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->create();

    $data = [
        'name' => 'updated_name',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'invalid_locale',
        'content' => 'Updated content',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => $template->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'locale' => ['The locale must be 2 characters.'],
    ]);
});

test('update notification template validates field lengths', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->create();

    $data = [
        'name' => str_repeat('a', 256), // Too long
        'description' => str_repeat('a', 501), // Too long
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => str_repeat('a', 256), // Too long
        'content' => 'Updated content',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => $template->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'name' => ['The name may not be greater than 255 characters.'],
        'description' => ['The description may not be greater than 500 characters.'],
        'subject' => ['The subject may not be greater than 255 characters.'],
    ]);
});

test('update notification template with boolean is_active field', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->active()->create();

    $data = [
        'name' => 'updated_name',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'content' => 'Updated content',
        'is_active' => false,
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => $template->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['is_active'])->toBe(false);

    $this->assertDatabaseHas($this->table, [
        'id' => $template->id,
        'is_active' => false,
    ]);
});

test('update nonexistent notification template returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'updated_name',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'content' => 'Updated content',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['notification_template' => 9999]), $data)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('delete notification template', function () {
    Sanctum::actingAs($this->adminUser);

    $template = NotificationTemplate::factory()->create([
        'name' => 'template_to_delete',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'content' => 'Content to delete',
    ]);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['notification_template' => $template->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseMissing($this->table, [
        'id' => $template->id,
    ]);
});

test('delete nonexistent notification template returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['notification_template' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('create notification template with all valid channels', function () {
    Sanctum::actingAs($this->adminUser);

    $channels = [
        NotificationChannel::MAIL,
        NotificationChannel::SMS,
        NotificationChannel::WHATSAPP,
    ];

    foreach ($channels as $channel) {
        $data = [
            'name' => "test_notification_{$channel}",
            'description' => "Test notification for {$channel}",
            'channel' => $channel,
            'locale' => 'en',
            'subject' => $channel === NotificationChannel::MAIL ? 'Test Subject' : null,
            'content' => "Test content for {$channel}",
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['channel'])->toBe($channel);
    }

    $this->assertDatabaseCount($this->table, 3);
});

test('create notification template with different locales', function () {
    Sanctum::actingAs($this->adminUser);

    $locales = ['en', 'es', 'fr', 'de'];

    foreach ($locales as $locale) {
        $data = [
            'name' => 'multilingual_notification',
            'description' => "Notification in {$locale}",
            'channel' => NotificationChannel::MAIL,
            'locale' => $locale,
            'subject' => "Subject in {$locale}",
            'content' => "Content in {$locale}",
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['locale'])->toBe($locale);
    }

    $this->assertDatabaseCount($this->table, 4);
});

test('create notification template with template variables', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'name' => 'variable_notification',
        'description' => 'Notification with template variables',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Welcome {{user_name}}!',
        'content' => 'Hello {{user_name}}, your order {{order_id}} is ready. Total: {{total_amount}}.',
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'subject' => $data['subject'],
            'content' => $data['content'],
        ]);

    $this->assertDatabaseHas($this->table, [
        'subject' => $data['subject'],
        'content' => $data['content'],
    ]);
});
