<?php

use App\Enums\Permission;
use App\Enums\Role as RoleEnum;
use App\Models\Branch;
use App\Models\User;
use App\Models\UserProfile;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::CREATE_USER,
        Permission::EDIT_USER,
        Permission::DELETE_USER,
        Permission::VIEW_USER,
    ], [Permission::VIEW_USER]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->staffUser = $users->get('staff_user');
    $this->customerUser = $users->get('customer_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');

    // User-specific setup
    $this->routeNamePrefix = 'users';
    $this->table = User::class;
    $this->userProfileTable = UserProfile::class;

    // Create test branches for branch-related tests
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access user endpoints', function () {
    $user = User::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['user' => $user->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['user' => $user->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create user', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 4);

    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '+12125551234', // Valid US phone number format
        'roles' => [RoleEnum::CUSTOMER],
        'branches' => [$this->branch1->id, $this->branch2->id],
        'is_active' => true,
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => $data['email'],
            'phone' => $data['phone'],
        ])
        ->and($response['data']['branches'])->toHaveCount(2);

    $this->assertDatabaseCount($this->table, 5);

    $this->assertDatabaseHas($this->table, [
        'firstname' => $data['firstname'],
        'lastname' => $data['lastname'],
        'email' => $data['email'],
        'phone' => $data['phone'],
    ]);

    // Verify branch associations
    $user = User::where('email', $data['email'])->first();

    expect($user->branches()->count())->toBe(2)
        ->and($user->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);
});

test('create user validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'firstname' => ['The firstname field is required.'],
        'lastname' => ['The lastname field is required.'],
        'password' => ['The password field is required.'],
        'email' => ['The email field is required.'],
    ]);
});

test('create user validates email', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => 'invalid-email',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email must be a valid email address.'],
    ]);

    //create user with duplicate email fails
    $data = [
        'firstname' => 'Duplicate',
        'lastname' => 'Email',
        'email' => $this->customerUser->email, // Use existing email
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email has already been taken.'],
    ]);
});

test('create user validate gmail address', function () {
    Sanctum::actingAs($this->adminUser);

    // First create a user with a Gmail address
    User::factory()->create([
        'email' => '<EMAIL>',
        'firstname' => 'Existing',
        'lastname' => 'User',
    ]);

    // Try to create another user with the same Gmail but with dots
    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $email_list = [
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
    ];

    foreach ($email_list as $email) {
        $data['email'] = $email;

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'email' => ['The email has already been taken (Gmail addresses with dots/plus signs are considered identical).'],
        ]);
    }

    //create user with unique Gmail address succeeds
    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('create user validate phone number', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'Invalid',
        'lastname' => 'Phone',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $phone_list = [
        '************', // Invalid format without country code
        'phone' => '+1234', // Too short
    ];

    foreach ($phone_list as $phone) {
        $data['phone'] = $phone;

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'phone' => ['The phone is not a valid phone number.'],
        ]);
    }

    //create user with duplicate phone fails
    $data = [
        'firstname' => 'Duplicate',
        'lastname' => 'Phone',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => $this->customerUser->phone, // Use existing phone
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'phone' => ['The phone has already been taken.'],
    ]);
});

test('create user with non-existent role fails', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'roles' => ['non-existent-role'], // Non-existent role name
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'roles.0' => ['The selected roles.0 is invalid.'],
    ]);
});

test('create user with non-existent branch fails', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
        'branches' => [999], // Non-existent branch ID
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'branches.0' => ['The selected branches.0 is invalid.'],
    ]);
});

test('create user with mix of valid and invalid branches fails', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
        'branches' => [$this->branch1->id, 999], // One valid, one invalid
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'branches.1' => ['The selected branches.1 is invalid.'],
    ]);
});

test('update user', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 4);

    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'phone' => '+12125559876',
        'password' => 'newpassword123',
        'roles' => [RoleEnum::STAFF],
        'branches' => [$this->branch1->id],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => $data['email'],
            'phone' => $data['phone'],
        ])
        ->and($response['data']['branches'])->toHaveCount(1);

    $this->assertDatabaseCount($this->table, 4);

    $this->assertDatabaseHas($this->table, [
        'firstname' => $data['firstname'],
        'lastname' => $data['lastname'],
        'email' => $data['email'],
        'phone' => $data['phone'],
    ]);

    // Verify branch associations
    $this->customerUser->refresh();
    expect($this->customerUser->branches()->count())->toBe(1)
        ->and($this->customerUser->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

test('update user validates email', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => 'invalid-email',
        'password' => 'newpassword123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email must be a valid email address.'],
    ]);

    //update user with duplicate email fails
    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => $this->adminUser->email, // Use existing email from another user
        'password' => 'newpassword123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'email' => ['The email has already been taken.'],
    ]);
});

test('update user validate phone number', function () {
    Sanctum::actingAs($this->adminUser);

    //Success
    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'password' => 'newpassword123',
        'phone' => '+447911123456', // Valid UK mobile number
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'phone' => $data['phone'],
        ]);

    //Invalid format
    $data = [
        'firstname' => 'Invalid',
        'lastname' => 'Phone',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $phone_list = [
        '************', // Invalid format without country code
        'phone' => '+1234', // Too short
    ];

    foreach ($phone_list as $phone) {
        $data['phone'] = $phone;

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'phone' => ['The phone is not a valid phone number.'],
        ]);
    }

    //duplicate phone fails
    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'password' => 'newpassword123',
        'phone' => $this->adminUser->phone ?? '+12125551999', // Use existing phone from another user
        'roles' => [RoleEnum::CUSTOMER],
    ];

    // Set phone for admin user if not set
    if (!$this->adminUser->phone) {
        $this->adminUser->update(['phone' => '+12125551999']);
    }

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'phone' => ['The phone has already been taken.'],
    ]);
});

test('update user validate gmail address', function () {
    Sanctum::actingAs($this->adminUser);

    // First create a user with a Gmail address
    User::factory()->create([
        'email' => '<EMAIL>',
        'firstname' => 'Existing',
        'lastname' => 'User',
    ]);

    // Try to create another user with the same Gmail but with dots
    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'password' => 'password123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $email_list = [
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
        '<EMAIL>', // <NAME_EMAIL> when normalized
    ];

    foreach ($email_list as $email) {
        $data['email'] = $email;

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'email' => ['The email has already been taken (Gmail addresses with dots/plus signs are considered identical).'],
        ]);
    }

    //Success
    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'password' => 'newpassword123',
        'roles' => [RoleEnum::CUSTOMER],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('update user with non-existent role fails', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'password' => 'newpassword123',
        'roles' => ['non-existent-role'], // Non-existent role name
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'roles.0' => ['The selected roles.0 is invalid.'],
    ]);
});

test('update user with non-existent branch fails', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'password' => 'newpassword123',
        'roles' => [RoleEnum::CUSTOMER],
        'branches' => [999], // Non-existent branch ID
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'branches.0' => ['The selected branches.0 is invalid.'],
    ]);
});

test('delete user', function () {
    Sanctum::actingAs($this->adminUser);

    $user_delete = User::factory()->create([
        'firstname' => 'Delete',
        'lastname' => 'Me',
        'email' => '<EMAIL>',
    ]);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['user' => $user_delete->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->table, [
        'id' => $user_delete->id,
        'deleted_at' => now(),
    ]);
});

test('delete nonexistent user returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['user' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('create user with profile', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 4);

    $data = [
        'firstname' => 'New',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '+12125551234',
        'roles' => [RoleEnum::CUSTOMER],
        'is_active' => true,
        'profile' => [
            'bio' => 'This is a test bio for the user',
            'contact' => '<EMAIL>',
            'emergency_contact' => 'Emergency Contact Name',
            'emergency_phone' => '+12125555555',
            'street_address' => '123 Test Street',
            'city' => 'Test City',
            'zip' => '12345',
            'race' => 'Test Race',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => $data['email'],
            'phone' => $data['phone'],
        ])
        ->and($response['data']['profile'])->toMatchArray([
            'bio' => $data['profile']['bio'],
            'contact' => $data['profile']['contact'],
            'emergency_contact' => $data['profile']['emergency_contact'],
            'emergency_phone' => $data['profile']['emergency_phone'],
            'street_address' => $data['profile']['street_address'],
            'city' => $data['profile']['city'],
            'zip' => $data['profile']['zip'],
            'race' => $data['profile']['race'],
        ]);

    $this->assertDatabaseCount($this->table, 5);
    $this->assertDatabaseCount($this->userProfileTable, 1);

    $this->assertDatabaseHas($this->table, [
        'firstname' => $data['firstname'],
        'lastname' => $data['lastname'],
        'email' => $data['email'],
        'phone' => $data['phone'],
    ]);

    $this->assertDatabaseHas($this->userProfileTable, [
        'bio' => $data['profile']['bio'],
        'contact' => $data['profile']['contact'],
        'emergency_contact' => $data['profile']['emergency_contact'],
        'emergency_phone' => $data['profile']['emergency_phone'],
        'street_address' => $data['profile']['street_address'],
        'city' => $data['profile']['city'],
        'zip' => $data['profile']['zip'],
        'race' => $data['profile']['race'],
    ]);
});




test('update user with profile', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 4);

    // First create a user profile using factory
    $existingProfile = UserProfile::factory()->create([
        'customer_id' => $this->customerUser->id,
        'bio' => 'Original bio',
        'contact' => '<EMAIL>',
        'emergency_contact' => 'Original Emergency Contact',
        'emergency_phone' => '+12125551111',
        'street_address' => '123 Original Street',
        'city' => 'Original City',
        'zip' => '12345',
        'race' => 'Original Race',
    ]);

    // Verify profile was created
    $this->assertDatabaseCount($this->userProfileTable, 1);

    // Now update with new data
    $data = [
        'firstname' => 'Updated',
        'lastname' => 'Name',
        'email' => '<EMAIL>',
        'phone' => '+12125559876',
        'password' => 'newpassword123',
        'roles' => [RoleEnum::STAFF],
        'profile' => [
            'bio' => 'Updated bio for the user',
            'contact' => '<EMAIL>',
            'emergency_contact' => 'Updated Emergency Contact',
            'emergency_phone' => '+12125556666',
            'street_address' => '456 Updated Street',
            'city' => 'Updated City',
            'zip' => '67890',
            'race' => 'Updated Race',
        ],
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['user' => $this->customerUser->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'firstname' => $data['firstname'],
            'lastname' => $data['lastname'],
            'email' => $data['email'],
            'phone' => $data['phone'],
        ])
        ->and($response['data']['profile'])->toMatchArray([
            'bio' => $data['profile']['bio'],
            'contact' => $data['profile']['contact'],
            'emergency_contact' => $data['profile']['emergency_contact'],
            'emergency_phone' => $data['profile']['emergency_phone'],
            'street_address' => $data['profile']['street_address'],
            'city' => $data['profile']['city'],
            'zip' => $data['profile']['zip'],
            'race' => $data['profile']['race'],
        ]);

    // Verify user count remains the same and profile count is still 1 (updated, not created new)
    $this->assertDatabaseCount($this->table, 4);
    $this->assertDatabaseCount($this->userProfileTable, 1);

    $this->assertDatabaseHas($this->table, [
        'firstname' => $data['firstname'],
        'lastname' => $data['lastname'],
        'email' => $data['email'],
        'phone' => $data['phone'],
    ]);

    $this->assertDatabaseHas($this->userProfileTable, [
        'bio' => $data['profile']['bio'],
        'contact' => $data['profile']['contact'],
        'emergency_contact' => $data['profile']['emergency_contact'],
        'emergency_phone' => $data['profile']['emergency_phone'],
        'street_address' => $data['profile']['street_address'],
        'city' => $data['profile']['city'],
        'zip' => $data['profile']['zip'],
        'race' => $data['profile']['race'],
        'customer_id' => $this->customerUser->id,
    ]);
});

describe('media', function () {
    test('can create user with media', function () {
        Sanctum::actingAs($this->adminUser);

        $file = \Illuminate\Http\UploadedFile::fake()->image('test-avatar.png');

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [
            'firstname' => 'John',
            'lastname' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+60123456789',
            'password' => 'password123',
            'roles' => [RoleEnum::CUSTOMER],
            'is_active' => true,
            'profile' => [
                'bio' => 'This is a test bio for the user',
                'contact' => '<EMAIL>',
                'emergency_contact' => 'Emergency Contact Name',
                'emergency_phone' => '+12125555555',
                'street_address' => '123 Test Street',
                'city' => 'Test City',
                'zip' => '12345',
                'race' => 'Test Race',
            ],
            'media' => [
                'avatar' => [
                    [
                        'file' => $file,
                        'order_column' => 1
                    ]
                ]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $user = User::find($response['data']['id']);
        expect($user->profile)->not()->toBeNull();
        expect($user->profile->getMedia('avatar'))->toHaveCount(1);
        expect($user->profile->getFirstMediaUrl('avatar'))->not()->toBeEmpty();
    });

    test('can update user with media', function () {
        Sanctum::actingAs($this->adminUser);

        $user = User::factory()->withProfile()->create();

        $file = \Illuminate\Http\UploadedFile::fake()->image('updated-avatar.png');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $user->id), [
            'firstname' => 'Updated John',
            'lastname' => $user->lastname,
            'email' => $user->email,
            'phone' =>  $user->phone,
            'is_active' => $user->is_active,
            'roles' => [RoleEnum::CUSTOMER],
            'media' => [
                'avatar' => [
                    [
                        'file' => $file,
                        'order_column' => 1
                    ]
                ]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $user->refresh();
        expect($user->profile->getMedia('avatar'))->toHaveCount(1);
        expect($user->profile->getFirstMediaUrl('avatar'))->not()->toBeEmpty();
    });

    test('can delete media when updating user', function () {
        Sanctum::actingAs($this->adminUser);

        $user = User::factory()->create();
        $user->profile()->create();
        // First add media to user profile using fake file
        $file = \Illuminate\Http\UploadedFile::fake()->image('test-avatar.png');
        $user->profile->addMedia($file->getPathname())
            ->usingFileName('test-avatar.png')
            ->toMediaCollection('avatar');

        $media = $user->profile->getFirstMedia('avatar');

        $response = $this->putJson(route("{$this->routeNamePrefix}.update", $user->id), [
            'firstname' => $user->firstname,
            'lastname' => $user->lastname,
            'email' => $user->email,
            'phone' => $user->phone,
            'is_active' => $user->is_active,
            'roles' => [RoleEnum::CUSTOMER],
            'media' => [
                'delete' => [$media->id]
            ]
        ])->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $user->refresh();
        expect($user->profile->getMedia('avatar'))->toHaveCount(0);
    });
});
