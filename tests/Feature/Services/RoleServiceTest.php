<?php

use App\Models\Role;
use App\Models\User;
use App\Repositories\RoleRepository;
use App\Services\RoleService;
use Spatie\Permission\Models\Permission;

beforeEach(function () {
    $this->roleService = app(RoleService::class);
});

test('check repository', function () {
    expect($this->roleService->getRepository())->toBeInstanceOf(RoleRepository::class);
});

test('check relationship', function () {
    $role = Role::factory()->create(['guard_name' => 'api']);
    $permission = Permission::create(['name' => 'test-permission', 'guard_name' => 'api']);
    $role->givePermissionTo($permission);

    $role_model = $this->roleService->setModel($role)->getModel();

    expect($role_model->toArray())->toHaveKey('permissions');
});

describe('sync permissions', function () {
    test('success', function () {
        $role = Role::factory()->create(['guard_name' => 'api']);
        $permission1 = Permission::create(['name' => 'permission-1', 'guard_name' => 'api']);
        $permission2 = Permission::create(['name' => 'permission-2', 'guard_name' => 'api']);
        $permission3 = Permission::create(['name' => 'permission-3', 'guard_name' => 'api']);

        // Initially give role permission1
        $role->givePermissionTo($permission1);
        expect($role->hasPermissionTo($permission1))->toBeTrue();

        // Sync with permission2 and permission3 (should remove permission1)
        $result = $this->roleService
            ->setModel($role)
            ->syncPermissions([$permission2->name, $permission3->name]);

        expect($result)->toBeInstanceOf(RoleService::class)
            ->and($result->getModel()->id)->toBe($role->id);

        $role->refresh();

        expect($role->hasPermissionTo($permission1))->toBeFalse()
            ->and($role->hasPermissionTo($permission2))->toBeTrue()
            ->and($role->hasPermissionTo($permission3))->toBeTrue();
    });

    test('with empty array', function () {
        $role = Role::factory()->create(['guard_name' => 'api']);
        $permission = Permission::create(['name' => 'remove-test-permission', 'guard_name' => 'api']);

        // Give role a permission
        $role->givePermissionTo($permission);
        expect($role->hasPermissionTo($permission))->toBeTrue();

        // Sync with empty array
        $this->roleService
            ->setModel($role)
            ->syncPermissions([]);

        $role->refresh();
        expect($role->hasPermissionTo($permission))->toBeFalse()
            ->and($role->permissions)->toHaveCount(0);
    });
});

describe('sync users', function () {
    test('success', function () {
        $role = Role::factory()->create(['guard_name' => 'api']);
        $user1 = User::factory()->create(['email' => '<EMAIL>']);
        $user2 = User::factory()->create(['email' => '<EMAIL>']);

        $this->roleService
            ->setModel($role)
            ->syncUsers([$user1->id, $user2->id]);

        expect($role->users)->toHaveCount(2)
            ->and($role->users->pluck('id'))->toContain($user1->id, $user2->id);
    });

    test('with empty array', function () {
        $role = Role::factory()->create(['guard_name' => 'api']);
        $user1 = User::factory()->create(['email' => '<EMAIL>']);

        $user1->assignRole($role->name);

        expect($role->users)->toHaveCount(1)
            ->and($role->users->pluck('id'))->toContain($user1->id);

        $this->roleService
            ->setModel($role)
            ->syncUsers([]);

        $role->refresh();

        expect($role->users)->toHaveCount(0);
    });
});

