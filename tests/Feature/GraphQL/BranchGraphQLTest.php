<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\Branch;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_BRANCH, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_BRANCH, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_BRANCH, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_BRANCH, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::SUPER_ADMIN);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_BRANCH,
        PermissionEnum::EDIT_BRANCH,
        PermissionEnum::CREATE_BRANCH,
        PermissionEnum::DELETE_BRANCH,
    ]);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
    $this->staffUser->givePermissionTo([PermissionEnum::VIEW_BRANCH]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    // Create test branches
    $this->activeBranch = Branch::factory()->active()->withDetails()->create([
        'name' => 'Active Branch',
        'code' => 'ACT001',
        'slug' => 'active-branch',
        'geolocation' => ['lat' => 40.7128, 'lng' => -74.0060],
        'virtual' => false,
    ]);

    $this->inactiveBranch = Branch::factory()->inactive()->create([
        'name' => 'Inactive Branch',
        'code' => 'INA001',
        'slug' => 'inactive-branch',
        'virtual' => true,
    ]);
});

test('branch query returns all fields correctly for branch with details', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            branch(id: $id) {
                id
                name
                code
                slug
                image {
                    id
                    original_url
                }
                geolocation
                active
                virtual
                created_at
                updated_at
                details {
                    id
                    branch_id
                    company_name
                    company_registration_no
                    company_address
                    company_phone
                    company_email
                    created_at
                    updated_at
                }
            }
        }
    ', [
        'id' => $this->activeBranch->id,
    ]);

    $response->assertJson([
        'data' => [
            'branch' => [
                'id' => (string)$this->activeBranch->id,
                'name' => 'Active Branch',
                'code' => 'ACT001',
                'slug' => 'active-branch',
                'active' => true,
                'virtual' => false,
            ],
        ],
    ]);

    // Verify all fields are present and not null
    $branch = $response->json('data.branch');
    expect($branch['id'])->not()->toBeNull()
        ->and($branch['name'])->not()->toBeNull()
        ->and($branch['code'])->not()->toBeNull()
        ->and($branch['slug'])->not()->toBeNull()
        ->and($branch['geolocation'])->not()->toBeNull()
        ->and($branch['active'])->not()->toBeNull()
        ->and($branch['virtual'])->not()->toBeNull()
        ->and($branch['created_at'])->not()->toBeNull()
        ->and($branch['updated_at'])->not()->toBeNull()
        ->and($branch['details'])->not()->toBeNull()
        ->and($branch['details']['id'])->not()->toBeNull()
        ->and($branch['details']['branch_id'])->not()->toBeNull()
        ->and($branch['details']['company_name'])->not()->toBeNull()
        ->and($branch['details']['company_registration_no'])->not()->toBeNull()
        ->and($branch['details']['company_address'])->not()->toBeNull()
        ->and($branch['details']['company_phone'])->not()->toBeNull()
        ->and($branch['details']['company_email'])->not()->toBeNull()
        ->and($branch['details']['created_at'])->not()->toBeNull()
        ->and($branch['details']['updated_at'])->not()->toBeNull();

    // Verify details are present and all fields are not null
});

test('branch query returns all fields correctly for branch without details', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            branch(id: $id) {
                id
                name
                code
                slug
                image {
                    id
                    original_url
                }
                geolocation
                active
                virtual
                created_at
                updated_at
                details {
                    id
                    company_name
                }
            }
        }
    ', [
        'id' => $this->inactiveBranch->id,
    ]);

    $response->assertJson([
        'data' => [
            'branch' => [
                'id' => (string)$this->inactiveBranch->id,
                'name' => 'Inactive Branch',
                'code' => 'INA001',
                'slug' => 'inactive-branch',
                'active' => false,
                'virtual' => true,
                'details' => null,
            ],
        ],
    ]);

    // Verify all fields are present
    $branch = $response->json('data.branch');
    expect($branch['id'])->not()->toBeNull()
        ->and($branch['name'])->not()->toBeNull()
        ->and($branch['code'])->not()->toBeNull()
        ->and($branch['slug'])->not()->toBeNull()
        ->and($branch['active'])->not()->toBeNull()
        ->and($branch['virtual'])->not()->toBeNull()
        ->and($branch['created_at'])->not()->toBeNull()
        ->and($branch['updated_at'])->not()->toBeNull()
        ->and($branch['details'])->toBeNull();
});

test('branches query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            branches {
                data {
                    id
                    name
                    code
                    slug
                    image {
                        id
                        original_url
                    }
                    geolocation
                    active
                    virtual
                    created_at
                    updated_at
                    details {
                        id
                        branch_id
                        company_name
                        company_registration_no
                        company_address
                        company_phone
                        company_email
                        created_at
                        updated_at
                    }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    total
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'branches' => [
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'code',
                        'slug',
                        'image',
                        'geolocation',
                        'active',
                        'virtual',
                        'created_at',
                        'updated_at',
                        'details',
                    ],
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages',
                    'total',
                ],
            ],
        ],
    ]);

    $branches = $response->json('data.branches.data');
    expect($branches)->toBeArray()->and($branches)->toHaveCount(2);

    // Verify each branch has all required fields
    foreach ($branches as $branch) {
        expect($branch['id'])->not()->toBeNull()
            ->and($branch['name'])->not()->toBeNull()
            ->and($branch['code'])->not()->toBeNull()
            ->and($branch['slug'])->not()->toBeNull()
            ->and($branch['active'])->not()->toBeNull()
            ->and($branch['virtual'])->not()->toBeNull()
            ->and($branch['created_at'])->not()->toBeNull()
            ->and($branch['updated_at'])->not()->toBeNull();
    }
});

test('branch query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query($id: ID!) {
            branch(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => $this->activeBranch->id
    ]);

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('branches query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query {
            branches {
                data {
                    id
                    name
                }
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('staff user with view permission can access branches', function () {
    Sanctum::actingAs($this->staffUser);

    $response = $this->graphQL('
        query {
            branches {
                data {
                    id
                    name
                    code
                    slug
                    active
                    virtual
                }
            }
        }
    ');

    $branches = $response->json('data.branches.data');
    expect($branches)->toBeArray()->and($branches)->toHaveCount(2);

    // Verify staff can see all branch fields
    foreach ($branches as $branch) {
        expect($branch['id'])->not()->toBeNull()
            ->and($branch['name'])->not()->toBeNull()
            ->and($branch['code'])->not()->toBeNull()
            ->and($branch['slug'])->not()->toBeNull()
            ->and($branch['active'])->not()->toBeNull()
            ->and($branch['virtual'])->not()->toBeNull();
    }
});

test('unauthenticated user cannot access branches', function () {
    $response = $this->graphQL('
        query {
            branches {
                data {
                    id
                    name
                }
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('branch query with media returns image correctly', function () {
    Sanctum::actingAs($this->adminUser);

    // Create branch using factory
    $branch = Branch::factory()->create([
        'name' => 'Branch with Media',
        'code' => 'BRANCH-001',
    ]);

    // Add media directly to the factory branch
    $file = \Illuminate\Http\UploadedFile::fake()->image('test-branch.png');
    $branch->addMedia($file->getPathname())
        ->usingFileName('test-branch.png')
        ->toMediaCollection('image');

    $response = $this->graphQL('
        query($id: ID!) {
            branch(id: $id) {
                id
                name
                image {
                    id
                    original_url
                }
            }
        }
    ', [
        'id' => $branch->id
    ]);

    $response->assertJson([
        'data' => [
            'branch' => [
                'id' => (string)$branch->id,
                'name' => 'Branch with Media',
                'image' => [
                    'id' => (string)$branch->getMedia('image')->first()->id,
                    'original_url' => $branch->getFirstMediaUrl('image'),
                ]
            ]
        ]
    ]);
});
