<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_USER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_USER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_USER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_USER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::VIEW_CUSTOMER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_CUSTOMER, 'guard_name' => 'api']);

    // Create test users with profiles
    $this->adminUser = User::factory()->withProfile()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::STAFF);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_USER,
        PermissionEnum::EDIT_USER,
        PermissionEnum::CREATE_USER,
        PermissionEnum::DELETE_USER,
        PermissionEnum::VIEW_CUSTOMER,
        PermissionEnum::EDIT_CUSTOMER,
    ]);

    $this->customerUser = User::factory()->withProfile()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    $this->staffUser = User::factory()->withProfile()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
});

test('me query returns authenticated user', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query {
            me {
                id
                firstname
                lastname
                email
                is_active
                roles {
                    name
                }
            }
        }
    ');

    $response->assertJson([
        'data' => [
            'me' => [
                'id' => (string)$this->customerUser->id,
                'firstname' => 'Customer',
                'lastname' => 'User',
                'email' => '<EMAIL>',
                'is_active' => true,
                'roles' => [
                    ['name' => RoleEnum::CUSTOMER]
                ]
            ]
        ]
    ]);
});

test('me query requires authentication', function () {
    $response = $this->graphQL('
        query {
            me {
                id
                email
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthenticatedError();
});

test('user query returns specific user for authorized admin', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            user(id: $id) {
                id
                firstname
                lastname
                email
                is_active
                profile {
                    avatar {
                        id
                        original_url
                    }
                    bio
                    contact
                    city
                }
                roles {
                    name
                }
            }
        }
    ', [
        'id' => $this->customerUser->id
    ]);

    $response->assertJson([
        'data' => [
            'user' => [
                'id' => (string)$this->customerUser->id,
                'firstname' => 'Customer',
                'lastname' => 'User',
                'email' => '<EMAIL>',
                'is_active' => true,
                'roles' => [
                    ['name' => RoleEnum::CUSTOMER]
                ]
            ]
        ]
    ]);
});

test('user query denies access for customer', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query($id: ID!) {
            user(id: $id) {
                id
                email
            }
        }
    ', [
        'id' => $this->staffUser->id
    ]);

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('users query returns paginated users for admin', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            users(first: 10) {
                data {
                    id
                    firstname
                    lastname
                    email
                    is_active
                    profile {
                        avatar {
                            id
                            original_url
                        }
                        bio
                        contact
                        city
                    }
                    roles {
                        name
                    }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'users' => [
                'data' => [
                    '*' => [
                        'id',
                        'firstname',
                        'lastname',
                        'email',
                        'is_active',
                        'profile' => [
                            'avatar',
                            'bio',
                            'contact',
                            'city'
                        ],
                        'roles' => [
                            '*' => ['name']
                        ]
                    ]
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages'
                ]
            ]
        ]
    ]);

    $users = $response->json('data.users.data');
    expect($users)->toHaveCount(3);

    $emails = collect($users)->pluck('email')->toArray();
    expect($emails)->toContain('<EMAIL>', '<EMAIL>', '<EMAIL>');
});

test('users query denies access for customer', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query {
            users(first: 10) {
                data {
                    id
                    email
                }
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('me query returns user with profile', function () {
    // Create user with profile using factory
    $user = User::factory()->withProfile()->create([
        'firstname' => 'Profile',
        'lastname' => 'User',
        'email' => '<EMAIL>',
    ]);
    $user->assignRole(RoleEnum::CUSTOMER);

    Sanctum::actingAs($user);

    $response = $this->graphQL('
        query {
            me {
                id
                firstname
                lastname
                email
                profile {
                    bio
                    contact
                    city
                }
            }
        }
    ');

    $response->assertJson([
        'data' => [
            'me' => [
                'id' => (string) $user->id,
                'firstname' => 'Profile',
                'lastname' => 'User',
                'email' => '<EMAIL>',
            ]
        ]
    ]);

    // Check that profile exists and has expected structure
    expect($response->json('data.me.profile'))->not()->toBeNull()
        ->and($response->json('data.me.profile.bio'))->not()->toBeNull()
        ->and($response->json('data.me.profile.contact'))->not()->toBeNull()
        ->and($response->json('data.me.profile.city'))->not()->toBeNull();
});

test('user profile query with media returns avatar correctly', function () {
    Sanctum::actingAs($this->adminUser);

    // Create user with profile using factory
    $user = User::factory()->withProfile()->create([
        'firstname' => 'User',
        'lastname' => 'With Avatar',
        'email' => '<EMAIL>',
    ]);

    // Add media directly to the user profile
    $file = \Illuminate\Http\UploadedFile::fake()->image('test-avatar.png');
    $user->profile->addMedia($file->getPathname())
        ->usingFileName('test-avatar.png')
        ->toMediaCollection('avatar');

    $response = $this->graphQL('
        query($id: ID!) {
            user(id: $id) {
                id
                firstname
                lastname
                profile {
                    id
                    avatar {
                        id
                        original_url
                    }
                }
            }
        }
    ', [
        'id' => $user->id
    ]);

    $response->assertJson([
        'data' => [
            'user' => [
                'id' => (string)$user->id,
                'firstname' => 'User',
                'lastname' => 'With Avatar',
                'profile' => [
                    'id' => (string)$user->profile->id,
                    'avatar' => [
                        'id' => (string)$user->profile->getMedia('avatar')->first()->id,
                        'original_url' => $user->profile->getFirstMediaUrl('avatar'),
                    ]
                ]
            ]
        ]
    ]);
});
