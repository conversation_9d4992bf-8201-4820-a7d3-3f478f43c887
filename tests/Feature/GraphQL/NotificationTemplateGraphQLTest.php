<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Enums\NotificationChannel;
use App\Models\User;
use App\Models\NotificationTemplate;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::EDIT_NOTIFICATION_TEMPLATE, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::VIEW_USER, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::STAFF);
    $this->adminUser->givePermissionTo([
        PermissionEnum::EDIT_NOTIFICATION_TEMPLATE,
        PermissionEnum::VIEW_USER,
    ]);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
    $this->staffUser->givePermissionTo([PermissionEnum::EDIT_NOTIFICATION_TEMPLATE]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    $this->unauthorizedUser = User::factory()->create([
        'firstname' => 'Unauthorized',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->unauthorizedUser->assignRole(RoleEnum::STAFF);

    // Create test notification templates
    $this->emailTemplate = NotificationTemplate::factory()->create([
        'name' => 'welcome_notification',
        'description' => 'Welcome email template',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Welcome, {{user_name}}!',
        'content' => 'Hello {{user_name}}, welcome to our platform!',
        'is_active' => true,
    ]);

    $this->smsTemplate = NotificationTemplate::factory()->create([
        'name' => 'welcome_notification',
        'description' => 'Welcome SMS template',
        'channel' => NotificationChannel::SMS,
        'locale' => 'en',
        'subject' => null,
        'content' => 'Hi {{user_name}}, welcome!',
        'is_active' => true,
    ]);

    $this->inactiveTemplate = NotificationTemplate::factory()->create([
        'name' => 'inactive_notification',
        'description' => 'Inactive template',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
        'subject' => 'Inactive',
        'content' => 'This template is inactive',
        'is_active' => false,
    ]);
});

test('notificationTemplate query returns specific template for authorized user', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            notificationTemplate(id: $id) {
                id
                name
                description
                channel
                locale
                subject
                content
                is_active
                created_at
                updated_at
            }
        }
    ', [
        'id' => $this->emailTemplate->id
    ]);

    $response->assertJson([
        'data' => [
            'notificationTemplate' => [
                'id' => (string)$this->emailTemplate->id,
                'name' => 'welcome_notification',
                'description' => 'Welcome email template',
                'channel' => NotificationChannel::MAIL,
                'locale' => 'en',
                'subject' => 'Welcome, {{user_name}}!',
                'content' => 'Hello {{user_name}}, welcome to our platform!',
                'is_active' => true,
            ]
        ]
    ]);

    expect($response->json('data.notificationTemplate.created_at'))->not()->toBeNull()
        ->and($response->json('data.notificationTemplate.updated_at'))->not()->toBeNull();
});

test('notificationTemplate query denies access for customer', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query($id: ID!) {
            notificationTemplate(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => $this->emailTemplate->id
    ]);

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('notificationTemplate query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->unauthorizedUser);

    $response = $this->graphQL('
        query($id: ID!) {
            notificationTemplate(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => $this->emailTemplate->id
    ]);

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('notificationTemplate query denies access for unauthenticated user', function () {
    $response = $this->graphQL('
        query($id: ID!) {
            notificationTemplate(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => $this->emailTemplate->id
    ]);

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('notificationTemplates query returns paginated templates for authorized user', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(first: 10) {
                data {
                    id
                    name
                    description
                    channel
                    locale
                    subject
                    content
                    is_active
                    created_at
                    updated_at
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'notificationTemplates' => [
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'description',
                        'channel',
                        'locale',
                        'subject',
                        'content',
                        'is_active',
                        'created_at',
                        'updated_at'
                    ]
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages'
                ]
            ]
        ]
    ]);

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(3); // emailTemplate, smsTemplate, inactiveTemplate

    $templateNames = collect($templates)->pluck('name')->toArray();
    expect($templateNames)->toContain('welcome_notification', 'inactive_notification');
});

test('notificationTemplates query filters by channel', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                where: { 
                    column: CHANNEL, 
                    operator: EQ, 
                    value: "' . NotificationChannel::MAIL . '" 
                }
            ) {
                data {
                    id
                    name
                    channel
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(2); // emailTemplate and inactiveTemplate

    foreach ($templates as $template) {
        expect($template['channel'])->toBe(NotificationChannel::MAIL);
    }
});

test('notificationTemplates query filters by is_active status', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                where: { 
                    column: IS_ACTIVE, 
                    operator: EQ, 
                    value: true 
                }
            ) {
                data {
                    id
                    name
                    is_active
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(2); // emailTemplate and smsTemplate

    foreach ($templates as $template) {
        expect($template['is_active'])->toBe(true);
    }
});

test('notificationTemplates query filters by name', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                where: { 
                    column: NAME, 
                    operator: EQ, 
                    value: "welcome_notification" 
                }
            ) {
                data {
                    id
                    name
                    channel
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(2); // emailTemplate and smsTemplate

    foreach ($templates as $template) {
        expect($template['name'])->toBe('welcome_notification');
    }
});

test('notificationTemplates query filters by locale', function () {
    // Create Spanish template
    $spanishTemplate = NotificationTemplate::factory()->create([
        'name' => 'welcome_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'es',
        'subject' => '¡Bienvenido, {{user_name}}!',
        'content' => 'Hola {{user_name}}, ¡bienvenido a nuestra plataforma!',
    ]);

    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                where: { 
                    column: LOCALE, 
                    operator: EQ, 
                    value: "es" 
                }
            ) {
                data {
                    id
                    name
                    locale
                    subject
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(1);
    expect($templates[0]['locale'])->toBe('es');
    expect($templates[0]['subject'])->toBe('¡Bienvenido, {{user_name}}!');
});

test('notificationTemplates query supports ordering by name', function () {
    // Create additional template with different name for ordering test
    NotificationTemplate::factory()->create([
        'name' => 'account_notification',
        'channel' => NotificationChannel::MAIL,
        'locale' => 'en',
    ]);

    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                orderBy: { column: NAME, order: ASC }
            ) {
                data {
                    id
                    name
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    $names = collect($templates)->pluck('name')->toArray();

    // Should be ordered alphabetically
    expect($names[0])->toBe('account_notification');
    expect($names)->toContain('inactive_notification', 'welcome_notification');
});

test('notificationTemplates query supports ordering by created_at', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                orderBy: { column: CREATED_AT, order: DESC }
            ) {
                data {
                    id
                    name
                    created_at
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(3);

    // Verify descending order by checking timestamps
    $timestamps = collect($templates)->pluck('created_at')->toArray();
    $sortedTimestamps = collect($timestamps)->sort()->reverse()->values()->toArray();
    expect($timestamps)->toBe($sortedTimestamps);
});

test('notificationTemplates query denies access for customer', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(first: 10) {
                data {
                    id
                    name
                }
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('notificationTemplates query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->unauthorizedUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(first: 10) {
                data {
                    id
                    name
                }
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('notificationTemplates query denies access for unauthenticated user', function () {
    $response = $this->graphQL('
        query {
            notificationTemplates(first: 10) {
                data {
                    id
                    name
                }
            }
        }
    ');

    expect($response->json())->toHaveGraphQLUnauthorizedError();
});

test('notificationTemplate query returns null for non-existent template', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            notificationTemplate(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => 99999
    ]);

    expect($response->json('data.notificationTemplate'))->toBeNull();
});

test('notificationTemplates query respects pagination limits', function () {
    // Create additional templates to test pagination
    NotificationTemplate::factory()->count(10)->create();

    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(first: 5) {
                data {
                    id
                    name
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    lastPage
                }
            }
        }
    ');

    $paginatorInfo = $response->json('data.notificationTemplates.paginatorInfo');
    expect($paginatorInfo['count'])->toBe(5);
    expect($paginatorInfo['hasMorePages'])->toBe(true);
    expect($response->json('data.notificationTemplates.data'))->toHaveCount(5);
});

test('staff user with permission can access notification templates', function () {
    Sanctum::actingAs($this->staffUser);

    $response = $this->graphQL('
        query($id: ID!) {
            notificationTemplate(id: $id) {
                id
                name
                channel
            }
        }
    ', [
        'id' => $this->emailTemplate->id
    ]);

    $response->assertJson([
        'data' => [
            'notificationTemplate' => [
                'id' => (string)$this->emailTemplate->id,
                'name' => 'welcome_notification',
                'channel' => NotificationChannel::MAIL,
            ]
        ]
    ]);
});

test('notificationTemplates query handles complex filtering', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            notificationTemplates(
                first: 10,
                where: {
                    AND: [
                        { column: NAME, operator: EQ, value: "welcome_notification" },
                        { column: IS_ACTIVE, operator: EQ, value: true }
                    ]
                }
            ) {
                data {
                    id
                    name
                    is_active
                }
            }
        }
    ');

    $templates = $response->json('data.notificationTemplates.data');
    expect($templates)->toHaveCount(2); // emailTemplate and smsTemplate

    foreach ($templates as $template) {
        expect($template['name'])->toBe('welcome_notification');
        expect($template['is_active'])->toBe(true);
    }
});
