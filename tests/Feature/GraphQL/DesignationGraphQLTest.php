<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\Designation;
use App\Models\Instructor;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_DESIGNATION, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_DESIGNATION, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_DESIGNATION, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_DESIGNATION, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::SUPER_ADMIN);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_DESIGNATION,
        PermissionEnum::EDIT_DESIGNATION,
        PermissionEnum::CREATE_DESIGNATION,
        PermissionEnum::DELETE_DESIGNATION,
    ]);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
    $this->staffUser->givePermissionTo([PermissionEnum::VIEW_DESIGNATION]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    $this->designation = Designation::factory()->create([
        'name' => 'Designation 1',
    ]);

    $this->instructor = Instructor::factory()->create([
        'designation_id' => $this->designation->id,
    ]);
});

test('designation query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            designation(id: $id) {
                id
                name
                created_at
                updated_at
                instructors {
                    id
                }
            }
        }
    ', [
        'id' => $this->designation->id,
    ]);

    $response->assertJson([
        'data' => [
            'designation' => [
                'id' => (string)$this->designation->id,
                'name' => $this->designation->name,
                'instructors' => [
                    [
                        'id' => (string)$this->instructor->id,
                    ],
                ],
            ],
        ],
    ]);
});

test('designations query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            designations {
                data {
                    id
                    name
                    created_at
                    updated_at
                    instructors {
                        id
                    }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    total
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'designations' => [
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'instructors',
                    ],
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages',
                    'total',
                ],
            ],
        ],
    ]);

    $designations = $response->json('data.designations.data');
    expect($designations)->toBeArray()->and($designations)->toHaveCount(1)
        ->and($designations[0])->toMatchArray([
            'id' => (string)$this->designation->id,
            'name' => $this->designation->name,
            'instructors' => [
                [
                    'id' => (string)$this->instructor->id,
                ],
            ],
        ]);
});

test('designation query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query($id: ID!) {
            designation(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => $this->designation->id,
    ])->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('designations query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $this->graphQL('
        query {
            designations {
                data {
                    id
                    name
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('staff user with view permission can access designations', function () {
    Sanctum::actingAs($this->staffUser);

    $response = $this->graphQL('
        query {
            designations {
                data {
                    id
                    name
                }
            }
        }
    ');

    $designations = $response->json('data.designations.data');
    expect($designations)->toBeArray()->and($designations)->toHaveCount(1);
});

test('unauthenticated user cannot access designations', function () {
    $this->graphQL('
        query {
            designations {
                data {
                    id
                    name
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});
