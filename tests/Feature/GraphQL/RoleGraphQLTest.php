<?php

use <PERSON><PERSON>\Sanctum\Sanctum;
use <PERSON><PERSON>\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

beforeEach(function () {
    // Set up roles and permissions
    setupRoles();
    setupPermissions(['view-role-permission']);

    // Set up test users
    $users = setupTestUsers(['view-role-permission']);
    $this->adminUser = $users['admin_user'];

    // Create test role with permission
    $this->testRole = Role::create([
        'name' => 'test-role',
        'guard_name' => 'api',
    ]);

    $this->testPermission = Permission::create([
        'name' => 'test-permission',
        'guard_name' => 'api',
    ]);

    $this->testRole->givePermissionTo($this->testPermission);
});

test('role query returns all fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            role(id: $id) {
                id
                name
                guard_name
                created_at
                updated_at
                permissions {
                    id
                    name
                    guard_name
                    created_at
                    updated_at
                }
            }
        }
    ', [
        'id' => $this->testRole->id
    ]);

    $response->assertJsonStructure([
        'data' => [
            'role' => [
                'id',
                'name',
                'guard_name',
                'created_at',
                'updated_at',
                'permissions' => [
                    '*' => [
                        'id',
                        'name',
                        'guard_name',
                        'created_at',
                        'updated_at',
                    ]
                ]
            ]
        ]
    ]);
});

test('roles query returns all fields with pagination', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            roles {
                data {
                    id
                    name
                    guard_name
                    created_at
                    updated_at
                    permissions {
                        id
                        name
                        guard_name
                        created_at
                        updated_at
                    }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    total
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'roles' => [
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'guard_name',
                        'created_at',
                        'updated_at',
                        'permissions' => [
                            '*' => [
                                'id',
                                'name',
                                'guard_name',
                                'created_at',
                                'updated_at',
                            ]
                        ]
                    ]
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages',
                    'total',
                ]
            ]
        ]
    ]);
});
