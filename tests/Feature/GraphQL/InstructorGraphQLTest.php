<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\Designation;
use App\Models\Instructor;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_INSTRUCTOR, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_INSTRUCTOR, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_INSTRUCTOR, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_INSTRUCTOR, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::SUPER_ADMIN);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_INSTRUCTOR,
        PermissionEnum::EDIT_INSTRUCTOR,
        PermissionEnum::CREATE_INSTRUCTOR,
        PermissionEnum::DELETE_INSTRUCTOR,
    ]);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
    $this->staffUser->givePermissionTo([PermissionEnum::VIEW_INSTRUCTOR]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    $this->designation = Designation::factory()->create([
        'name' => 'Designation 1',
    ]);

    $this->user = User::factory()->create();

    $this->instructor = Instructor::factory()->create([
        'designation_id' => $this->designation->id,
        'user_id' => $this->user->id,
    ]);
});

test('instructor query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            instructor(id: $id) {
                  id
                  avatar {
                      id
                      original_url
                  }
                  name
                  bio
                  priority
                  active
                  order_column
                  created_at
                  updated_at

                  user {
                    id
                  }
                  designation {
                    id
                  }
            }
        }
    ', [
        'id' => $this->instructor->id,
    ]);

    $response->assertJson([
        'data' => [
            'instructor' => [
                'id' => (string)$this->instructor->id,
                'avatar' => null,
                'name' => $this->instructor->name,
                'bio' => $this->instructor->bio,
                'priority' => $this->instructor->priority,
                'active' => $this->instructor->active,
                'order_column' => $this->instructor->order_column,
                'created_at' => $this->instructor->created_at,
                'updated_at' => $this->instructor->updated_at,
                'user' => [
                    'id' => (string)$this->user->id,
                ],
                'designation' => [
                    'id' => (string)$this->designation->id,
                ],
            ],
        ],
    ]);
});

test('designations query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            instructors {
                data {
                      id
                      avatar {
                          id
                          original_url
                      }
                      name
                      bio
                      priority
                      active
                      order_column
                      created_at
                      updated_at
                      user {
                        id
                      }
                      designation {
                        id
                      }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    total
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'instructors' => [
                'data' => [
                    '*' => [
                        'id',
                        'avatar',
                        'name',
                        'bio',
                        'priority',
                        'active',
                        'order_column',
                        'created_at',
                        'updated_at',
                        'user',
                        'designation',
                    ],
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages',
                    'total',
                ],
            ],
        ],
    ]);

    $instructors = $response->json('data.instructors.data');
    expect($instructors)->toBeArray()->and($instructors)->toHaveCount(1)
        ->and($instructors[0])->toMatchArray([
            'id' => (string)$this->instructor->id,
            'avatar' => null,
            'name' => $this->instructor->name,
            'bio' => $this->instructor->bio,
            'priority' => $this->instructor->priority,
            'active' => $this->instructor->active,
            'order_column' => $this->instructor->order_column,
            'created_at' => $this->instructor->created_at,
            'updated_at' => $this->instructor->updated_at,
            'user' => [
                'id' => (string)$this->user->id,
            ],
            'designation' => [
                'id' => (string)$this->designation->id,
            ],
        ]);
});

test('instructor query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query($id: ID!) {
            instructor(id: $id) {
                id
                name
            }
        }
    ', [
        'id' => $this->instructor->id,
    ])->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('instructors query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $this->graphQL('
        query {
            instructors {
                data {
                    id
                    name
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('staff user with view permission can access instructors', function () {
    Sanctum::actingAs($this->staffUser);

    $response = $this->graphQL('
        query {
            instructors {
                data {
                    id
                    name
                }
            }
        }
    ');

    $instructors = $response->json('data.instructors.data');
    expect($instructors)->toBeArray()->and($instructors)->toHaveCount(1);
});

test('unauthenticated user cannot access instructors', function () {
    $this->graphQL('
        query {
            instructors {
                data {
                    id
                    name
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('instructor query with media returns avatar correctly', function () {
    Sanctum::actingAs($this->adminUser);

    // Create instructor using factory
    $user = User::factory()->create();
    $designation = Designation::factory()->create();

    $instructor = Instructor::factory()->create([
        'user_id' => $user->id,
        'designation_id' => $designation->id,
        'name' => 'Instructor with Avatar',
    ]);

    // Add media directly to the factory instructor
    $file = \Illuminate\Http\UploadedFile::fake()->image('test-avatar.png');
    $instructor->addMedia($file->getPathname())
        ->usingFileName('test-avatar.png')
        ->toMediaCollection('avatar');

    $response = $this->graphQL('
        query($id: ID!) {
            instructor(id: $id) {
                id
                name
                avatar {
                    id
                    original_url
                }
            }
        }
    ', [
        'id' => $instructor->id
    ]);

    $response->assertJson([
        'data' => [
            'instructor' => [
                'id' => (string)$instructor->id,
                'name' => 'Instructor with Avatar',
                'avatar' => [
                    'id' => (string)$instructor->getMedia('avatar')->first()->id,
                    'original_url' => $instructor->getFirstMediaUrl('avatar'),
                ]
            ]
        ]
    ]);
});
