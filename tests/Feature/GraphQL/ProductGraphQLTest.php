<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use App\Models\Warehouse;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_PRODUCT, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_PRODUCT, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_PRODUCT, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_PRODUCT, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::STAFF);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_PRODUCT,
        PermissionEnum::CREATE_PRODUCT,
        PermissionEnum::EDIT_PRODUCT,
        PermissionEnum::DELETE_PRODUCT,
    ]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);
});

describe('Product GraphQL Queries', function () {

    test('products query returns paginated list with variants', function () {
        Sanctum::actingAs($this->adminUser);

        // Create products with variants
        $product1 = Product::factory()->create([
            'name' => 'Test Product 1',
            'price' => 100.00,
            'sale_price' => 80.00,
            'sku' => 'TEST-001',
            'description' => 'Test product description'
        ]);

        Product::factory()->create([
            'name' => 'Test Product 2',
            'price' => 200.00,
            'sku' => 'TEST-002'
        ]);

        // Create variants for product1
        ProductVariant::factory()->create([
            'product_id' => $product1->id,
            'title' => 'Small',
            'price' => 90.00,
            'sku' => 'TEST-001-S'
        ]);

        ProductVariant::factory()->create([
            'product_id' => $product1->id,
            'title' => 'Large',
            'price' => 110.00,
            'sku' => 'TEST-001-L'
        ]);

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                        slug
                        description
                        price
                        sale_price
                        sku
                        barcode
                        is_taxable
                        is_bundle
                        is_require_double_scanning
                        status
                        created_at
                        updated_at
                        variants {
                            id
                            title
                            price
                            sale_price
                            sku
                            is_active
                        }
                        bundle_items {
                            id
                            bundle_product_id
                            item_product_id
                            quantity
                            item {
                                id
                                name
                                sku
                            }
                        }
                    }
                    paginatorInfo {
                        count
                        currentPage
                        total
                    }
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'products' => [
                    'paginatorInfo' => [
                        'count' => 2,
                        'currentPage' => 1,
                        'total' => 2,
                    ]
                ]
            ]
        ]);

        $products_data = $response->json('data.products.data');
        expect($products_data)->toHaveCount(2);

        // Find product1 in response
        $product1_data = collect($products_data)->firstWhere('id', (string)$product1->id);
        expect($product1_data)->not()->toBeNull()
            ->and($product1_data['name'])->toBe('Test Product 1')
            ->and($product1_data['price'])->toBe(100)
            ->and($product1_data['sale_price'])->toBe(80)
            ->and($product1_data['sku'])->toBe('TEST-001')
            ->and($product1_data['variants'])->toHaveCount(2);

        // Verify variants
        $variant_titles = collect($product1_data['variants'])->pluck('title')->toArray();
        expect($variant_titles)->toContain('Small', 'Large');
    });

    test('product query returns specific product with relationships', function () {
        Sanctum::actingAs($this->adminUser);

        // Create product
        $product = Product::factory()->create([
            'name' => 'Specific Test Product',
            'price' => 150.00,
            'sku' => 'SPECIFIC-001',
            'description' => 'Specific product for testing'
        ]);

        // Create variant
        ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Medium',
            'price' => 140.00,
            'sku' => 'SPECIFIC-001-M'
        ]);

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                    slug
                    description
                    price
                    sale_price
                    sku
                    is_bundle
                    is_require_double_scanning
                    status
                    variants {
                        id
                        product_id
                        title
                        price
                        sku
                        is_active
                    }
                    bundle_items {
                        id
                        bundle_product_id
                        item_product_id
                        quantity
                        item {
                            id
                            name
                            sku
                        }
                    }
                }
            }
        ', [
            'id' => $product->id
        ]);

        $response->assertJson([
            'data' => [
                'product' => [
                    'id' => (string)$product->id,
                    'name' => 'Specific Test Product',
                    'price' => 150,
                    'sku' => 'SPECIFIC-001',
                ]
            ]
        ]);

        $product_data = $response->json('data.product');
        expect($product_data['variants'])->toHaveCount(1)
            ->and($product_data['variants'][0]['title'])->toBe('Medium')
            ->and($product_data['variants'][0]['product_id'])->toBe((string)$product->id);
    });





    test('products query with name filter', function () {
        Sanctum::actingAs($this->adminUser);

        Product::factory()->create(['name' => 'Apple iPhone']);
        Product::factory()->create(['name' => 'Samsung Galaxy']);
        Product::factory()->create(['name' => 'Apple iPad']);

        $response = $this->graphQL('
            query {
                products(where: { column: NAME, operator: LIKE, value: "%Apple%" }) {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        $products_data = $response->json('data.products.data');
        expect($products_data)->toHaveCount(2);

        $product_names = collect($products_data)->pluck('name')->toArray();
        expect($product_names)->toContain('Apple iPhone', 'Apple iPad')
            ->and($product_names)->not()->toContain('Samsung Galaxy');
    });

    test('products query with price range filter', function () {
        Sanctum::actingAs($this->adminUser);

        Product::factory()->create(['name' => 'Cheap Product', 'price' => 50.00]);
        Product::factory()->create(['name' => 'Mid Product', 'price' => 150.00]);
        Product::factory()->create(['name' => 'Expensive Product', 'price' => 500.00]);

        $response = $this->graphQL('
            query {
                products(where: { AND: [
                    { column: PRICE, operator: GTE, value: 100 }
                    { column: PRICE, operator: LTE, value: 200 }
                ]}) {
                    data {
                        id
                        name
                        price
                    }
                }
            }
        ');

        $products_data = $response->json('data.products.data');
        expect($products_data)->toHaveCount(1)
            ->and($products_data[0]['name'])->toBe('Mid Product')
            ->and($products_data[0]['price'])->toBe(150);
    });

    test('products query requires view-product permission', function () {
        Sanctum::actingAs($this->customerUser); // Customer without view-product permission

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        expect($response->json())->toHaveGraphQLUnauthorizedError();
    });

    test('product query requires view-product permission', function () {
        Sanctum::actingAs($this->customerUser); // Customer without view-product permission

        $product = Product::factory()->create();

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                }
            }
        ', [
            'id' => $product->id
        ]);

        expect($response->json())->toHaveGraphQLUnauthorizedError();
    });

    test('products query without authentication returns error', function () {
        Product::factory()->create();

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        expect($response->json())->toHaveGraphQLUnauthorizedError();
    });

    test('products query with ordering by created_at', function () {
        Sanctum::actingAs($this->adminUser);

        $product1 = Product::factory()->create(['name' => 'First Product']);
        sleep(1);
        $product2 = Product::factory()->create(['name' => 'Second Product']);

        $response = $this->graphQL('
            query {
                products(orderBy: { column: CREATED_AT, order: DESC }) {
                    data {
                        id
                        name
                        created_at
                    }
                }
            }
        ');

        $products_data = $response->json('data.products.data');
        expect($products_data)->toHaveCount(2)
            ->and((int)$products_data[0]['id'])->toBe($product2->id)
            ->and((int)$products_data[1]['id'])->toBe($product1->id);
    });

    test('product query returns bundle fields and bundle items', function () {
        Sanctum::actingAs($this->adminUser);

        // Create bundle product
        $bundle_product = Product::factory()->create([
            'name' => 'Test Bundle Product',
            'price' => 300.00,
            'sku' => 'BUNDLE-001',
            'is_bundle' => true,
            'is_require_double_scanning' => true,
        ]);

        // Create item products
        $item1 = Product::factory()->create([
            'name' => 'Bundle Item 1',
            'price' => 100.00,
            'sku' => 'ITEM-001',
        ]);

        $item2 = Product::factory()->create([
            'name' => 'Bundle Item 2',
            'price' => 150.00,
            'sku' => 'ITEM-002',
        ]);

        // Create bundle items
        $bundle_product->bundleItems()->create([
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $bundle_product->bundleItems()->create([
            'item_product_id' => $item2->id,
            'quantity' => 1,
        ]);

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                    is_bundle
                    is_require_double_scanning
                    bundle_items {
                        id
                        bundle_product_id
                        item_product_id
                        quantity
                        item {
                            id
                            name
                            sku
                            price
                        }
                    }
                }
            }
        ', [
            'id' => $bundle_product->id
        ]);

        $response->assertJson([
            'data' => [
                'product' => [
                    'id' => (string)$bundle_product->id,
                    'name' => 'Test Bundle Product',
                    'is_bundle' => true,
                    'is_require_double_scanning' => true,
                ]
            ]
        ]);

        $product_data = $response->json('data.product');
        expect($product_data['bundle_items'])->toHaveCount(2);

        // Verify bundle items
        $bundle_items = collect($product_data['bundle_items']);

        $item1_bundle = $bundle_items->firstWhere('item_product_id', (string)$item1->id);
        expect($item1_bundle)->not()->toBeNull()
            ->and($item1_bundle['quantity'])->toBe(2)
            ->and($item1_bundle['item']['name'])->toBe('Bundle Item 1')
            ->and($item1_bundle['item']['sku'])->toBe('ITEM-001');

        $item2_bundle = $bundle_items->firstWhere('item_product_id', (string)$item2->id);
        expect($item2_bundle)->not()->toBeNull()
            ->and($item2_bundle['quantity'])->toBe(1)
            ->and($item2_bundle['item']['name'])->toBe('Bundle Item 2')
            ->and($item2_bundle['item']['sku'])->toBe('ITEM-002');
    });

    test('product query returns correct boolean values for bundle flags', function () {
        Sanctum::actingAs($this->adminUser);

        // Create regular product (not bundle, no double scanning)
        $regularProduct = Product::factory()->create([
            'name' => 'Regular Product',
            'is_bundle' => false,
            'is_require_double_scanning' => false,
        ]);

        // Create bundle product with double scanning
        $bundleProduct = Product::factory()->create([
            'name' => 'Bundle Product',
            'is_bundle' => true,
            'is_require_double_scanning' => true,
        ]);

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                        is_bundle
                        is_require_double_scanning
                    }
                }
            }
        ');

        $products_data = $response->json('data.products.data');
        expect($products_data)->toHaveCount(2);

        // Find products in response
        $regular_data = collect($products_data)->firstWhere('id', (string)$regularProduct->id);
        $bundle_data = collect($products_data)->firstWhere('id', (string)$bundleProduct->id);

        // Verify regular product flags
        expect($regular_data['is_bundle'])->toBeFalse()
            ->and($regular_data['is_require_double_scanning'])->toBeFalse();

        // Verify bundle product flags
        expect($bundle_data['is_bundle'])->toBeTrue()
            ->and($bundle_data['is_require_double_scanning'])->toBeTrue();
    });

    test('products query returns paginated list with warehouses', function () {
        Sanctum::actingAs($this->adminUser);

        // Create products
        $product1 = Product::factory()->create([
            'name' => 'Test Product 1',
            'price' => 100.00,
            'sale_price' => 80.00,
            'sku' => 'TEST-001',
            'description' => 'Test product description'
        ]);

        Product::factory()->create([
            'name' => 'Test Product 2',
            'price' => 200.00,
            'sku' => 'TEST-002'
        ]);

        // Create warehouses
        $warehouse1 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 1',
            'location' => 'Test Warehouse Location 1',
            'description' => 'Test Warehouse Description 1',
        ]);
        $warehouse2 = Warehouse::factory()->create([
            'name' => 'Test Warehouse Name 2',
            'location' => 'Test Warehouse Location 2',
            'description' => 'Test Warehouse Description 2',
        ]);

        $warehouses = [
            $warehouse1->id => ['label' => 'Test Product Warehouse label 1'],
            $warehouse2->id => ['label' => 'Test Product Warehouse label 2'],
        ];
        $product1->warehouses()->sync($warehouses);

        $response = $this->graphQL('
            query {
                products {
                    data {
                        id
                        name
                        slug
                        description
                        price
                        sale_price
                        sku
                        barcode
                        is_taxable
                        status
                        created_at
                        updated_at
                        warehouses {
                            id
                            name
                            location
                            description
                            pivot {
                                label
                            }
                        }
                    }
                    paginatorInfo {
                        count
                        currentPage
                        total
                    }
                }
            }
        ');

        $response->assertJson([
            'data' => [
                'products' => [
                    'paginatorInfo' => [
                        'count' => 2,
                        'currentPage' => 1,
                        'total' => 2,
                    ]
                ]
            ]
        ]);

        $products_data = $response->json('data.products.data');
        expect($products_data)->toHaveCount(2);

        // Find product1 in response
        $product1_data = collect($products_data)->firstWhere('id', (string)$product1->id);
        expect($product1_data)->not()->toBeNull()
            ->and($product1_data['name'])->toBe('Test Product 1')
            ->and($product1_data['price'])->toBe(100)
            ->and($product1_data['sale_price'])->toBe(80)
            ->and($product1_data['sku'])->toBe('TEST-001')
            ->and($product1_data['warehouses'])->toHaveCount(2);

        // Verify warehouse
        $warehouses_names = collect($product1_data['warehouses'])->pluck('name')->toArray();
        expect($warehouses_names)->toContain('Test Warehouse Name 1', 'Test Warehouse Name 2');
    });

    test('product query with media returns image correctly', function () {
        Sanctum::actingAs($this->adminUser);

        // Create product using factory
        $product = Product::factory()->create([
            'name' => 'Product with Media',
            'price' => 149.99,
            'sku' => 'PROD-MEDIA-001',
        ]);

        // Add media directly to the factory product
        $file = \Illuminate\Http\UploadedFile::fake()->image('test-product.png');
        $product->addMedia($file->getPathname())
            ->usingFileName('test-product.png')
            ->toMediaCollection('image');

        $response = $this->graphQL('
            query($id: ID!) {
                product(id: $id) {
                    id
                    name
                    image {
                        id
                        original_url
                    }
                }
            }
        ', [
            'id' => $product->id
        ]);

        $response->assertJson([
            'data' => [
                'product' => [
                    'id' => (string)$product->id,
                    'name' => 'Product with Media',
                    'image' => [
                        'id' => (string)$product->getMedia('image')->first()->id,
                        'original_url' => $product->getFirstMediaUrl('image'),
                    ]
                ]
            ]
        ]);
    });
});
