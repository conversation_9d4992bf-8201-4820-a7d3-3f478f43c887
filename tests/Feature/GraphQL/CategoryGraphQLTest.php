<?php

use App\Enums\ProductType;
use App\Models\Category;
use App\Models\User;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Create test users (no permissions needed for category viewing)
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);

    // Create test categories
    $this->rootCategory = Category::factory()->electronics()->create([
        'name' => 'Electronics',
        'slug' => 'electronics',
        'parent_id' => null,
        'type' => ProductType::DEFAULT,
        'is_active' => true,
        'order_column' => 1,
    ]);

    // add media
    $file = \Illuminate\Http\UploadedFile::fake()->image('test-category.png');
    $this->rootCategory->addMedia($file->getPathname())
        ->usingFileName('test-category.png')
        ->toMediaCollection('image');

    $this->childCategory = Category::factory()->create([
        'name' => 'Smartphones',
        'slug' => 'smartphones',
        'parent_id' => $this->rootCategory->id,
        'type' => ProductType::DEFAULT,
        'is_active' => true,
        'order_column' => 1,
    ]);

    $this->inactiveCategory = Category::factory()->inactive()->create([
        'name' => 'Discontinued',
        'slug' => 'discontinued',
        'parent_id' => null,
        'type' => ProductType::DEFAULT,
        'is_active' => false,
        'order_column' => 999,
    ]);
});

describe('Category GraphQL Queries', function () {

    test('category query returns specific category by id', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query($id: ID!) {
                category(id: $id) {
                    id
                    name
                    slug
                    parent_id
                    type
                    is_active
                    meta
                    order_column
                    created_at
                    updated_at
                    full_path
                    has_children
                    is_root
                }
            }
        ', [
            'id' => $this->rootCategory->id
        ]);

        $response->assertJson([
            'data' => [
                'category' => [
                    'id' => (string)$this->rootCategory->id,
                    'name' => 'Electronics',
                    'slug' => 'electronics',
                    'parent_id' => null,
                    'type' => ProductType::DEFAULT,
                    'is_active' => true,
                    'order_column' => 1,
                    'is_root' => true,
                    'has_children' => true,
                ]
            ]
        ]);
    });

    test('category query returns category with relationships', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query($id: ID!) {
                category(id: $id) {
                    id
                    name
                    slug
                    childCategories {
                        id
                        name
                        slug
                        parent_id
                    }
                }
            }
        ', [
            'id' => $this->rootCategory->id
        ]);

        $response->assertJson([
            'data' => [
                'category' => [
                    'id' => (string)$this->rootCategory->id,
                    'name' => 'Electronics',
                    'slug' => 'electronics',
                    'childCategories' => [
                        [
                            'id' => (string)$this->childCategory->id,
                            'name' => 'Smartphones',
                            'slug' => 'smartphones',
                            'parent_id' => (string)$this->rootCategory->id,
                        ]
                    ]
                ]
            ]
        ]);
    });

    test('category query with parent relationship', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query($id: ID!) {
                category(id: $id) {
                    id
                    name
                    slug
                    parent_id
                    parentCategory {
                        id
                        name
                        slug
                    }
                }
            }
        ', [
            'id' => $this->childCategory->id
        ]);

        $response->assertJson([
            'data' => [
                'category' => [
                    'id' => (string)$this->childCategory->id,
                    'name' => 'Smartphones',
                    'slug' => 'smartphones',
                    'parent_id' => (string)$this->rootCategory->id,
                    'parentCategory' => [
                        'id' => (string)$this->rootCategory->id,
                        'name' => 'Electronics',
                        'slug' => 'electronics',
                    ]
                ]
            ]
        ]);
    });

    test('categories query returns paginated categories', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(first: 10) {
                    data {
                        id
                        name
                        slug
                        type
                        is_active
                        order_column
                        parent_id
                        has_children
                        is_root
                    }
                    paginatorInfo {
                        count
                        currentPage
                        hasMorePages
                        total
                    }
                }
            }
        ');

        $response->assertJsonStructure([
            'data' => [
                'categories' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'slug',
                            'type',
                            'is_active',
                            'order_column',
                            'parent_id',
                            'has_children',
                            'is_root'
                        ]
                    ],
                    'paginatorInfo' => [
                        'count',
                        'currentPage',
                        'hasMorePages',
                        'total'
                    ]
                ]
            ]
        ]);

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray()->and($categories)->toHaveCount(3);
    });

    test('categories query with filtering by active status', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(where: {column: IS_ACTIVE, operator: EQ, value: true}) {
                    data {
                        id
                        name
                        is_active
                    }
                }
            }
        ');

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray()->and($categories)->toHaveCount(2);

        foreach ($categories as $category) {
            expect($category['is_active'])->toBeTrue();
        }
    });

    test('categories query with filtering by parent_id', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(where: {column: PARENT_ID, operator: IS_NULL}) {
                    data {
                        id
                        name
                        parent_id
                    }
                }
            }
        ');

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray()->and($categories)->toHaveCount(2);

        foreach ($categories as $category) {
            expect($category['parent_id'])->toBeNull();
        }
    });

    test('categories query with ordering', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(orderBy: [{column: ORDER_COLUMN, order: ASC}]) {
                    data {
                        id
                        name
                        order_column
                    }
                }
            }
        ');

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray();

        // Verify ordering by order_column
        $orderColumns = array_column($categories, 'order_column');
        $sortedColumns = $orderColumns;
        sort($sortedColumns);
        expect($orderColumns)->toBe($sortedColumns);
    });

    test('root category query with media', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query($id: ID!) {
                category(id: $id) {
                    id
                    name
                    image {
                        id
                        original_url
                    }
                }
            }
        ', [
            'id' => $this->rootCategory->id
        ]);

        $response->assertJson([
            'data' => [
                'category' => [
                    'id' => (string)$this->rootCategory->id,
                    'name' => 'Electronics',
                    'image' => [
                        'id' => (string)$this->rootCategory->getMedia('image')->first()->id,
                        'original_url' => $this->rootCategory->getFirstMediaUrl('image'),
                    ]
                ]
            ]
        ]);
    });
});

describe('Category GraphQL Computed Fields', function () {

    test('category query returns computed fields correctly', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query($id: ID!) {
                category(id: $id) {
                    id
                    name
                    full_path
                    has_children
                    is_root
                }
            }
        ', [
            'id' => $this->childCategory->id
        ]);

        $response->assertJson([
            'data' => [
                'category' => [
                    'id' => (string)$this->childCategory->id,
                    'name' => 'Smartphones',
                    'full_path' => 'Electronics > Smartphones',
                    'has_children' => false,
                    'is_root' => false,
                ]
            ]
        ]);
    });

    test('category query returns nested set relationships', function () {
        Sanctum::actingAs($this->adminUser);

        // Create a grandchild category for better testing
        Category::factory()->create([
            'name' => 'iPhones',
            'slug' => 'iphones',
            'parent_id' => $this->childCategory->id,
            'type' => ProductType::DEFAULT,
        ]);

        $response = $this->graphQL('
            query($id: ID!) {
                category(id: $id) {
                    id
                    name
                    ancestors {
                        id
                        name
                    }
                    descendants {
                        id
                        name
                    }
                    siblings {
                        id
                        name
                    }
                }
            }
        ', [
            'id' => $this->childCategory->id
        ]);

        $category = $response->json('data.category');

        expect($category['id'])->toBe((string)$this->childCategory->id)
            ->and($category['name'])->toBe('Smartphones');

        // Check ancestors (should include Electronics)
        $ancestors = $category['ancestors'];
        expect($ancestors)->toBeArray();

        // Check descendants (should include iPhones)
        $descendants = $category['descendants'];
        expect($descendants)->toBeArray();

        // Check siblings (categories with same parent)
        $siblings = $category['siblings'];
        expect($siblings)->toBeArray();
    });
});

describe('Category GraphQL Advanced Filtering', function () {

    test('categories query with whereHas filtering on parent', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(whereHas: {column: NAME, operator: EQ, value: "Electronics"}) {
                    data {
                        id
                        name
                        parentCategory {
                            name
                        }
                    }
                }
            }
        ');

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray();

        foreach ($categories as $category) {
            if ($category['parentCategory']) {
                expect($category['parentCategory']['name'])->toBe('Electronics');
            }
        }
    });

    test('categories query with complex filtering and ordering', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(
                    where: {
                        AND: [
                            {column: IS_ACTIVE, operator: EQ, value: true},
                            {column: TYPE, operator: EQ, value: "default"}
                        ]
                    },
                    orderBy: [
                        {column: ORDER_COLUMN, order: ASC},
                        {column: NAME, order: ASC}
                    ]
                ) {
                    data {
                        id
                        name
                        type
                        is_active
                        order_column
                    }
                }
            }
        ');

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray()->and($categories)->toHaveCount(2);

        foreach ($categories as $category) {
            expect($category['is_active'])->toBeTrue()
                ->and($category['type'])->toBe(ProductType::DEFAULT);
        }
    });

    test('categories query with name search', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->graphQL('
            query {
                categories(where: {column: NAME, operator: LIKE, value: "%Smart%"}) {
                    data {
                        id
                        name
                    }
                }
            }
        ');

        $categories = $response->json('data.categories.data');
        expect($categories)->toBeArray()->and($categories)->toHaveCount(1);
        expect($categories[0]['name'])->toBe('Smartphones');
    });
});
