<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Repositories\ProductRepository;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_WAREHOUSE, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_WAREHOUSE, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::CREATE_WAREHOUSE, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_WAREHOUSE, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::SUPER_ADMIN);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_WAREHOUSE,
        PermissionEnum::EDIT_WAREHOUSE,
        PermissionEnum::CREATE_WAREHOUSE,
        PermissionEnum::DELETE_WAREHOUSE,
    ]);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
    $this->staffUser->givePermissionTo([PermissionEnum::VIEW_WAREHOUSE]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    // Create test warehouses
    $this->warehouse = Warehouse::factory()->create([
        'name' => 'Test Warehouse Name 1',
        'location' => 'Test Warehouse Location 1',
        'description' => 'Test Warehouse Description 1',
    ]);

    // Create test products
    $this->product = Product::factory()->create([
        'name' => 'Test Product 1',
        'price' => 100.00,
        'sale_price' => 80.00,
        'sku' => 'TEST-001',
        'description' => 'Test product description'
    ]);

    // Create test product_warehouse
    $this->productRepository = app(ProductRepository::class);
    $warehouses = [
        $this->warehouse->id => ['label' => 'Test Product Warehouse label 1'],
    ];
    $this->productRepository->syncWarehouses($this->product, $warehouses);
});

test('warehouse query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            warehouse(id: $id) {
                id
                name
                location
                description
                created_at
                updated_at
                deleted_at
                products {
                    id
                    name
                    pivot {
                        label
                    }
                }
            }
        }
    ', [
        'id' => $this->warehouse->id,
    ]);

    $response->assertJson([
        'data' => [
            'warehouse' => [
                'id' => (string)$this->warehouse->id,
                'name' => $this->warehouse->name,
                'location' => $this->warehouse->location,
                'description' => $this->warehouse->description,
                'products' => [
                    [
                        'id' => (string)$this->product->id,
                        'name' => $this->product->name,
                        'pivot' => [
                            'label' => 'Test Product Warehouse label 1',
                        ]
                    ],
                ],
            ],
        ],
    ]);
});

test('warehouses query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            warehouses {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    total
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'warehouses' => [
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'location',
                        'description',
                        'products' => [
                            [
                                'id',
                                'name',
                                'pivot' => [
                                    'label',
                                ]
                            ],
                        ],
                    ],
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages',
                    'total',
                ],
            ],
        ],
    ]);

    $warehouses = $response->json('data.warehouses.data');
    expect($warehouses)->toBeArray()->and($warehouses)->toHaveCount(1)
        ->and($warehouses[0])->toMatchArray([
            'id' => (string)$this->warehouse->id,
            'name' => $this->warehouse->name,
            'location' => $this->warehouse->location,
            'description' => $this->warehouse->description,
            'products' => [
                [
                    'id' => (string)$this->product->id,
                    'name' => $this->product->name,
                    'pivot' => [
                        'label' => 'Test Product Warehouse label 1',
                    ]
                ],
            ],
        ]);
});

test('warehouse query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $response = $this->graphQL('
        query($id: ID!) {
            warehouse(id: $id) {
                id
                name
                location
                description
                created_at
                updated_at
                deleted_at
                products {
                    id
                    name
                    pivot {
                        label
                    }
                }
            }
        }
    ', [
        'id' => $this->warehouse->id,
    ])->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('warehouses query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $this->graphQL('
        query {
            warehouses {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('staff user with view permission can access warehouses', function () {
    Sanctum::actingAs($this->staffUser);

    $response = $this->graphQL('
        query {
            warehouses {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses = $response->json('data.warehouses.data');
    expect($warehouses)->toBeArray()->and($warehouses)->toHaveCount(1);
});

test('unauthenticated user cannot access warehouses', function () {
    $this->graphQL('
        query {
            warehouses {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('warehouses query with name filter', function () {
    Sanctum::actingAs($this->adminUser);

    Warehouse::factory()->create(['name' => 'Test Warehouse Name 2']);
    Warehouse::factory()->create(['name' => 'Test Warehouse Name 2A']);
    Warehouse::factory()->create(['name' => 'Test Warehouse Name 3']);

    $response = $this->graphQL('
        query {
            warehouses(where: { column: NAME, operator: LIKE, value: "%Name 2%" }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(2);

    $warehouse_names = collect($warehouses_data)->pluck('name')->toArray();
    expect($warehouse_names)->toContain('Test Warehouse Name 2', 'Test Warehouse Name 2A')
        ->and($warehouse_names)->not()->toContain('Test Warehouse Name 1', 'Test Warehouse Name 3');
});

test('warehouses query with location filter', function () {
    Sanctum::actingAs($this->adminUser);

    Warehouse::factory()->create(['name' => 'Test Warehouse Name 2', 'location' => 'Test Warehouse Location 2']);
    Warehouse::factory()->create(['name' => 'Test Warehouse Name 2A', 'location' => 'Test Warehouse Location 2A']);
    Warehouse::factory()->create(['name' => 'Test Warehouse Name 4', 'location' => 'Test Warehouse Location 3']);

    $response = $this->graphQL('
        query {
            warehouses(where: { column: LOCATION, operator: LIKE, value: "%Location 2%" }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(2);

    $warehouse_names = collect($warehouses_data)->pluck('location')->toArray();
    expect($warehouse_names)->toContain('Test Warehouse Location 2', 'Test Warehouse Location 2A')
        ->and($warehouse_names)->not()->toContain('Test Warehouse Location 1', 'Test Warehouse Location 3');
});

test('warehouses query with description filter', function () {
    Sanctum::actingAs($this->adminUser);

    Warehouse::factory()->create(['name' => 'Test Warehouse Name 2', 'description' => 'Test Warehouse Description 2']);
    Warehouse::factory()->create(['name' => 'Test Warehouse Name 2A', 'description' => 'Test Warehouse Description 2A']);
    Warehouse::factory()->create(['name' => 'Test Warehouse Name 4', 'description' => 'Test Warehouse Description 3']);

    $response = $this->graphQL('
        query {
            warehouses(where: { column: DESCRIPTION, operator: LIKE, value: "%Description 2%" }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(2);

    $warehouse_names = collect($warehouses_data)->pluck('description')->toArray();
    expect($warehouse_names)->toContain('Test Warehouse Description 2', 'Test Warehouse Description 2A')
        ->and($warehouse_names)->not()->toContain('Test Warehouse Description 1', 'Test Warehouse Description 3');
});

test('warehouses query with ordering by id', function () {
    Sanctum::actingAs($this->adminUser);

    sleep(1);
    $warehouse1 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 2']);
    sleep(1);
    $warehouse2 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 3']);

    $response = $this->graphQL('
        query {
            warehouses(orderBy: { column: ID, order: DESC }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(3)
        ->and((int)$warehouses_data[0]['id'])->toBe($warehouse2->id)
        ->and((int)$warehouses_data[1]['id'])->toBe($warehouse1->id)
        ->and((int)$warehouses_data[2]['id'])->toBe($this->warehouse->id);
});

test('warehouses query with ordering by name', function () {
    Sanctum::actingAs($this->adminUser);

    sleep(1);
    $warehouse1 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 2']);
    sleep(1);
    $warehouse2 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 3']);

    $response = $this->graphQL('
        query {
            warehouses(orderBy: { column: NAME, order: DESC }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(3)
        ->and((int)$warehouses_data[0]['id'])->toBe($warehouse2->id)
        ->and((int)$warehouses_data[1]['id'])->toBe($warehouse1->id)
        ->and((int)$warehouses_data[2]['id'])->toBe($this->warehouse->id);
});

test('warehouses query with ordering by created_at', function () {
    Sanctum::actingAs($this->adminUser);

    sleep(1);
    $warehouse1 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 2']);
    sleep(1);
    $warehouse2 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 3']);

    $response = $this->graphQL('
        query {
            warehouses(orderBy: { column: CREATED_AT, order: DESC }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(3)
        ->and((int)$warehouses_data[0]['id'])->toBe($warehouse2->id)
        ->and((int)$warehouses_data[1]['id'])->toBe($warehouse1->id)
        ->and((int)$warehouses_data[2]['id'])->toBe($this->warehouse->id);
});

test('warehouses query with ordering by updated_at', function () {
    Sanctum::actingAs($this->adminUser);

    sleep(1);
    $warehouse1 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 2']);
    sleep(1);
    $warehouse2 = Warehouse::factory()->create(['name' => 'Test Warehouse Name 3']);

    $response = $this->graphQL('
        query {
            warehouses(orderBy: { column: UPDATED_AT, order: DESC }) {
                data {
                    id
                    name
                    location
                    description
                    created_at
                    updated_at
                    deleted_at
                    products {
                        id
                        name
                        pivot {
                            label
                        }
                    }
                }
            }
        }
    ');

    $warehouses_data = $response->json('data.warehouses.data');
    expect($warehouses_data)->toHaveCount(3)
        ->and((int)$warehouses_data[0]['id'])->toBe($warehouse2->id)
        ->and((int)$warehouses_data[1]['id'])->toBe($warehouse1->id)
        ->and((int)$warehouses_data[2]['id'])->toBe($this->warehouse->id);
});
