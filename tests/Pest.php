<?php

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/


use App\Enums\Role as RoleEnum;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;

uses(
    Tests\TestCase::class,
    RefreshDatabase::class,
)->in('Feature', 'Unit');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

expect()->extend('toHaveSuccessGeneralResponse', function () {
    return $this->toMatchArray([
        'status' => 'OK',
        'code' => 200,
        'message' => 'Success.',
    ]);
});

expect()->extend('toHaveModelResourceNotFoundResponse', function () {
    return $this->toMatchArray([
        'status' => 'ERROR',
        'code' => 404,
        'error' => "Resource not found",
    ]);
});

expect()->extend('toHaveFailedGeneralResponse', function (int $code = null, string $error = null) {
    $data = [
        'status' => 'ERROR',
    ];

    if ($code) {
        $data['code'] = $code;
    }

    if ($error) {
        $data['error'] = $error;
    }

    return $this->toMatchArray($data);
});

expect()->extend('toHaveFailedValidationResponse', function ($error = []) {
    $data = [
        'status' => 'ERROR',
        'code' => 422,
    ];

    if ($error) {
        $data['error'] = $error;
    }

    return $this->toMatchArray($data);
});

expect()->extend('toHaveUnauthenticatedResponse', function () {
    return $this->toMatchArray([
        'status' => 'ERROR',
        'code' => 401,
        'error' => 'Unauthenticated.',
    ]);
});

expect()->extend('toHaveUnauthorizedPermissionResponse', function () {
    return $this->toMatchArray([
        'status' => 'ERROR',
        'code' => 401,
        'error' => 'Unauthorized. User does not have the right permissions.',
    ]);
});

expect()->extend('toHaveUnexpectedErrorResponse', function ($errorMessage = null) {
    $data = [
        'status' => 'ERROR',
        'code' => 500,
        'message' => 'An unexpected error occurred.',
    ];

    if ($errorMessage) {
        $data['error'] = $errorMessage;
    }

    return $this->toMatchArray($data);
});

expect()->extend('toHaveGraphQLError', function (string $expectedMessage) {
    $errors = $this->value['errors'] ?? [];

    $errorMessages = array_map(function ($error) {
        return $error['message'] ?? '';
    }, $errors);

    expect($errorMessages)->toContain($expectedMessage);

    return $this;
});

expect()->extend('toHaveGraphQLUnauthorizedError', function () {
    return $this->toHaveGraphQLError('This action is unauthorized.');
});

expect()->extend('toHaveGraphQLUnauthenticatedError', function () {
    return $this->toHaveGraphQLError('Unauthenticated.');
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function something()
{
    // ..
}

/**
 * Set up common roles for testing
 */
function setupRoles(): void
{
    Role::query()->insert([
        ['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api'],
        ['name' => RoleEnum::STAFF, 'guard_name' => 'api'],
        ['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api'],
        ['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']
    ]);
}

/**
 * Set up common permissions for testing
 *
 * @param array $permissions Array of permission names to create
 */
function setupPermissions(array $permissions): void
{
    $permission_data = [];

    foreach ($permissions as $permission) {
        $permission_data[] = ['name' => $permission, 'guard_name' => 'api'];
    }

    PermissionModel::query()->insert($permission_data);
}

/**
 * Set up common test users with roles and permissions
 *
 * @param array $permissions Array of permissions to assign to admin user
 * @param array $staff_permissions Array of permissions to assign to staff user (default: first permission for VIEW)
 * @return object Object containing all test users
 */
function setupTestUsers(array $permissions, array $staff_permissions = []): object
{
    // Default staff permissions to first permission if not specified
    if (empty($staff_permissions) && !empty($permissions)) {
        $staff_permissions = [reset($permissions)];
    }

    // Create admin user with all permissions
    $admin_user = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'phone' => '+12125551233',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $admin_user->assignRole(RoleEnum::SUPER_ADMIN);
    $admin_user->givePermissionTo($permissions);

    // Create staff user with limited permissions
    $staff_user = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $staff_user->assignRole(RoleEnum::STAFF);
    $staff_user->givePermissionTo($staff_permissions);

    // Create customer user
    $customer_user = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '+60124444444',
        'is_active' => true,
    ]);
    $customer_user->assignRole(RoleEnum::CUSTOMER);

    // Create unauthorized user (no permissions)
    $unauthorized_user = User::factory()->create([
        'firstname' => 'Unauthorized',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'is_active' => true,
    ]);
    $unauthorized_user->assignRole(RoleEnum::STAFF);

    return collect([
        'admin_user' => $admin_user,
        'staff_user' => $staff_user,
        'customer_user' => $customer_user,
        'unauthorized_user' => $unauthorized_user,
    ]);
}

/**
 * Complete setup for API controller tests
 *
 * @param array $permissions Array of permission names to create and assign
 * @param array $staff_permissions Array of permissions to assign to staff user (optional)
 * @return object Object containing all test users
 */
function setupApiControllerTest(array $permissions, array $staff_permissions = []): object
{
    setupRoles();
    setupPermissions($permissions);
    $users = setupTestUsers($permissions, $staff_permissions);

    Carbon::setTestNow('2025-01-01 00:00:00');

    return $users;
}

function getMediaFullUrl($media, $custom_file_name = null): string
{
    $prefix = config('media-library.prefix');

    if ($prefix) {
        $prefix .= '/';
    }

    return url('/storage/' . $prefix . $media->id . '/' . ($custom_file_name ?: $media->file_name));
}

/**
 * Creates a fake Excel file with specific test data
 *
 * @param array $data Array of rows to write to Excel
 * @param string $filename Optional custom filename
 * @return UploadedFile
 */
function createFakeExcelFileWithData(array $data, string $filename = 'test.xlsx'): UploadedFile
{
    $tempPath = sys_get_temp_dir() . '/' . uniqid();

    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // Write data to sheet using coordinate notation
    foreach ($data as $row_index => $row) {
        foreach ($row as $column_index => $value) {
            $column = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($column_index + 1);
            $coordinate = $column . ($row_index + 1);
            $sheet->setCellValue($coordinate, $value);
        }
    }

    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save($tempPath);

    return new UploadedFile(
        $tempPath,
        $filename,
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        null,
        true
    );
}
