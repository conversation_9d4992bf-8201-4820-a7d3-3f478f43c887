<?php

/**
 * Complete UnionPay API Integration Test
 * 
 * This file demonstrates:
 * 1. JWE Encryption for sensitive data
 * 2. JWS Signature for request authentication
 * 3. Fund Inquiry API call
 * 4. Complete request/response handling
 */

require_once 'vendor/autoload.php';

class UnionPayCompleteTest
{
    private string $baseUrl;
    private string $sendPartyIIN;
    private string $forwardingIIN;
    private string $appId;
    private string $institutionPrivateKey;
    private string $upiPublicKey;
    private string $upiEncryptionKey;
    private string $signCertSerial;
    private string $encCertSerial;
    private string $upiSigningCertData;
    private string $upiEncryptionCertData;

    public function __construct()
    {
        // Configuration - Update these with your actual values
        $this->baseUrl = 'https://apigateway.unionpayintl.com';
        $this->sendPartyIIN = '22410702';  // Your institution IIN
        $this->forwardingIIN = '22410702'; // Forwarding institution IIN
        $this->appId = '22410702';         // Your app ID

        // Certificate serials (kid values)
        $this->signCertSerial = '1562032880608';    // Your institution's signing cert serial
        $this->encCertSerial = '1562032885962';     // UPI's encryption cert serial

        // Certificate data
        $this->upiSigningCertData = "MIIC9jCCAd6gAwIBAgIGAWuwalDKMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNVBAYTAkNOMREwDwYDVQQHEwhTaGVuemhlbjEMMAoGA1UEChMDVVBJMQ0wCwYDVQQDEwRVQUdTMB4XDTE5MDcwMjAyMDEyNVoXDTI5MDcwMjAyMDEyNVowPTELMAkGA1UEBhMCQ04xETAPBgNVBAcTCFNoZW56aGVuMQwwCgYDVQQKEwNVUEkxDTALBgNVBAMTBFVBR1MwggEgMA0GCSqGSIb3DQEBAQUAA4IBDQAwggEIAoIBAQCS8W0X6grXDmE7zHBcvxUXYONBq8xI2DszFyuxV5mle5HZ48T0/nNS4+hxrHJcD1fQR+fYdU4YKJN+bZSHJ33nsIHEUuUM1a1iv7INXQqUj0INK76yF4wrDIOukIghvLramqapTBej6iOAL4cIAyr+C0PoLFYzgOl+XHkuxml5QO+ASS6icX/3+gKbhSC5jzt9Fw7AUBVYG/WsdWAwWDTyZszjxiUB9wCAzt9kM2ULdrbheN7wSTCfeh3+mMNdGPq3gXV+Rfm69Am5XXxD9xXPNGD4Y7oBEsNV3DwBFHY6rm8SZurl9ug8tVwnxdPu7WXMwysCejAbOxzrnU4RNteRAgEDMA0GCSqGSIb3DQEBCwUAA4IBAQBzKs96ucqwZG6oVRHFgNLAoEsX//Xy3To7xRDUDyXz597Dvyu0HL/7sFZuWug4uS93VT7acmBwBzEd1u/6qqLTdGoLprz/EDCPyYo1G4Goo0cBRfP6CoPAKOXuh1TT5b8cNGUnSlJIdCqBQyTX6Yx90k8QqfMGxRdG02+A2TGyltcEKOCcF1KRuYqX4OJP2EY5sMtSttGIyMMu0VFJQn8K15UO9bjnvYEtx5lHCjmIx4ukXr5fZMuGLb530fb4X2RcaO/6fNNdAeA+aAitqTmmHef0USj6I7QtXlNG5H/MQVI7v8im42468er7OnPyV4sJXo/GdFKFYD59BoyrgwVm";

        $this->upiEncryptionCertData = "MIIC9jCCAd6gAwIBAgIGAWuwajvgMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNVBAYTAkNOMREwDwYDVQQHEwhTaGVuemhlbjEMMAoGA1UEChMDVVBJMQ0wCwYDVQQDEwRVQUdTMB4XDTE5MDcwMjAyMDEyMFoXDTI5MDcwMjAyMDEyMFowPTELMAkGA1UEBhMCQ04xETAPBgNVBAcTCFNoZW56aGVuMQwwCgYDVQQKEwNVUEkxDTALBgNVBAMTBFVBR1MwggEgMA0GCSqGSIb3DQEBAQUAA4IBDQAwggEIAoIBAQDPSTmjjBRH7HA9ZTVxtO5iWft5jrK9Yvk1nEIRTzcm6/yr7KANS5MGZZlW9621eFOUCdNgOR7LPcqJqlBcFguY5fgWhvdu227Xplz9gDrqOOzPECWtbW5/D0GOa2Hdt3ilqICSrnH+ogG3VtotDboyg5qR7IdOiUI/xJyV13sfTJRx+rud0sNTLi43KvkmWrKepAI8ax/o+XwWVJ7wD/cziA/89W6Y8lN7uv2U2rXiTu6eH2rk2Ljw9pZij0UCPqaZmDIqA9hH8ACb7V5gh8izYztUfwLs4auq4QcdTJ78w8LyHwyOzLXvfD2fFu/stSQ/Zi0Dft45DyCPD/TDFh9jAgEDMA0GCSqGSIb3DQEBCwUAA4IBAQBL5JgdZqSo+Iz4uIfNNjzKSWiA1lr8CPtCPUDbeqciJGy3KOldnAFFuMj/0kA2llHZiXGSxjm0+3+xiaexBahu/tQ3Hl2KgtvMeejV9pEvOblJL+8Ded6zJIP/GhbapySjgBFEMdCT+Lp2LRdghGXpNoBAuUeLhrHqIaApeC7txGrAYRAGO3HwaiZH5dCLu3wNPyiRO8bbQ2ogh2nF8HyYCQs4pzpvNlpWOEnntfZ2tUY7MDXe5hErg7vSpi8csi7uL1GpZWqYz+tJ8iMq+eT4Ipp/Rvv7yRL+10fvjRAJnNZi+iYmjn/DrlzwwAOuNtZrWv/0F2As0HPjp+frEczp";

        // Keys - Load only what we need for client-side operations
        $this->upiPublicKey = $this->loadUpiPublicKey();
        $this->upiEncryptionKey = $this->loadUpiEncryptionKey();

        // Note: We don't load institution private key since this is client-side only
        // In production, you would load your actual private key for JWS signing
        $this->institutionPrivateKey = $this->generateDummyPrivateKey();
    }

    /**
     * Main test function
     */
    public function runTest()
    {
        echo "=== UnionPay Complete Integration Test ===\n\n";

        try {
            // Step 1: Test JWE Encryption
            echo "1. Testing JWE Encryption...\n";
            $sensitiveData = [
                'pan' => '6212345678901234',
                'expiryDate' => '2512',
                'cvn2' => '123',
                'cardholderName' => 'Test User',
                'idType' => '01',
                'idNumber' => 'TEST123456789',
                'mobileNumber' => '+86-13812345678',
                'otp' => '123456'
            ];

            $encryptedData = $this->createJWE($sensitiveData);
            echo "✓ JWE Encryption successful\n";
            echo "Encrypted Data: " . substr($encryptedData, 0, 100) . "...\n\n";

            // Step 2: Test Fund Inquiry Request
            echo "2. Testing Fund Inquiry Request...\n";
            $fundInquiryResult = $this->fundInquiry('156'); // CNY currency code
            echo "✓ Fund Inquiry completed\n";
            echo "Result: " . json_encode($fundInquiryResult, JSON_PRETTY_PRINT) . "\n\n";

            // Step 3: Test Key Exchange (Public Key Management)
            echo "3. Testing Key Exchange (Public Key Management)...\n";
            $keyExchangeResult = $this->keyExchange([
                'signPublicKey' => $this->upiSigningCertData,
                'encPublicKey' => $this->upiEncryptionCertData
            ]);
            echo "✓ Key exchange completed\n";
            echo "Result: " . json_encode($keyExchangeResult, JSON_PRETTY_PRINT) . "\n\n";
        } catch (Exception $e) {
            echo "✗ Error: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    /**
     * Create JWE encrypted payload
     */
    private function createJWE(array $sensitiveData): string
    {
        // Step 1: Create JWE Protected Header
        $header = [
            'alg' => 'RSA1_5',
            'enc' => 'A128CBC-HS256',
            'kid' => $this->encCertSerial
        ];
        $encodedHeader = $this->base64UrlEncode(json_encode($header));

        // Step 2: Generate random CEK (Content Encryption Key) for AES
        $cek = random_bytes(32); // 256-bit key for AES-256

        // Step 3: Prepare public key resource
        $publicKeyResource = openssl_pkey_get_public($this->upiEncryptionKey);
        if ($publicKeyResource === false) {
            throw new Exception('Invalid public key format: ' . openssl_error_string());
        }

        // Step 4: Encrypt CEK with RSA public key
        $encryptedKey = '';
        if (!openssl_public_encrypt($cek, $encryptedKey, $publicKeyResource, OPENSSL_PKCS1_PADDING)) {
            throw new Exception('Failed to encrypt CEK with RSA: ' . openssl_error_string());
        }
        $encodedEncryptedKey = $this->base64UrlEncode($encryptedKey);

        // Step 5: Generate random IV for AES
        $iv = random_bytes(16); // 128-bit IV for AES
        $encodedIV = $this->base64UrlEncode($iv);

        // Step 6: Encrypt payload with AES-CBC
        $payload = json_encode($sensitiveData);
        $ciphertext = openssl_encrypt($payload, 'AES-256-CBC', $cek, OPENSSL_RAW_DATA, $iv);
        if ($ciphertext === false) {
            throw new Exception('Failed to encrypt payload with AES: ' . openssl_error_string());
        }
        $encodedCiphertext = $this->base64UrlEncode($ciphertext);

        // Step 7: Calculate authentication tag (HMAC)
        $authData = $encodedHeader;
        $hmacKey = substr($cek, 16, 16); // Use second half of CEK for HMAC
        $authTag = hash_hmac('sha256', $authData . '.' . $encodedIV . '.' . $encodedCiphertext, $hmacKey, true);
        $authTag = substr($authTag, 0, 16); // Truncate to 128 bits
        $encodedAuthTag = $this->base64UrlEncode($authTag);

        // Step 8: Construct final JWE
        return $encodedHeader . '.' . $encodedEncryptedKey . '.' . $encodedIV . '.' . $encodedCiphertext . '.' . $encodedAuthTag;
    }

    /**
     * Create JWS signature with mandatory UPI-REQPATH in protected header
     */
    private function createJWS(string $payload, string $requestPath, string $uuid, string $timestamp): string
    {
        // Create JWS Protected Header with ALL UPI-* fields inside (not as separate HTTP headers)
        $header = [
            'alg' => 'RS256',
            'kid' => $this->signCertSerial,
            'crit' => ['UPI-UUID', 'UPI-TIMESTAMP', 'UPI-APPID', 'UPI-REQPATH'],
            'UPI-UUID' => $uuid,
            'UPI-TIMESTAMP' => $timestamp,
            'UPI-APPID' => $this->appId,
            'UPI-REQPATH' => $requestPath,
        ];

        $encodedHeader = $this->base64UrlEncode(json_encode($header, JSON_UNESCAPED_SLASHES));
        $encodedPayload = $this->base64UrlEncode($payload);

        // Create signing input
        $signingInput = $encodedHeader . '.' . $encodedPayload;

        // Sign with private key
        $signature = '';
        if (!openssl_sign($signingInput, $signature, $this->institutionPrivateKey, OPENSSL_ALGO_SHA256)) {
            throw new Exception('Failed to create JWS signature: ' . openssl_error_string());
        }
        $encodedSignature = $this->base64UrlEncode($signature);

        // Return JWS in header..signature format (no payload for detached signature)
        return $encodedHeader . '..' . $encodedSignature;
    }

    /**
     * Fund Inquiry API call
     */
    private function fundInquiry(string $currencyCode): array
    {
        $msgId = $this->generateMessageId();

        $requestBody = [
            'msgID' => $msgId,
            'sendPartyIIN' => $this->sendPartyIIN,
            'forwardingIIN' => $this->forwardingIIN,
            'localTxnDateTime' => date('mdHis'),
            'trxCurrency' => $currencyCode  // Corrected field name
        ];

        return $this->makeApiRequest('/umts/iswitch/v1/fundinquiry', $requestBody);
    }

    /**
     * Public Key Management - Key Exchange API
     * Used to update institution's public key certificates with UnionPay
     */
    private function keyExchange(array $keyData): array
    {
        $msgId = $this->generateKeyExchangeMessageId();

        $requestBody = [
            'msgID' => $msgId,
            'sendPartyIIN' => $this->sendPartyIIN,
            'forwardingIIN' => $this->forwardingIIN,
            'localTxnDateTime' => date('mdHis')
        ];

        // Add signature public key certificate if provided
        if (isset($keyData['signPublicKey'])) {
            $requestBody['signPublicKey'] = $keyData['signPublicKey'];
        }

        // Add encryption public key certificate if provided
        if (isset($keyData['encPublicKey'])) {
            $requestBody['encPublicKey'] = $keyData['encPublicKey'];
        }

        return $this->makeApiRequest('/umts/iswitch/v1/keyexchange', $requestBody);
    }



    /**
     * Make API request with proper headers and signature
     */
    private function makeApiRequest(string $endpoint, array $data, string $method = 'POST'): array
    {
        $url = $this->baseUrl . $endpoint;
        $payload = json_encode($data);
        $requestPath = parse_url($url, PHP_URL_PATH);

        // Generate security values
        $uuid = $this->generateUUID();
        $timestamp = $this->generateUnixTimestamp();

        // Create JWS signature with ALL UPI-* fields embedded in protected header
        $jwsSignature = $this->createJWS($payload, $requestPath, $uuid, $timestamp);

        // Prepare HTTP headers - ONLY UPI-JWS and Content-Type
        $headers = [
            'Content-Type: application/json',
            'UPI-JWS: ' . $jwsSignature
        ];

        echo "Making API request to: $url\n";
        echo "Request Path: $requestPath\n";
        echo "JWS Signature: $jwsSignature\n";
        echo "HTTP Headers:\n";
        foreach ($headers as $header) {
            echo "  $header\n";
        }
        echo "Payload: $payload\n";

        // Make HTTP request using cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $method !== 'GET' ? $payload : null,
            CURLOPT_SSL_VERIFYPEER => false, // For testing only
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        echo "HTTP Response Code: $httpCode\n";
        echo "Response: $response\n";

        return [
            'http_code' => $httpCode,
            'response' => json_decode($response, true) ?: $response,
            'success' => $httpCode >= 200 && $httpCode < 300
        ];
    }

    // Utility functions
    private function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }



    private function generateUnixTimestamp(): string
    {
        return (string) time();
    }

    private function generateMessageId(): string
    {
        return $this->sendPartyIIN . $this->forwardingIIN . date('mdHis') . sprintf('%06d', mt_rand(0, 999999));
    }

    /**
     * Generate message ID specifically for KEY_EXCHANGE API
     * Format: "U" + INS ID + Transaction Time + Serial Number
     */
    private function generateKeyExchangeMessageId(): string
    {
        $insId = $this->sendPartyIIN; // Institution ID
        $transactionTime = date('YmdHis'); // 14-digit: YYYYMMDDhhmmss
        $serialNumber = sprintf('%06d', mt_rand(0, 999999)); // 6-digit serial

        return "U" . $insId . $transactionTime . $serialNumber;
    }

    // Key loading functions - Update these with your actual key loading logic
    private function generateDummyPrivateKey(): string
    {
        // For testing purposes, generate a dummy private key
        // In production, replace this with your actual private key loading:
        // $keyPath = __DIR__ . '/institution_private_key.pem';
        // return file_get_contents($keyPath);

        echo "Warning: Using generated test private key. Replace with actual key for production.\n";
        $config = [
            "digest_alg" => "sha256",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];
        $res = openssl_pkey_new($config);
        openssl_pkey_export($res, $privateKey);
        return $privateKey;
    }

    private function loadUpiPublicKey(): string
    {
        return $this->extractPublicKeyFromCertData($this->upiSigningCertData);
    }

    private function loadUpiEncryptionKey(): string
    {
        return $this->extractPublicKeyFromCertData($this->upiEncryptionCertData);
    }

    /**
     * Helper function to extract public key from certificate data
     */
    private function extractPublicKeyFromCertData(string $certData): string
    {
        // Convert certificate data to PEM format
        $certPem = "-----BEGIN CERTIFICATE-----\n" . chunk_split($certData, 64, "\n") . "-----END CERTIFICATE-----\n";

        // Extract public key from certificate
        $cert = openssl_x509_read($certPem);
        if ($cert === false) {
            throw new Exception('Failed to read certificate: ' . openssl_error_string());
        }

        $publicKey = openssl_pkey_get_public($cert);
        if ($publicKey === false) {
            throw new Exception('Failed to extract public key from certificate: ' . openssl_error_string());
        }

        $keyDetails = openssl_pkey_get_details($publicKey);
        if ($keyDetails === false) {
            throw new Exception('Failed to get public key details: ' . openssl_error_string());
        }

        return $keyDetails['key'];
    }
}

// Run the test
if (php_sapi_name() === 'cli') {
    $test = new UnionPayCompleteTest();
    $test->runTest();
} else {
    echo "This script should be run from command line.\n";
}
