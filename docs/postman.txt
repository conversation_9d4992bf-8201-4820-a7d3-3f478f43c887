can write the postman POST/PUT/DELETE endpoint with its payload (only update this when the the endpoint exist in the routes (api.php))

{{base_url}}/api/auth/register [POST]
{
  "firstname": "<PERSON>",
  "lastname": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+12125551234",
  "dob": "1990-01-01"
}

{{base_url}}/api/users [POST]
{
  "firstname": "<PERSON>",
  "lastname": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+12125551234",
  "dob": "1990-01-01",
  "roles": [2],
  "is_active": true,
  "profile": {
    "bio": "This is my professional bio",
    "contact": "<EMAIL>",
    "emergency_contact": "<PERSON>",
    "emergency_phone": "+12125555555",
    "street_address": "123 Main Street",
    "city": "New York",
    "zip": "10001",
    "race": "Preferred not to say"
  }
}

{{base_url}}/api/branches [POST]
{
  "name": "Downtown Branch",
  "image": "https://avatar.iran.liara.run/public",
  "geolocation": {
    "lat": 40.7128,
    "lng": -74.0060,
    "address": "123 Main Street, New York, NY 10001"
  },
  "active": true,
  "virtual": false,
  "details": {
    "company_name": "Downtown Branch LLC",
    "company_registration_no": "12345678901",
    "company_address": "123 Main Street, New York, NY 10001",
    "company_phone": "+12125551234",
    "company_email": "<EMAIL>"
  }
}

{{base_url}}/api/branches/{id} [PUT]
{
  "name": "Updated Downtown Branch",
  "image": "https://avatar.iran.liara.run/public",
  "geolocation": {
    "lat": 40.7128,
    "lng": -74.0060,
    "address": "456 Updated Street, New York, NY 10001"
  },
  "active": true,
  "virtual": false,
  "details": {
    "company_name": "Updated Downtown Branch LLC",
    "company_registration_no": "12345678901",
    "company_address": "456 Updated Street, New York, NY 10001",
    "company_phone": "+12125559999",
    "company_email": "<EMAIL>"
  }
}

{{base_url}}/api/branches/{id} [DELETE]
// No body required

{{base_url}}/api/notification-templates [POST] (WhatsApp)
{
  "name": "whatsapp_notification",
  "description": "WhatsApp notification template",
  "channel": "whatsapp",
  "locale": "en",
  "content": "👋 Hello {{user_name}}! {{message}} Sent on {{date}}."
}

{{base_url}}/api/notification-templates/{id} [PUT]
{
  "name": "updated_welcome_notification",
  "description": "Updated welcome notification for new users",
  "channel": "mail",
  "locale": "en",
  "subject": "Welcome to our platform, {{user_name}}!",
  "content": "Hello {{user_name}},<br><br>We're excited to have you join us! {{message}}<br><br>Best regards,<br>The Team",
  "is_active": true
}

{{base_url}}/api/notification-templates/{id} [DELETE]
// No body required

{{base_url}}/api/products [POST]
{
  "name": "Premium Wireless Headphones",
  "description": "High-quality wireless headphones with noise cancellation and premium sound quality. Perfect for music lovers and professionals.",
  "price": 299.99,
  "sale_price": 249.99,
  "sku": "PWH-001",
  "barcode": "1234567890123",
  "is_taxable": true,
  "tax_class_id": 1,
  "shipping_class_id": 1,
  "meta": {
    "brand": "AudioTech",
    "warranty": "2 years",
    "color": "Black"
  },
  "status": "publish",
  "unit": "piece",
  "height": 20.5,
  "width": 18.0,
  "length": 8.5,
  "weight": 0.35,
  "is_active": true,
  "is_bundle": false,
  "is_require_double_scanning": false,
  "category_ids": [1, 2],
  "variants": [
    {
      "title": "Black - Standard",
      "price": "299.99",
      "sale_price": "249.99",
      "sku": "PWH-001-BLK",
      "barcode": "1234567890124",
      "is_active": true,
      "width": 18.0,
      "height": 20.5,
      "length": 8.5,
      "weight": 0.35
    },
    {
      "title": "White - Standard",
      "price": "299.99",
      "sale_price": "249.99",
      "sku": "PWH-001-WHT",
      "barcode": "1234567890125",
      "is_active": true,
      "width": 18.0,
      "height": 20.5,
      "length": 8.5,
      "weight": 0.35
    }
  ],
  "warehouses": [
    {
      "id": 1,
      "label": "Main Warehouse"
    },
    {
      "id": 2,
      "label": "Secondary Storage"
    }
  ]
}

{{base_url}}/api/products/{id} [PUT]
{
  "name": "Updated Premium Wireless Headphones",
  "description": "Updated description for high-quality wireless headphones with enhanced features.",
  "price": 319.99,
  "sale_price": 269.99,
  "sku": "PWH-001-V2",
  "barcode": "1234567890126",
  "is_taxable": true,
  "tax_class_id": 1,
  "shipping_class_id": 1,
  "meta": {
    "brand": "AudioTech",
    "warranty": "3 years",
    "color": "Black",
    "version": "2.0"
  },
  "status": "publish",
  "unit": "piece",
  "height": 21.0,
  "width": 18.5,
  "length": 9.0,
  "weight": 0.32,
  "is_active": true,
  "is_bundle": false,
  "is_require_double_scanning": false,
  "category_ids": [1, 3],
  "variants": [
    {
      "id": 1,
      "title": "Black - Premium",
      "price": "319.99",
      "sale_price": "269.99",
      "sku": "PWH-001-V2-BLK",
      "barcode": "1234567890127",
      "is_active": true,
      "width": 18.5,
      "height": 21.0,
      "length": 9.0,
      "weight": 0.32
    }
  ],
  "warehouses": [
    {
      "id": 1,
      "label": "Main Warehouse"
    }
  ]
}

{{base_url}}/api/products/{id} [DELETE]
// No body required

{{base_url}}/api/products [POST] (Bundle Product)
{
  "name": "Complete Home Office Bundle",
  "description": "Everything you need for a productive home office setup including desk accessories and tech essentials.",
  "price": 599.99,
  "sale_price": 499.99,
  "sku": "BUNDLE-HOME-001",
  "barcode": "1234567890200",
  "is_taxable": true,
  "tax_class_id": 1,
  "shipping_class_id": 2,
  "meta": {
    "bundle_type": "fixed",
    "savings": "100.00"
  },
  "status": "publish",
  "unit": "set",
  "is_active": true,
  "is_bundle": true,
  "is_require_double_scanning": false,
  "category_ids": [1, 4],
  "bundle_items": [
    {
      "item_product_id": "PWH-001",
      "quantity": 1,
      "price": 299.99
    },
    {
      "item_product_id": "DESK-LAMP-001",
      "quantity": 1,
      "price": 89.99
    },
    {
      "item_product_id": "MOUSE-PAD-001",
      "quantity": 2,
      "price": 19.99
    }
  ]
}

{{base_url}}/api/products [POST] (Simple Product - No Variants)
{
  "name": "Wireless Mouse",
  "description": "Ergonomic wireless mouse with precision tracking and long battery life.",
  "price": 49.99,
  "sale_price": 39.99,
  "sku": "MOUSE-001",
  "barcode": "1234567890300",
  "is_taxable": true,
  "tax_class_id": 1,
  "shipping_class_id": 1,
  "meta": {
    "brand": "TechGear",
    "warranty": "1 year",
    "battery_life": "12 months"
  },
  "status": "publish",
  "unit": "piece",
  "height": 3.5,
  "width": 6.2,
  "length": 11.8,
  "weight": 0.085,
  "is_active": true,
  "is_bundle": false,
  "is_require_double_scanning": true,
  "category_ids": [1, 5]
}

{{base_url}}/api/categories [POST]
{
  "name": "Electronics",
  "parent_id": null,
  "type": "default",
  "is_active": true,
  "meta": {
    "description": "Electronic devices and gadgets",
    "keywords": ["electronics", "gadgets", "technology"],
    "featured": true
  },
  "order_column": 1
}

{{base_url}}/api/categories [POST] (Subcategory)
{
  "name": "Smartphones",
  "parent_id": 1,
  "type": "default",
  "is_active": true,
  "meta": {
    "description": "Mobile phones and smartphones",
    "keywords": ["phones", "mobile", "smartphones"],
    "brand_filter": true
  },
  "order_column": 1
}

{{base_url}}/api/categories [POST] (Fashion Category)
{
  "name": "Fashion & Clothing",
  "parent_id": null,
  "type": "default",
  "is_active": true,
  "meta": {
    "description": "Clothing, accessories and fashion items",
    "keywords": ["fashion", "clothing", "apparel", "accessories"],
    "seasonal": true,
    "image": "fashion-category.jpg"
  },
  "order_column": 2
}

{{base_url}}/api/categories/{id} [PUT]
{
  "name": "Electronics & Technology",
  "parent_id": null,
  "type": "default",
  "is_active": true,
  "meta": {
    "description": "Electronic devices, gadgets and technology products",
    "keywords": ["electronics", "technology", "gadgets", "devices"],
    "featured": true,
    "promotion": "Summer Sale 2025"
  },
  "order_column": 1
}

{{base_url}}/api/categories/{id} [DELETE]
// No body required
// Note: Cannot delete categories that have subcategories

{{base_url}}/api/class-types [POST]
{
  "name": "Hatha Yoga",
  "description": "A gentle form of yoga that focuses on static postures and breathing techniques. Perfect for beginners and those looking for a slower-paced practice.",
 
  "duration_in_minutes": 60,
  "class_count": 1,
  "price_type": "fixed",
  "price": 25.00,
  "tax_class_id": 1,
  "colour": "#4CAF50",
  "is_addon": false,
  "is_bookable": true,
  "is_active": true
}



{{base_url}}/api/class-types/{id} [PUT]
{
  "name": "Advanced Hatha Yoga",
  "description": "An advanced form of Hatha yoga incorporating challenging postures and advanced breathing techniques. Suitable for experienced practitioners.",
  "duration_in_minutes": 90,
  "class_count": 1,
  "price_type": "fixed",
  "price": 35.00,
  "tax_class_id": 1,
  "colour": "#4CAF50",
  "is_addon": false,
  "is_bookable": true,
  "is_active": true
}

{{base_url}}/api/class-types/{id} [DELETE]
// No body required
