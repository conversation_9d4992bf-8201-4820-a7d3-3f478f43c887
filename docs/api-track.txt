# API Mutation Endpoints (POST/PUT/DELETE only)
# Extracted from ecommerce-core route listing - GET/HEAD methods removed
# Total endpoints: 287 mutation endpoints

+--------+---------------+----------------------------------------------------------------------------------+------------------------------------------+--------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------+
| Domain | Method        | URI                                                                              | Name                                     | Action                                                                               | Middleware                                                                                       |
+--------+---------------+----------------------------------------------------------------------------------+------------------------------------------+--------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------+
|        | DELETE        | address/{address}                                                                | address.destroy                          | App\Http\Controllers\AddressController@destroy                                       | api                                                                                              |
|        | PUT           | address/{id?}                                                                    | generated::bgRqoXg9rRfvaaE0              | App\Http\Controllers\AddressController@storeOrUpdate                                 | api                                                                                              |
|        | POST          | attachments                                                                      | attachments.store                        | App\Http\Controllers\AttachmentController@store                                      | api                                                                                              |
|        | POST          | attachments/url                                                                  | generated::3uE5x2VWATtdPGkp              | App\Http\Controllers\AttachmentController@storeFromS3Url                             | api                                                                                              |
|        | POST          | attribute-values                                                                 | attribute-values.store                   | App\Http\Controllers\AttributeValueController@store                                  | api                                                                                              |
|        | PUT|PATCH     | attribute-values/{attribute_value}                                               | attribute-values.update                  | App\Http\Controllers\AttributeValueController@update                                 | api                                                                                              |
|        | DELETE        | attribute-values/{attribute_value}                                               | attribute-values.destroy                 | App\Http\Controllers\AttributeValueController@destroy                                | api                                                                                              |
|        | POST          | attributes                                                                       | attributes.store                         | App\Http\Controllers\AttributeController@store                                       | api                                                                                              |
|        | PUT|PATCH     | attributes/{attribute}                                                           | attributes.update                        | App\Http\Controllers\AttributeController@update                                      | api                                                                                              |
|        | DELETE        | attributes/{attribute}                                                           | attributes.destroy                       | App\Http\Controllers\AttributeController@destroy                                     | api                                                                                              |
|        | POST          | auth/tng                                                                         | generated::wDbPC3KAE58W3WlV              | App\Http\Controllers\UserController@tngAuth                                          | api                                                                                              |
|        | POST          | banners                                                                          | banners.store                            | App\Http\Controllers\BannerController@store                                          | api                                                                                              |
|        | POST          | banners/search                                                                   | generated::AiTokbQwoHCuanRF              | App\Http\Controllers\BannerController@search                                         | api                                                                                              |
|        | PUT|PATCH     | banners/{banner}                                                                 | banners.update                           | App\Http\Controllers\BannerController@update                                         | api                                                                                              |
|        | DELETE        | banners/{banner}                                                                 | banners.destroy                          | App\Http\Controllers\BannerController@destroy                                        | api                                                                                              |
|        | PUT           | batch-categories                                                                 | generated::afQUSHdymgpxrrAt              | App\Http\Controllers\CategoryController@batchUpdate                                  | api                                                                                              |
|        | POST          | block-schedules                                                                  | generated::epsTvr5vtgil3AK8              | App\Http\Controllers\ClassScheduleController@blockClassSchedule                      | api                                                                                              |
|        | PUT           | block-schedules/{id}                                                             | generated::26KBAI4CofxB6aDJ              | App\Http\Controllers\ClassScheduleController@updateBlockClassSchedule                | api                                                                                              |
|        | DELETE        | block-schedules/{id}                                                             | generated::vTUSgICmA4NUMOtC              | App\Http\Controllers\ClassScheduleController@deleteBlockedSchedule                   | api                                                                                              |
|        | POST          | blocked_domains                                                                  | blocked_domains.store                    | App\Http\Controllers\BlockedDomainController@store                                   | api                                                                                              |
|        | PUT|PATCH     | blocked_domains/{blocked_domain}                                                 | blocked_domains.update                   | App\Http\Controllers\BlockedDomainController@update                                  | api                                                                                              |
|        | DELETE        | blocked_domains/{blocked_domain}                                                 | blocked_domains.destroy                  | App\Http\Controllers\BlockedDomainController@destroy                                 | api                                                                                              |
|        | POST          | bookings                                                                         | generated::pPoSFelM2KdHp5vj              | App\Http\Controllers\ClassScheduleBookingController@customerBookClass                | api                                                                                              |
|        | POST          | bookings/confirm                                                                 | generated::Jt4ii9Hgakj0oXtH              | App\Http\Controllers\ClassScheduleBookingController@confirmBookingByKey              | api                                                                                              |
|        | POST          | bookings/key                                                                     | generated::esBvjWVyJpw4MA4h              | App\Http\Controllers\ClassScheduleBookingController@getBookingByKey                  | api                                                                                              |
|        | POST          | bookings/verify                                                                  | generated::Lxf1DNn96m4wJHob              | App\Http\Controllers\ClassScheduleBookingController@verifyCanBook                    | api                                                                                              |
|        | PUT           | bookings/{id}/cancel                                                             | generated::NpDKaSuSnBdyKME5              | App\Http\Controllers\ClassScheduleBookingController@customerCancelClass              | api                                                                                              |
|        | POST          | bookings/{id}/confirm                                                            | generated::R34wf1ImaeXUWgHH              | App\Http\Controllers\ClassScheduleBookingController@confirmBookingById               | api                                                                                              |
|        | POST          | branch                                                                           | branch.store                             | App\Http\Controllers\BranchController@store                                          | api                                                                                              |
|        | POST          | branch-used                                                                      | branch-used.store                        | App\Http\Controllers\BranchUsedController@store                                      | api                                                                                              |
|        | PUT|PATCH     | branch-used/{branch_used}                                                        | branch-used.update                       | App\Http\Controllers\BranchUsedController@update                                     | api                                                                                              |
|        | DELETE        | branch-used/{branch_used}                                                        | branch-used.destroy                      | App\Http\Controllers\BranchUsedController@destroy                                    | api                                                                                              |
|        | PUT           | branch-used/{id}/{status}                                                        | generated::oArVfo4IXlu1cHLl              | App\Http\Controllers\BranchUsedController@changeStatus                               | api                                                                                              |
|        | PUT|PATCH     | branch/{branch}                                                                  | branch.update                            | App\Http\Controllers\BranchController@update                                         | api                                                                                              |
|        | DELETE        | branch/{branch}                                                                  | branch.destroy                           | App\Http\Controllers\BranchController@destroy                                        | api                                                                                              |
|        | PUT           | branch/{id}/pin                                                                  | generated::nhAFcpZDUycp1RVB              | App\Http\Controllers\BranchController@updatePin                                      | api                                                                                              |
|        | PUT           | branch/{id}/products                                                             | generated::DNybiZZXMVVDepIA              | App\Http\Controllers\BranchController@updateProducts                                 | api                                                                                              |
|        | PUT           | cart                                                                             | generated::VgdaSjD1T0yc2Rn9              | App\Http\Controllers\CartController@update                                           | api                                                                                              |
|        | POST          | cart/buy-again                                                                   | generated::rKgUndQeX2AXr1NW              | App\Http\Controllers\CartController@buyAgain                                         | api                                                                                              |
|        | POST          | categories                                                                       | categories.store                         | App\Http\Controllers\CategoryController@store                                        | api                                                                                              |
|        | PUT|PATCH     | categories/{category}                                                            | categories.update                        | App\Http\Controllers\CategoryController@update                                       | api                                                                                              |
|        | DELETE        | categories/{category}                                                            | categories.destroy                       | App\Http\Controllers\CategoryController@destroy                                      | api                                                                                              |
|        | PUT           | categories/{id}/products                                                         | generated::ASIvC6rRdqzHlNwI              | App\Http\Controllers\CategoryController@updateProducts                               | api                                                                                              |
|        | PUT           | categories/{id}/sorting                                                          | generated::t570Y41uclwYC5FL              | App\Http\Controllers\CategoryController@updateProductSorting                         | api                                                                                              |
|        | POST          | change-password                                                                  | generated::kcGold8v2grQOeBG              | App\Http\Controllers\UserController@changePassword                                   | api                                                                                              |
|        | POST          | checkin                                                                          | generated::l02tjfCS9DOgzVhi              | App\Http\Controllers\DailyCheckinController@checkin                                  | api                                                                                              |
|        | PUT           | checkout                                                                         | generated::3AqTQqGBTOmKk8tV              | App\Http\Controllers\CheckoutController@update                                       | api                                                                                              |
|        | POST          | class-types                                                                      | class-types.store                        | App\Http\Controllers\ClassTypeController@store                                       | api                                                                                              |
|        | POST          | class-types/import                                                               | generated::8hmNE0bxfP0moHkk              | App\Http\Controllers\ClassTypeController@import                                      | api                                                                                              |
|        | PUT|PATCH     | class-types/{class_type}                                                         | class-types.update                       | App\Http\Controllers\ClassTypeController@update                                      | api                                                                                              |
|        | DELETE        | class-types/{class_type}                                                         | class-types.destroy                      | App\Http\Controllers\ClassTypeController@destroy                                     | api                                                                                              |
|        | POST          | commission-groups                                                                | commission-groups.store                  | App\Http\Controllers\CommissionGroupController@store                                 | api                                                                                              |
|        | POST          | commission-groups/{commission_group_id}/schemes                                  | generated::bMfsD25HpbJFFw6I              | App\Http\Controllers\CommissionGroupSchemeController@createOrUpdate                  | api                                                                                              |
|        | PUT           | commission-groups/{commission_group_id}/schemes/{scheme_id}/attach               | generated::jLOrwQz7HiAElKcc              | App\Http\Controllers\CommissionGroupSchemeController@attachToCommissionGroup         | api                                                                                              |
|        | PUT           | commission-groups/{commission_group_id}/schemes/{scheme_id}/detach               | generated::MB8FiVkM3g78ZQ1V              | App\Http\Controllers\CommissionGroupSchemeController@detachFromCommissionGroup       | api                                                                                              |
|        | PUT           | commission-groups/{commission_group_id}/sync-users                               | generated::goDMoKvIuiMAg93E              | App\Http\Controllers\CommissionGroupController@syncUser                              | api                                                                                              |
|        | PUT|PATCH     | commission-groups/{commission_group}                                             | commission-groups.update                 | App\Http\Controllers\CommissionGroupController@update                                | api                                                                                              |
|        | POST          | commission-reports                                                               | generated::Ez9hjpx92nzFFIrh              | App\Http\Controllers\CommissionReportController@generateReport                       | api                                                                                              |
|        | PUT           | commission-reports/{id}/status                                                   | generated::OMyiYzyexxIdVVRC              | App\Http\Controllers\CommissionReportController@changeStatus                         | api                                                                                              |
|        | POST          | contacts                                                                         | contacts.store                           | App\Http\Controllers\ContactController@store                                         | api                                                                                              |
|        | POST          | contracts                                                                        | contracts.store                          | App\Http\Controllers\ContractController@store                                        | api                                                                                              |
|        | PUT|PATCH     | contracts/{contract}                                                             | contracts.update                         | App\Http\Controllers\ContractController@update                                       | api                                                                                              |
|        | DELETE        | contracts/{contract}                                                             | contracts.destroy                        | App\Http\Controllers\ContractController@destroy                                      | api                                                                                              |
|        | POST          | coupons                                                                          | coupons.store                            | App\Http\Controllers\CouponController@store                                          | api                                                                                              |
|        | PUT|PATCH     | coupons/{coupon}                                                                 | coupons.update                           | App\Http\Controllers\CouponController@update                                         | api                                                                                              |
|        | DELETE        | coupons/{coupon}                                                                 | coupons.destroy                          | App\Http\Controllers\CouponController@destroy                                        | api                                                                                              |
|        | POST          | cp/v1/reservations                                                               | generated::9bxUIw2l7ZhrJHOe              | App\Http\Controllers\ClassPassController@makeReservation                             | api                                                                                              |
|        | POST          | credit-memo/{id}/refund                                                          | generated::2c7G4MpybMtSq1AF              | App\Http\Controllers\CreditMemoController@refund                                     | api                                                                                              |
|        | POST          | credits-topup/checkout                                                           | generated::PRRSaplYQU1CInrg              | App\Http\Controllers\OrderController@creditTopUpCheckout                             | api                                                                                              |
|        | POST          | customer_groups                                                                  | customer_groups.store                    | App\Http\Controllers\CustomerGroupController@store                                   | api                                                                                              |
|        | PUT|PATCH     | customer_groups/{customer_group}                                                 | customer_groups.update                   | App\Http\Controllers\CustomerGroupController@update                                  | api                                                                                              |
|        | POST          | designations                                                                     | designations.store                       | App\Http\Controllers\DesignationController@store                                     | api                                                                                              |
|        | PUT|PATCH     | designations/{designation}                                                       | designations.update                      | App\Http\Controllers\DesignationController@update                                    | api                                                                                              |
|        | DELETE        | designations/{designation}                                                       | designations.destroy                     | App\Http\Controllers\DesignationController@destroy                                   | api                                                                                              |
|        | POST          | difficulty-levels                                                                | difficulty-levels.store                  | App\Http\Controllers\DifficultyLevelController@store                                 | api                                                                                              |
|        | PUT           | difficulty-levels-order                                                          | generated::XkCTs6McAOAvNWiP              | App\Http\Controllers\DifficultyLevelController@updateOrdering                        | api                                                                                              |
|        | PUT|PATCH     | difficulty-levels/{difficulty_level}                                             | difficulty-levels.update                 | App\Http\Controllers\DifficultyLevelController@update                                | api                                                                                              |
|        | DELETE        | difficulty-levels/{difficulty_level}                                             | difficulty-levels.destroy                | App\Http\Controllers\DifficultyLevelController@destroy                               | api                                                                                              |
|        | POST          | forget-password                                                                  | generated::qgu2i7UjX1Gh3SFM              | App\Http\Controllers\UserController@forgetPassword                                   | api                                                                                              |
|        | POST          | forms                                                                            | forms.store                              | App\Http\Controllers\FormController@store                                            | api                                                                                              |
|        | POST          | forms/sign                                                                       | generated::90l52nMHNXSZpHae              | App\Http\Controllers\FormController@signForm                                         | api                                                                                              |
|        | PUT|PATCH     | forms/{form}                                                                     | forms.update                             | App\Http\Controllers\FormController@update                                           | api                                                                                              |
|        | POST          | frontend/orders/payment/callback/{gateway_code}                                  | generated::e1bm2mKS2B1mdWDS              | App\Http\Controllers\OrderController@frontendPaymentCallback                         | api                                                                                              |
|        | POST          | gift-card-template-codes                                                         | gift-card-template-codes.store           | App\Http\Controllers\GiftCardTemplatePresetCodeController@store                      | api                                                                                              |
|        | POST          | gift-card-templates                                                              | gift-card-templates.store                | App\Http\Controllers\GiftCardTemplateController@store                                | api                                                                                              |
|        | PUT|PATCH     | gift-card-templates/{gift_card_template}                                         | gift-card-templates.update               | App\Http\Controllers\GiftCardTemplateController@update                               | api                                                                                              |
|        | POST          | gift-cards/check-balance                                                         | generated::gucRH18qWgRsWGTa              | App\Http\Controllers\GiftCardController@checkBalance                                 | api                                                                                              |
|        | POST          | gift-cards/checkout                                                              | generated::Rkv5wEYmefbuVFGW              | App\Http\Controllers\OrderController@giftCardCheckout                                | api                                                                                              |
|        | POST          | gift-cards/format-content                                                        | generated::0E4LocI6rRHUY7lj              | App\Http\Controllers\GiftCardController@formatHtmlContent                            | api                                                                                              |
|        | PUT|PATCH     | gift-cards/{gift_card}                                                           | gift-cards.update                        | App\Http\Controllers\GiftCardController@update                                       | api                                                                                              |
|        | POST          | gift-cards/{id}/email                                                            | generated::rBkTIX9xaa2I7t51              | App\Http\Controllers\GiftCardController@sendEmail                                    | api                                                                                              |
|        | GET|POST|HEAD | graphql                                                                          | graphql                                  | Nuwave\Lighthouse\Support\Http\Controllers\GraphQLController                         | Nuwave\Lighthouse\Support\Http\Middleware\AcceptJson                                             |
|        | POST          | instructor/availabilities                                                        | generated::inJp1UePC1z6BW2p              | App\Http\Controllers\PrivateBoardInstructorController@getInstructorAvailabilities    | api                                                                                              |
|        | POST          | instructor/send-verify-confirmation-email                                        | generated::lqy6nsXJ6TNVjR1g              | App\Http\Controllers\InstructorController@sendVerifyConfirmationEmail                | api                                                                                              |
|        | POST          | instructors                                                                      | instructors.store                        | App\Http\Controllers\InstructorController@store                                      | api                                                                                              |
|        | PUT           | instructors-order                                                                | generated::5TJCYTmy3QZzgQ5I              | App\Http\Controllers\InstructorController@updateOrdering                             | api                                                                                              |
|        | PUT           | instructors/class-type/{id}                                                      | generated::48FPJnpyZjdgO7c0              | App\Http\Controllers\InstructorController@updateInstructorClassTypeOption            | api                                                                                              |
|        | DELETE        | instructors/class-type/{id}                                                      | generated::oIwiDOPeVQBeHv7n              | App\Http\Controllers\InstructorController@deleteInstructorClassTypeOption            | api                                                                                              |
|        | POST          | instructors/send-verification-email/{id}                                         | generated::kz2P5toSVjuijmAu              | App\Http\Controllers\InstructorController@sendVerificationEmail                      | api                                                                                              |
|        | PUT|PATCH     | instructors/{instructor}                                                         | instructors.update                       | App\Http\Controllers\InstructorController@update                                     | api                                                                                              |
|        | DELETE        | instructors/{instructor}                                                         | instructors.destroy                      | App\Http\Controllers\InstructorController@destroy                                    | api                                                                                              |
|        | POST          | locations                                                                        | locations.store                          | App\Http\Controllers\LocationController@store                                        | api                                                                                              |
|        | PUT|PATCH     | locations/{location}                                                             | locations.update                         | App\Http\Controllers\LocationController@update                                       | api                                                                                              |
|        | DELETE        | locations/{location}                                                             | locations.destroy                        | App\Http\Controllers\LocationController@destroy                                      | api                                                                                              |
|        | PUT           | me                                                                               | generated::5sp2t8b20kDgNuag              | App\Http\Controllers\UserController@updateProfile                                    | api                                                                                              |
|        | POST          | me/delete                                                                        | generated::KiuHQCNMTa3uA1dp              | App\Http\Controllers\UserController@deleteMyAccount                                  | api                                                                                              |
|        | POST          | media/signed/url                                                                 | generated::J4uwm35mff5bVq2W              | App\Http\Controllers\MediaController@getS3SignedUrl                                  | api                                                                                              |
|        | POST          | notification_templates                                                           | notification_templates.store             | App\Http\Controllers\NotificationTemplateController@store                            | api                                                                                              |
|        | PUT|PATCH     | notification_templates/{notification_template}                                   | notification_templates.update            | App\Http\Controllers\NotificationTemplateController@update                           | api                                                                                              |
|        | DELETE        | notification_templates/{notification_template}                                   | notification_templates.destroy           | App\Http\Controllers\NotificationTemplateController@destroy                          | api                                                                                              |
|        | POST          | offline/transaction                                                              | generated::mhsnusucU2kYSVHy              | App\Http\Controllers\WalletTransactionController@offlineStore                        | api                                                                                              |
|        | POST          | order-attributes                                                                 | order-attributes.store                   | App\Http\Controllers\OrderAttributeController@store                                  | api                                                                                              |
|        | PUT|PATCH     | order-attributes/{order_attribute}                                               | order-attributes.update                  | App\Http\Controllers\OrderAttributeController@update                                 | api                                                                                              |
|        | DELETE        | order-attributes/{order_attribute}                                               | order-attributes.destroy                 | App\Http\Controllers\OrderAttributeController@destroy                                | api                                                                                              |
|        | POST          | order-status                                                                     | order-status.store                       | App\Http\Controllers\OrderStatusController@store                                     | api                                                                                              |
|        | PUT|PATCH     | order-status/{order_status}                                                      | order-status.update                      | App\Http\Controllers\OrderStatusController@update                                    | api                                                                                              |
|        | DELETE        | order-status/{order_status}                                                      | order-status.destroy                     | App\Http\Controllers\OrderStatusController@destroy                                   | api                                                                                              |
|        | POST          | orders                                                                           | orders.store                             | App\Http\Controllers\OrderController@store                                           | api                                                                                              |
|        | POST          | orders/checkout                                                                  | generated::lnVXZpblw4wv4ywo              | App\Http\Controllers\OrderController@save                                            | api                                                                                              |
|        | POST          | orders/checkout/verify                                                           | generated::Qxu1WimjOeM8163H              | App\Http\Controllers\CheckoutController@verify                                       | api                                                                                              |
|        | POST          | orders/export                                                                    | generated::8NgQIZuoKvYu4brF              | App\Http\Controllers\OrderController@export                                          | api                                                                                              |
|        | POST          | orders/payment/callback/{gateway_code}/{action?}                                 | generated::PeGAjmEQEqbXrYcT              | App\Http\Controllers\OrderController@paymentCallback                                 | api                                                                                              |
|        | POST          | orders/refund/notify/{gateway_code}                                              | generated::fjVlJBmIwOyn8I1L              | App\Http\Controllers\CreditMemoController@refundNotify                               | api                                                                                              |
|        | POST          | orders/validate-discount/{checkout_type?}                                        | generated::nSM6itOItpHxzQyM              | App\Http\Controllers\OrderController@validateOrderDiscount                           | api                                                                                              |
|        | POST          | orders/verify                                                                    | generated::jT3bhXDja2H4WisE              | App\Http\Controllers\OrderController@verify                                          | api                                                                                              |
|        | PUT           | orders/{id}/attributes                                                           | generated::ec72lUaHNH64dDJY              | App\Http\Controllers\OrderController@updateOrderAttributes                           | api                                                                                              |
|        | POST          | orders/{id}/invoice                                                              | generated::PlPJmsOxxSDWzwEY              | App\Http\Controllers\OrderController@invoice                                         | api                                                                                              |
|        | POST          | orders/{id}/refund                                                               | generated::sUIScpZKaltjgdPA              | App\Http\Controllers\OrderController@refund                                          | api                                                                                              |
|        | POST          | orders/{id}/ship                                                                 | generated::C7K8WIkl5yjMplk5              | App\Http\Controllers\OrderController@ship                                            | api                                                                                              |
|        | PUT           | orders/{order_id}/products/{order_product_id}/sale-user-rates                    | generated::DB1yZIMI4eaIGVDR              | App\Http\Controllers\OrderController@updateOrderProductSaleUserRates                 | api                                                                                              |
|        | PUT|PATCH     | orders/{order}                                                                   | orders.update                            | App\Http\Controllers\OrderController@update                                          | api                                                                                              |
|        | DELETE        | orders/{order}                                                                   | orders.destroy                           | App\Http\Controllers\OrderController@destroy                                         | api                                                                                              |
|        | POST          | packages/checkout                                                                | generated::GrhdRm6HIMovG3u1              | App\Http\Controllers\OrderController@classPackageCheckout                            | api                                                                                              |
|        | POST          | packages/share                                                                   | generated::OTyUj2orNOPVRFzb              | App\Http\Controllers\UserPackageController@sharePackage                              | api                                                                                              |
|        | POST          | packages/{id}/notify                                                             | generated::QmpQI5IAArGBO9iV              | App\Http\Controllers\UserPackageController@notifyContractRenewal                     | api                                                                                              |
|        | POST          | packages/{id}/reauthorize                                                        | generated::q43wj95fZF1kBO9D              | App\Http\Controllers\UserPackageController@reauthorizeContractPayment                | api                                                                                              |
|        | POST          | packages/{id}/renewal                                                            | generated::8tACBoXCeypT6B5d              | App\Http\Controllers\UserPackageController@attemptContractRenewal                    | api                                                                                              |
|        | POST          | packages/{id}/terminate                                                          | generated::9gTsoVHlIjPC6DIR              | App\Http\Controllers\UserPackageController@terminateContract                         | api                                                                                              |
|        | PUT|PATCH     | packages/{package}                                                               | packages.update                          | App\Http\Controllers\UserPackageController@update                                    | api                                                                                              |
|        | POST          | payment_gateways                                                                 | payment_gateways.store                   | App\Http\Controllers\PaymentGatewayController@store                                  | api                                                                                              |
|        | PUT           | payment_gateways/password/{id}                                                   | generated::mddUT4s0MuDAyFdf              | App\Http\Controllers\PaymentGatewayController@updatePassword                         | api                                                                                              |
|        | DELETE        | payment_gateways/password/{id}                                                   | generated::MsLXLuFKsVOTLshf              | App\Http\Controllers\PaymentGatewayController@deletePassword                         | api                                                                                              |
|        | PUT           | payment_gateways/verify-password/{id}                                            | generated::Bod5oi7KyXfSUFrS              | App\Http\Controllers\PaymentGatewayController@verifyPassword                         | api                                                                                              |
|        | PUT|PATCH     | payment_gateways/{payment_gateway}                                               | payment_gateways.update                  | App\Http\Controllers\PaymentGatewayController@update                                 | api                                                                                              |
|        | DELETE        | payment_gateways/{payment_gateway}                                               | payment_gateways.destroy                 | App\Http\Controllers\PaymentGatewayController@destroy                                | api                                                                                              |
|        | POST          | payment_methods                                                                  | payment_methods.store                    | App\Http\Controllers\PaymentMethodController@store                                   | api                                                                                              |
|        | PUT|PATCH     | payment_methods/{payment_method}                                                 | payment_methods.update                   | App\Http\Controllers\PaymentMethodController@update                                  | api                                                                                              |
|        | DELETE        | payment_methods/{payment_method}                                                 | payment_methods.destroy                  | App\Http\Controllers\PaymentMethodController@destroy                                 | api                                                                                              |
|        | POST          | private-boards                                                                   | private-boards.store                     | App\Http\Controllers\PrivateBoardController@store                                    | api                                                                                              |
|        | POST          | private-boards/{id}/availability                                                 | generated::ST7aRqqNuXSt2EMc              | App\Http\Controllers\PrivateBoardController@createBoardAvailability                  | api                                                                                              |
|        | PUT           | private-boards/{id}/availability/{availability_id}                               | generated::qL8ODaoehXoefSEn              | App\Http\Controllers\PrivateBoardController@updateBoardAvailability                  | api                                                                                              |
|        | DELETE        | private-boards/{id}/availability/{availability_id}                               | generated::3SCZXEY6C4jMoCZ9              | App\Http\Controllers\PrivateBoardController@deleteBoardAvailability                  | api                                                                                              |
|        | POST          | private-boards/{id}/class-types                                                  | generated::yTggXobd7s4fUsou              | App\Http\Controllers\PrivateBoardController@addClassTypes                            | api                                                                                              |
|        | DELETE        | private-boards/{id}/class-types                                                  | generated::393EwADEAa7NXwFp              | App\Http\Controllers\PrivateBoardController@deleteClassTypes                         | api                                                                                              |
|        | POST          | private-boards/{id}/instructors                                                  | generated::L1KRRtva9KuWtNuf              | App\Http\Controllers\PrivateBoardController@addInstructors                           | api                                                                                              |
|        | DELETE        | private-boards/{id}/instructors                                                  | generated::zSES6kpVmklgOHQ1              | App\Http\Controllers\PrivateBoardController@deleteInstructors                        | api                                                                                              |
|        | POST          | private-boards/{id}/instructors/{board_instructor_id}/room-availability          | generated::kFgXhD8k4aotaGQx              | App\Http\Controllers\PrivateBoardController@addInstructorRoomsAvailability           | api                                                                                              |
|        | DELETE        | private-boards/{id}/instructors/{board_instructor_id}/room-availability          | generated::Fxqd7skFHbVJ3g8F              | App\Http\Controllers\PrivateBoardController@deleteInstructorRoomsAvailability        | api                                                                                              |
|        | POST          | private-boards/{id}/rooms                                                        | generated::7Bq8ma0T7Otl6OKS              | App\Http\Controllers\PrivateBoardController@addRooms                                 | api                                                                                              |
|        | DELETE        | private-boards/{id}/rooms                                                        | generated::d0oqZRlAQSFjwiBT              | App\Http\Controllers\PrivateBoardController@deleteRooms                              | api                                                                                              |
|        | POST          | private-boards/{id}/rooms/{board_room_id}/instructor-availability                | generated::M4IhdpMzeYlnxgCU              | App\Http\Controllers\PrivateBoardController@addRoomInstructorsAvailability           | api                                                                                              |
|        | DELETE        | private-boards/{id}/rooms/{board_room_id}/instructor-availability                | generated::H2PchzWz2nWraSB3              | App\Http\Controllers\PrivateBoardController@deleteRoomInstructorsAvailability        | api                                                                                              |
|        | PUT|PATCH     | private-boards/{private_board}                                                   | private-boards.update                    | App\Http\Controllers\PrivateBoardController@update                                   | api                                                                                              |
|        | DELETE        | private-boards/{private_board}                                                   | private-boards.destroy                   | App\Http\Controllers\PrivateBoardController@destroy                                  | api                                                                                              |
|        | POST          | private-class                                                                    | generated::w7qc96xOSt91PPtX              | App\Http\Controllers\ClassScheduleBookingController@customerSchedulePrivateClass     | api                                                                                              |
|        | POST          | private-class/step                                                               | generated::ytoRu8bCdARMo7dy              | App\Http\Controllers\ClassScheduleBookingController@customerStepSchedulePrivateClass | api                                                                                              |
|        | POST          | private-class/verify                                                             | generated::LAwer3jW1kC8US36              | App\Http\Controllers\ClassScheduleBookingController@customerVerifySchedulePrivate    | api                                                                                              |
|        | POST          | private-sale                                                                     | generated::OYfTEyQi6VE9AJXW              | App\Http\Controllers\ClassScheduleBookingController@customerScheduleSale             | api                                                                                              |
|        | POST          | private-sale/step                                                                | private-sale.generated::zsfkr90TIcLaDHgh | App\Http\Controllers\ClassScheduleBookingController@customerGetScheduleSaleStep      | api                                                                                              |
|        | POST          | private-sale/users                                                               | private-sale.generated::IG48kUxRGPGvgflq | App\Http\Controllers\ClassScheduleBookingController@customerGetInvitedUsers          | api                                                                                              |
|        | POST          | private-sale/verify                                                              | private-sale.                            | App\Http\Controllers\ClassScheduleBookingController@customerVerifyScheduleSale       | api                                                                                              |
|        | POST          | products                                                                         | products.store                           | App\Http\Controllers\ProductController@store                                         | api                                                                                              |
|        | POST          | products/export                                                                  | generated::VVlint3VLbebj3WF              | App\Http\Controllers\ProductController@export                                        | api                                                                                              |
|        | POST          | products/import                                                                  | generated::gGNUJP65KMM0n1S1              | App\Http\Controllers\ProductController@import                                        | api                                                                                              |
|        | POST          | products/preview                                                                 | generated::Q4tZBpNEfxy1slyJ              | App\Http\Controllers\ProductController@getPreviewKey                                 | api                                                                                              |
|        | PUT           | products/quick-update/{id}                                                       | generated::lVcRk8hGwSh6J1Uv              | App\Http\Controllers\ProductController@quickUpdate                                   | api                                                                                              |
|        | PUT|PATCH     | products/{product}                                                               | products.update                          | App\Http\Controllers\ProductController@update                                        | api                                                                                              |
|        | DELETE        | products/{product}                                                               | products.destroy                         | App\Http\Controllers\ProductController@destroy                                       | api                                                                                              |
|        | POST          | promotions                                                                       | promotions.store                         | App\Http\Controllers\PromotionController@store                                       | api                                                                                              |
|        | PUT|PATCH     | promotions/{promotion}                                                           | promotions.update                        | App\Http\Controllers\PromotionController@update                                      | api                                                                                              |
|        | DELETE        | promotions/{promotion}                                                           | promotions.destroy                       | App\Http\Controllers\PromotionController@destroy                                     | api                                                                                              |
|        | POST          | purchase-orders                                                                  | purchase-orders.store                    | App\Http\Controllers\PurchaseOrderController@store                                   | api                                                                                              |
|        | PUT           | purchase-orders/{id}/{status}                                                    | generated::BogYw4E8XwhIeiG9              | App\Http\Controllers\PurchaseOrderController@changePoStatus                          | api                                                                                              |
|        | PUT|PATCH     | purchase-orders/{purchase_order}                                                 | purchase-orders.update                   | App\Http\Controllers\PurchaseOrderController@update                                  | api                                                                                              |
|        | DELETE        | purchase-orders/{purchase_order}                                                 | purchase-orders.destroy                  | App\Http\Controllers\PurchaseOrderController@destroy                                 | api                                                                                              |
|        | POST          | refunds                                                                          | refunds.store                            | App\Http\Controllers\RefundController@store                                          | api                                                                                              |
|        | PUT|PATCH     | refunds/{refund}                                                                 | refunds.update                           | App\Http\Controllers\RefundController@update                                         | api                                                                                              |
|        | POST          | register                                                                         | generated::USmmt5S3oi77ezbn              | App\Http\Controllers\UserController@register                                         | api                                                                                              |
|        | POST          | request/otp                                                                      | generated::Xk8mFtjd8zUEF23O              | App\Http\Controllers\UserController@sendPhoneVerification                            | api                                                                                              |
|        | POST          | reset-password                                                                   | generated::QcXAkyoK9Yi0gK7U              | App\Http\Controllers\UserController@resetPassword                                    | api                                                                                              |
|        | POST          | roles                                                                            | roles.store                              | App\Http\Controllers\RoleController@store                                            | api                                                                                              |
|        | PUT|PATCH     | roles/{role}                                                                     | roles.update                             | App\Http\Controllers\RoleController@update                                           | api                                                                                              |
|        | DELETE        | roles/{role}                                                                     | roles.destroy                            | App\Http\Controllers\RoleController@destroy                                          | api                                                                                              |
|        | POST          | rooms                                                                            | rooms.store                              | App\Http\Controllers\RoomController@store                                            | api                                                                                              |
|        | POST          | rooms/operation-hours                                                            | generated::4ZrSfk8O15oeV465              | App\Http\Controllers\PrivateBoardRoomController@getRoomOperationHours                | api                                                                                              |
|        | PUT|PATCH     | rooms/{room}                                                                     | rooms.update                             | App\Http\Controllers\RoomController@update                                           | api                                                                                              |
|        | DELETE        | rooms/{room}                                                                     | rooms.destroy                            | App\Http\Controllers\RoomController@destroy                                          | api                                                                                              |
|        | POST          | schedule-bookings                                                                | schedule-bookings.store                  | App\Http\Controllers\ClassScheduleBookingController@store                            | api                                                                                              |
|        | POST          | schedule-bookings/{id}/send-confirm                                              | generated::dbxP9Peq5Kk65Mdg              | App\Http\Controllers\ClassScheduleBookingController@sendBookingConfirmationEmail     | api                                                                                              |
|        | PUT           | schedule-bookings/{id}/status                                                    | generated::bOTrvMAW1OVQ4UkC              | App\Http\Controllers\ClassScheduleBookingController@changeStatus                     | api                                                                                              |
|        | POST          | schedules                                                                        | schedules.store                          | App\Http\Controllers\ClassScheduleController@store                                   | api                                                                                              |
|        | POST          | schedules-group-sale                                                             | group-schedule-sale                      | App\Http\Controllers\ClassScheduleController@storeClassScheduleGroupSale             | api                                                                                              |
|        | POST          | schedules-group-sale/verify                                                      | group-schedule-sale.verify               | App\Http\Controllers\ClassScheduleController@verifyClassScheduleGroupSale            | api                                                                                              |
|        | POST          | schedules-sale                                                                   | schedule-sale                            | App\Http\Controllers\ClassScheduleController@storeClassScheduleSale                  | api                                                                                              |
|        | POST          | schedules-sale/verify                                                            | schedule-sale.verify                     | App\Http\Controllers\ClassScheduleController@verifyClassScheduleSale                 | api                                                                                              |
|        | PUT           | schedules-settings                                                               | generated::ud5hUwLSXork37KS              | App\Http\Controllers\ClassScheduleSettingController@updateDefaultSettings            | api                                                                                              |
|        | PUT           | schedules/attendance/{token}                                                     | generated::mKomYoPyrLJkXMUc              | App\Http\Controllers\ClassScheduleController@attendanceByCachedToken                 | api                                                                                              |
|        | POST          | schedules/notify                                                                 | generated::LsCqEkgIh5g49EZN              | App\Http\Controllers\ClassScheduleController@notify                                  | api                                                                                              |
|        | PUT           | schedules/publish-all                                                            | generated::qXf9R61WNVe4iPHi              | App\Http\Controllers\ClassScheduleController@publishAll                              | api                                                                                              |
|        | PUT           | schedules/qrcode-expire/{token}                                                  | generated::zRUYAs4kJTcZKICb              | App\Http\Controllers\ClassScheduleController@expireClassQrCode                       | api                                                                                              |
|        | PUT           | schedules/{id}/cancel                                                            | generated::w9dwyFhbDdwlhcrp              | App\Http\Controllers\ClassScheduleController@cancel                                  | api                                                                                              |
|        | POST          | schedules/{id}/cancel-service                                                    | generated::shhJnYPfvxRYJfpr              | App\Http\Controllers\ClassScheduleController@cancelService                           | api                                                                                              |
|        | PUT           | schedules/{id}/hidden                                                            | generated::kBh3BvFnaGLcanxe              | App\Http\Controllers\ClassScheduleController@toggleHidden                            | api                                                                                              |
|        | PUT           | schedules/{id}/quick-update                                                      | generated::9GzX0U6SaJiaPeTx              | App\Http\Controllers\ClassScheduleController@quickUpdate                             | api                                                                                              |
|        | PUT           | schedules/{id}/status                                                            | generated::BoXKPjJexBQYp4xG              | App\Http\Controllers\ClassScheduleController@changeStatus                            | api                                                                                              |
|        | POST          | schedules/{id}/validate-quick-update                                             | generated::9rKg1N3iftgHjFyi              | App\Http\Controllers\ClassScheduleController@validateQuickUpdate                     | api                                                                                              |
|        | PUT|PATCH     | schedules/{schedule}                                                             | schedules.update                         | App\Http\Controllers\ClassScheduleController@update                                  | api                                                                                              |
|        | DELETE        | schedules/{schedule}                                                             | schedules.destroy                        | App\Http\Controllers\ClassScheduleController@destroy                                 | api                                                                                              |
|        | POST          | search_terms/export                                                              | generated::429glaVFAVP2z6C6              | App\Http\Controllers\SearchTermController@export                                     | api                                                                                              |
|        | PUT|PATCH     | search_terms/{search_term}                                                       | search_terms.update                      | App\Http\Controllers\SearchTermController@update                                     | api                                                                                              |
|        | POST          | send-account-verify                                                              | generated::f0KsGuPqjvV7hgiA              | App\Http\Controllers\UserController@sendAccountEmailVerification                     | api                                                                                              |
|        | POST          | send-email-verify                                                                | generated::q5KPE5SLSASWpRge              | App\Http\Controllers\UserController@sendEmailVerification                            | api                                                                                              |
|        | POST          | series                                                                           | series.store                             | App\Http\Controllers\SeriesController@store                                          | api                                                                                              |
|        | POST          | series-types                                                                     | series-types.store                       | App\Http\Controllers\SeriesTypeController@store                                      | api                                                                                              |
|        | PUT           | series-types-order                                                               | generated::36xmzPV4uG1mfXkr              | App\Http\Controllers\SeriesTypeController@updateOrdering                             | api                                                                                              |
|        | PUT|PATCH     | series-types/{series_type}                                                       | series-types.update                      | App\Http\Controllers\SeriesTypeController@update                                     | api                                                                                              |
|        | DELETE        | series-types/{series_type}                                                       | series-types.destroy                     | App\Http\Controllers\SeriesTypeController@destroy                                    | api                                                                                              |
|        | PUT|PATCH     | series/{series}                                                                  | series.update                            | App\Http\Controllers\SeriesController@update                                         | api                                                                                              |
|        | DELETE        | series/{series}                                                                  | series.destroy                           | App\Http\Controllers\SeriesController@destroy                                        | api                                                                                              |
|        | POST          | settings                                                                         | settings.store                           | App\Http\Controllers\SettingsController@store                                        | api                                                                                              |
|        | POST          | shippings                                                                        | shippings.store                          | App\Http\Controllers\ShippingController@store                                        | api                                                                                              |
|        | PUT|PATCH     | shippings/{shipping}                                                             | shippings.update                         | App\Http\Controllers\ShippingController@update                                       | api                                                                                              |
|        | DELETE        | shippings/{shipping}                                                             | shippings.destroy                        | App\Http\Controllers\ShippingController@destroy                                      | api                                                                                              |
|        | PUT           | sort-static-block                                                                | generated::vZnxw0fw5JqNOS0J              | App\Http\Controllers\StaticBlockController@updateStaticBlockSorting                  | api                                                                                              |
|        | POST          | static_blocks                                                                    | static_blocks.store                      | App\Http\Controllers\StaticBlockController@store                                     | api                                                                                              |
|        | PUT|PATCH     | static_blocks/{static_block}                                                     | static_blocks.update                     | App\Http\Controllers\StaticBlockController@update                                    | api                                                                                              |
|        | DELETE        | static_blocks/{static_block}                                                     | static_blocks.destroy                    | App\Http\Controllers\StaticBlockController@destroy                                   | api                                                                                              |
|        | POST          | stock-requests                                                                   | stock-requests.store                     | App\Http\Controllers\StockTransferRequestController@store                            | api                                                                                              |
|        | PUT|PATCH     | stock-requests/{stock_request}                                                   | stock-requests.update                    | App\Http\Controllers\StockTransferRequestController@update                           | api                                                                                              |
|        | DELETE        | stock-requests/{stock_request}                                                   | stock-requests.destroy                   | App\Http\Controllers\StockTransferRequestController@destroy                          | api                                                                                              |
|        | POST          | stock-transfers                                                                  | stock-transfers.store                    | App\Http\Controllers\StockTransferController@store                                   | api                                                                                              |
|        | PUT           | stock-transfers/{id}/{status}                                                    | generated::vTuJaZtad3sKNmny              | App\Http\Controllers\StockTransferController@changeStatus                            | api                                                                                              |
|        | PUT|PATCH     | stock-transfers/{stock_transfer}                                                 | stock-transfers.update                   | App\Http\Controllers\StockTransferController@update                                  | api                                                                                              |
|        | DELETE        | stock-transfers/{stock_transfer}                                                 | stock-transfers.destroy                  | App\Http\Controllers\StockTransferController@destroy                                 | api                                                                                              |
|        | POST          | studios                                                                          | studios.store                            | App\Http\Controllers\StudioController@store                                          | api                                                                                              |
|        | PUT|PATCH     | studios/{studio}                                                                 | studios.update                           | App\Http\Controllers\StudioController@update                                         | api                                                                                              |
|        | DELETE        | studios/{studio}                                                                 | studios.destroy                          | App\Http\Controllers\StudioController@destroy                                        | api                                                                                              |
|        | POST          | suppliers                                                                        | suppliers.store                          | App\Http\Controllers\SupplierController@store                                        | api                                                                                              |
|        | PUT|PATCH     | suppliers/{supplier}                                                             | suppliers.update                         | App\Http\Controllers\SupplierController@update                                       | api                                                                                              |
|        | DELETE        | suppliers/{supplier}                                                             | suppliers.destroy                        | App\Http\Controllers\SupplierController@destroy                                      | api                                                                                              |
|        | POST          | tags                                                                             | tags.store                               | Marvel\Http\Controllers\TagController@store                                          | api                                                                                              |
|        | PUT|PATCH     | tags/{tag}                                                                       | tags.update                              | Marvel\Http\Controllers\TagController@update                                         | api                                                                                              |
|        | DELETE        | tags/{tag}                                                                       | tags.destroy                             | Marvel\Http\Controllers\TagController@destroy                                        | api                                                                                              |
|        | POST          | taxes                                                                            | taxes.store                              | Marvel\Http\Controllers\TaxController@store                                          | api                                                                                              |
|        | PUT|PATCH     | taxes/{tax}                                                                      | taxes.update                             | Marvel\Http\Controllers\TaxController@update                                         | api                                                                                              |
|        | DELETE        | taxes/{tax}                                                                      | taxes.destroy                            | Marvel\Http\Controllers\TaxController@destroy                                        | api                                                                                              |
|        | POST          | token                                                                            | generated::sZXr9kkHJpdeM2hO              | App\Http\Controllers\UserController@token                                            | api                                                                                              |
|        | PUT           | update-brand-category                                                            | generated::ZBnY6hKaaZGsicdK              | App\Http\Controllers\CategoryController@updateBrand                                  | api                                                                                              |
|        | PUT           | update-nav-category                                                              | generated::2VH1qsYYOYRAT4jQ              | App\Http\Controllers\CategoryController@updateNavigation                             | api                                                                                              |
|        | POST          | users                                                                            | users.store                              | App\Http\Controllers\UserController@store                                            | api                                                                                              |
|        | POST          | users/block-booking                                                              | generated::N86A4oxzc3xb2kUr              | App\Http\Controllers\UserController@editBookingBlockUser                             | api                                                                                              |
|        | POST          | users/block-user                                                                 | generated::xAMg3IRehCAdhJcF              | App\Http\Controllers\UserController@banUser                                          | api                                                                                              |
|        | POST          | users/delete/{id}                                                                | generated::fit1Gp3NxsQhkhAv              | App\Http\Controllers\UserController@destroy                                          | api                                                                                              |
|        | POST          | users/export                                                                     | generated::BjRPBraWpOmCPMey              | App\Http\Controllers\UserController@export                                           | api                                                                                              |
|        | POST          | users/import                                                                     | generated::DCd66KlxUYhGphsy              | App\Http\Controllers\UserController@import                                           | api                                                                                              |
|        | POST          | users/merge-account                                                              | generated::3L4GgRHuRsDumKeR              | App\Http\Controllers\UserController@mergeAccounts                                    | api                                                                                              |
|        | POST          | users/unblock-user                                                               | generated::sHONxvohHIPVXlnW              | App\Http\Controllers\UserController@activeUser                                       | api                                                                                              |
|        | PUT           | users/{id}                                                                       | generated::XbtvC2l8C1YuDgl1              | App\Http\Controllers\UserController@update                                           | api                                                                                              |
|        | POST          | users/{id}/notes                                                                 | generated::Bl5fxL42Jg3a87mn              | App\Http\Controllers\UserController@addUserNote                                      | api                                                                                              |
|        | PUT           | users/{id}/notes/{note_id}                                                       | generated::WNQmVJNmH98iFOZL              | App\Http\Controllers\UserController@editUserNote                                     | api                                                                                              |
|        | DELETE        | users/{id}/notes/{note_id}                                                       | generated::qzx5l8r7CSjyAIU2              | App\Http\Controllers\UserController@deleteUserNote                                   | api                                                                                              |
|        | PUT|PATCH     | users/{user}                                                                     | users.update                             | App\Http\Controllers\UserController@update                                           | api                                                                                              |
|        | POST          | vapor/signed-storage-url                                                         | generated::507mRBbcSvEtnFnn              | Laravel\Vapor\Contracts\SignedStorageUrlController@store                             | web                                                                                              |
|        | POST          | verify-account-merge                                                             | generated::GL7Dfx9PZClxB70k              | App\Http\Controllers\UserController@verifyAndMergeAccount                            | api                                                                                              |
|        | POST          | verify-email                                                                     | generated::TT4uWclh9DQrjuNZ              | App\Http\Controllers\UserController@verifyEmail                                      | api                                                                                              |
|        | POST          | verify-forget-password-token                                                     | generated::B6QxO9MzEBDCuqIk              | App\Http\Controllers\UserController@verifyForgetPasswordToken                        | api                                                                                              |
|        | POST          | verify-otp                                                                       | generated::8IodbQf6HFvLYNYu              | App\Http\Controllers\UserController@verifyPhone                                      | api                                                                                              |
|        | POST          | wallet/groups                                                                    | groups.store                             | App\Http\Controllers\WalletGroupController@store                                     | api                                                                                              |
|        | PUT|PATCH     | wallet/groups/{group}                                                            | groups.update                            | App\Http\Controllers\WalletGroupController@update                                    | api                                                                                              |
|        | POST          | wallet/groups/{wallet_group_id}/rules                                            | rules.store                              | App\Http\Controllers\WalletRuleController@store                                      | api                                                                                              |
|        | PUT|PATCH     | wallet/groups/{wallet_group_id}/rules/{rule}                                     | rules.update                             | App\Http\Controllers\WalletRuleController@update                                     | api                                                                                              |
|        | DELETE        | wallet/groups/{wallet_group_id}/rules/{rule}                                     | rules.destroy                            | App\Http\Controllers\WalletRuleController@destroy                                    | api                                                                                              |
|        | POST          | wallet_transactions                                                              | generated::mOoDxltuol9Vng64              | App\Http\Controllers\WalletTransactionController@store                               | api                                                                                              |
|        | POST          | wallet_transactions/export                                                       | generated::ljBqazCK8tXSS9s8              | App\Http\Controllers\WalletTransactionController@export                              | api                                                                                              |
|        | POST          | webhook/order/shipment/update                                                    | generated::svjzqd4JaaDIk3R9              | App\Http\Controllers\WebhookController@shipmentUpdate                                | api                                                                                              |
|        | PUT           | wishlist/{id?}                                                                   | generated::vUUnpqmfSMNAnuIk              | App\Http\Controllers\WishlistController@update                                       | api                                                                                              |
