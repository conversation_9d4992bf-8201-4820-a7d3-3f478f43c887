<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// CREATE TABLE `users` (
//   `id` bigint unsigned NOT NULL AUTO_INCREMENT,
//   `is_guest` tinyint(1) NOT NULL DEFAULT '0',
//   `ref_id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `customer_group_id` bigint unsigned DEFAULT NULL,
//   `commission_group_id` bigint unsigned DEFAULT NULL,
//   `firstname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
//   `lastname` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
//   `email` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `email_verified_at` timestamp NULL DEFAULT NULL,
//   `phone` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `country_code` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `phone_verified_at` timestamp NULL DEFAULT NULL,
//   `gender` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `dob` datetime DEFAULT NULL,
//   `dob_updated_at` datetime DEFAULT NULL,
//   `password` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `verification_token` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `created_at` timestamp NULL DEFAULT NULL,
//   `updated_at` timestamp NULL DEFAULT NULL,
//   `is_active` tinyint(1) NOT NULL DEFAULT '1',
//   `is_book_blocked` tinyint(1) NOT NULL DEFAULT '0',
//   `is_new` tinyint(1) NOT NULL DEFAULT '1',
//   `shop_id` bigint unsigned DEFAULT NULL,
//   `deleted_at` timestamp NULL DEFAULT NULL,
//   PRIMARY KEY (`id`),
//   KEY `users_shop_id_foreign` (`shop_id`),
//   KEY `users_commission_group_id_foreign` (`commission_group_id`),
//   CONSTRAINT `users_commission_group_id_foreign` FOREIGN KEY (`commission_group_id`) REFERENCES `commission_groups` (`id`),
//   CONSTRAINT `users_shop_id_foreign` FOREIGN KEY (`shop_id`) REFERENCES `shops` (`id`) ON DELETE CASCADE
// ) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->boolean('is_guest')->default(false);
            $table->string('ref_id')->nullable();
            $table->unsignedBigInteger('customer_group_id')->nullable();
            $table->unsignedBigInteger('commission_group_id')->nullable();
            $table->string('firstname', 255);
            $table->string('lastname');
            $table->string('email')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone')->nullable();
            $table->string('country_code', 4)->nullable();
            $table->timestamp('phone_verified_at')->nullable();
            $table->string('gender')->nullable();
            $table->date('dob')->nullable();
            $table->dateTime('dob_updated_at')->nullable();
            $table->string('password')->nullable();
            $table->string('verification_token')->nullable();
            $table->rememberToken();
            $table->timestamps();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_book_blocked')->default(false);
            $table->boolean('is_new')->default(true);
            $table->unsignedBigInteger('shop_id')->nullable();
            $table->softDeletes();

            $table->index(['email', 'phone', 'firstname', 'lastname']);
            // TODO
            // $table->foreign('shop_id')->references('id')->on('shops')->onDelete('cascade');
            // $table->foreign('commission_group_id')->references('id')->on('commission_groups');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
};
