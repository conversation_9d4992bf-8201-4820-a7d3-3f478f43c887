<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_product', function (Blueprint $table) {
            $table->id();

            // Core relationships
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('product_id')->nullable();
            $table->unsignedBigInteger('inventory_id')->nullable();
            $table->unsignedBigInteger('product_variant_id')->nullable();

            // Product information (snapshot at time of order)
            $table->string('name')->nullable();
            $table->string('sku')->nullable();
            $table->string('barcode')->nullable();

            // Physical dimensions
            $table->decimal('width', 8, 2)->nullable();
            $table->decimal('height', 8, 2)->nullable();
            $table->decimal('length', 8, 2)->nullable();
            $table->decimal('weight', 8, 2)->nullable();

            // Media
            $table->string('image')->nullable();
            $table->string('banner')->nullable();

            // Quantity tracking
            $table->unsignedInteger('order_quantity');
            $table->unsignedInteger('invoiced_quantity')->default(0);
            $table->unsignedInteger('shipped_quantity')->default(0);
            $table->unsignedInteger('canceled_quantity')->default(0);
            $table->unsignedInteger('refunded_quantity')->default(0);

            // Pricing and financial
            $table->double('unit_price');
            $table->unsignedDouble('upfront_amount')->default(0);

            // Discount information
            $table->string('discount_rate_type', 20)->default('percentage');
            $table->unsignedDouble('discount_rate')->default(0);
            $table->unsignedDouble('discount')->default(0);

            // Credits
            $table->unsignedDouble('store_credit')->default(0);
            $table->unsignedDouble('gift_card_credit')->default(0);

            // Totals
            $table->double('subtotal');
            $table->double('tax', 10, 2)->default(0.00);

            // Flags
            $table->boolean('is_commissioned')->default(false);
            $table->boolean('is_deposit')->default(false);

            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('set null');
            $table->foreign('product_variant_id')->references('id')->on('product_variants')->onDelete('set null');

            // Indexes for performance
            $table->index(['order_id', 'product_id']);
            $table->index('sku');
            $table->index('barcode');
            $table->index(['is_commissioned', 'order_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_product');
    }
};
