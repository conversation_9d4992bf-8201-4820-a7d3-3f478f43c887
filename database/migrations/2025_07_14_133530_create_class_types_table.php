<?php

use App\Enums\ClassTypePriceType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('class_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedSmallInteger('duration_in_minutes');
            $table->unsignedSmallInteger('class_count')->default(1);
            $table->string('price_type', 20)->default(ClassTypePriceType::FIXED);
            $table->decimal('price', 10, 2)->nullable();
            $table->unsignedBigInteger('tax_class_id')->nullable();
            $table->char('colour', 7);
            $table->boolean('is_addon')->default(false);
            $table->boolean('is_bookable')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();


            $table->index(['is_active', 'is_bookable']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('class_types');
    }
};
