<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instructors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('bio')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('designation_id')->nullable();
            $table->unsignedTinyInteger('priority')->nullable();
            $table->boolean('active')->default(true);
            $table->unsignedInteger('order_column')->nullable();
            $table->timestamps();

            $table->foreign('designation_id')->references('id')->on('designations');
            $table->foreign('user_id')->references('id')->on('users');

            $table->index(['name', 'user_id', 'designation_id', 'active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instructors');
    }
};
