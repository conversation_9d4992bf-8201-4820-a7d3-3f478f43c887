<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('price');
            $table->string('sale_price')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('sku')->nullable();
            $table->string('barcode')->nullable();
            $table->decimal('width', 10, 1)->nullable();
            $table->decimal('height', 10, 1)->nullable();
            $table->decimal('length', 10, 1)->nullable();
            $table->decimal('weight', 10, 3)->nullable();
            $table->json('options')->nullable();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
