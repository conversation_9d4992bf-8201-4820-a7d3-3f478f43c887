<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// CREATE TABLE `user_profiles` (
//   `id` bigint unsigned NOT NULL AUTO_INCREMENT,
//   `avatar` json DEFAULT NULL,
//   `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
//   `socials` json DEFAULT NULL,
//   `race` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `contact` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `emergency_contact` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `emergency_phone` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `street_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
//   `city` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `zip` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
//   `state_id` bigint unsigned DEFAULT NULL,
//   `studio_id` bigint unsigned DEFAULT NULL,
//   `branch_id` bigint unsigned DEFAULT NULL,
//   `customer_id` bigint unsigned NOT NULL,
//   `created_at` timestamp NULL DEFAULT NULL,
//   `updated_at` timestamp NULL DEFAULT NULL,
//   PRIMARY KEY (`id`),
//   KEY `user_profiles_customer_id_foreign` (`customer_id`),
//   KEY `user_profiles_branch_id_foreign` (`branch_id`),
//   CONSTRAINT `user_profiles_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branch` (`id`),
//   CONSTRAINT `user_profiles_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`)
// ) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id();
            $table->text('bio')->nullable();
            $table->json('socials')->nullable();
            $table->string('race', 20)->nullable();
            $table->string('contact')->nullable();
            $table->string('emergency_contact')->nullable();
            $table->string('emergency_phone')->nullable();
            $table->text('street_address')->nullable();
            $table->string('city')->nullable();
            $table->string('zip')->nullable();
            $table->unsignedBigInteger('state_id')->nullable();
            $table->unsignedBigInteger('studio_id')->nullable();
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('customer_id');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('customer_id')->references('id')->on('users');
            // TODO
            // $table->foreign('branch_id')->references('id')->on('branch');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_profiles');
    }
};
