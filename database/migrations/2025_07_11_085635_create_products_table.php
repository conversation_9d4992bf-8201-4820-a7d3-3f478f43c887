<?php

use App\Enums\ProductType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;


return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('productable_id')->nullable();
            $table->string('productable_type')->nullable();
            $table->string('type')->default(ProductType::DEFAULT);
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->decimal('sale_price', 8, 2)->nullable();
            $table->string('sku')->nullable();
            $table->string('barcode')->nullable();
            $table->boolean('is_taxable')->default(false);
            $table->unsignedBigInteger('tax_class_id')->nullable();
            $table->unsignedBigInteger('shipping_class_id')->nullable();
            $table->json('meta')->nullable();
            $table->string('status')->default('publish');
            $table->boolean('is_bundle')->default(false);
            $table->boolean('is_require_double_scanning')->default(false);
            $table->string('unit')->nullable();
            $table->decimal('height', 10, 1)->nullable();
            $table->decimal('width', 10, 1)->nullable();
            $table->decimal('length', 10, 1)->nullable();
            $table->decimal('weight', 10, 3)->nullable();
            $table->json('variant_attributes')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
