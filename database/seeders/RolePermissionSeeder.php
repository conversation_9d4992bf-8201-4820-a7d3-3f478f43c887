<?php

namespace Database\Seeders;

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class RolePermissionSeeder extends Seeder
{
    /**
     * The command instance for output
     *
     * @var \Illuminate\Console\Command|null
     */
    protected $command;

    /**
     * Set the command instance for output
     *
     * @param \Illuminate\Console\Command $command
     * @return void
     */
    public function setCommand($command): void
    {
        $this->command = $command;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $this->info('🔐 Seeding roles and permissions...');
        // Clear cached permissions
        app(PermissionRegistrar::class)->forgetCachedPermissions();


        // Create roles
        $this->createRoles();

        // Create permissions
        $this->createPermissions();

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        // Create mock super admin user
        $this->createMockSuperAdminUser();

        $this->info('✅ Roles and permissions seeded successfully!');
    }

    /**
     * Output info message (works with both command and seeder contexts)
     *
     * @param string $message
     * @return void
     */
    protected function info($message): void
    {
        if ($this->command) {
            $this->command->info($message);
        }
        else {
            echo $message . PHP_EOL;
        }
    }

    /**
     * Output warning message (works with both command and seeder contexts)
     *
     * @param string $message
     * @return void
     */
    protected function warn($message): void
    {
        if ($this->command) {
            $this->command->warn($message);
        }
        else {
            echo "WARNING: " . $message . PHP_EOL;
        }
    }

    /**
     * Output error message (works with both command and seeder contexts)
     *
     * @param string $message
     * @return void
     */
    protected function error($message): void
    {
        if ($this->command) {
            $this->command->error($message);
        }
        else {
            echo "ERROR: " . $message . PHP_EOL;
        }
    }

    /**
     * Create all roles
     *
     * @return void
     */
    protected function createRoles(): void
    {
        foreach (RoleEnum::getValues() as $role) {
            Role::firstOrCreate([
                'name' => $role,
                'guard_name' => 'api'
            ]);
        }

        $this->info('📋 Roles created successfully.');
    }

    /**
     * Create all permissions
     *
     * @return void
     */
    protected function createPermissions(): void
    {
        foreach (PermissionEnum::getValues() as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        $this->info('🔑 Permissions created successfully.');
    }

    /**
     * Assign permissions to roles
     *
     * @return void
     */
    protected function assignPermissionsToRoles()
    {
        $roles = [
            [
                'role' => Role::findByName(RoleEnum::STAFF, 'api'),
                'permissions' => $this->getStaffPermissions()
            ],
            [
                'role' => Role::findByName(RoleEnum::INSTRUCTOR, 'api'),
                'permissions' => $this->getInstructorPermissions()
            ],
            [
                'role' => Role::findByName(RoleEnum::OBSERVER, 'api'),
                'permissions' => $this->getObserverPermissions()
            ],
            [
                'role' => Role::findByName(RoleEnum::TREASURY, 'api'),
                'permissions' => $this->getTreasuryPermissions()
            ],
        ];

        foreach ($roles as $roleData) {
            if ($roleData['role']) {
                foreach ($roleData['permissions'] as $permission) {
                    try {
                        $roleData['role']->givePermissionTo($permission);
                    } catch (\Exception $e) {
                        $this->error("Failed to assign permission {$permission} to role {$roleData['role']->name}: " . $e->getMessage());
                    }
                }
            }
        }

        $this->info('🎯 Permissions assigned to roles successfully.');
    }

    /**
     * Create mock super admin user for development/testing
     *
     * @return void
     */
    protected function createMockSuperAdminUser(): void
    {
        $mockAdminData = [
            'firstname' => 'Super',
            'lastname' => 'Admin',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'is_active' => true,
            'email_verified_at' => now(),
        ];

        // Check if super admin already exists
        $existingAdmin = User::where('email', $mockAdminData['email'])->first();

        if ($existingAdmin) {
            $this->warn('⚠️  Super admin user already exists: ' . $mockAdminData['email']);
            return;
        }

        $user = User::create($mockAdminData);
        $user->assignRole(RoleEnum::SUPER_ADMIN);

        $this->info('👤 Mock super admin user created:');
        $this->info('   Email: ' . $mockAdminData['email']);
        $this->info('   Password: password123');
        $this->warn('⚠️  Please change the password in production!');
    }

    /**
     * Get staff permissions
     *
     * @return array
     */
    protected function getStaffPermissions(): array
    {
        return [
            PermissionEnum::VIEW_BRANCH,
            PermissionEnum::EDIT_BRANCH,
            PermissionEnum::DELETE_BRANCH,
            PermissionEnum::VIEW_PRODUCT,
            PermissionEnum::EDIT_PRODUCT,
            PermissionEnum::DELETE_PRODUCT,
            PermissionEnum::VIEW_WALLET_GROUP,
            PermissionEnum::EDIT_WALLET_GROUP,
            PermissionEnum::VIEW_WALLET_RULE,
            PermissionEnum::EDIT_WALLET_RULE,
            PermissionEnum::DELETE_WALLET_RULE,
            PermissionEnum::VIEW_WALLET,
            PermissionEnum::VIEW_WALLET_TRANSACTION,
            PermissionEnum::VIEW_ADDRESS,
            PermissionEnum::VIEW_DASHBOARD,
            PermissionEnum::EDIT_BANNER,
            PermissionEnum::DELETE_BANNER,
            PermissionEnum::SEARCH_BANNER,
            PermissionEnum::VIEW_DISCOUNT,
            PermissionEnum::EDIT_DISCOUNT,
            PermissionEnum::DELETE_DISCOUNT,
            PermissionEnum::EDIT_CUSTOMER_GROUP,
            PermissionEnum::EDIT_CATEGORY,
            PermissionEnum::DELETE_CATEGORY,
            PermissionEnum::VIEW_ORDER,
            PermissionEnum::EDIT_ORDER,
            PermissionEnum::DELETE_ORDER,
            PermissionEnum::EDIT_ORDER_STATUS,
            PermissionEnum::EDIT_FORMS,
            PermissionEnum::VIEW_SEARCH_TERM,
            PermissionEnum::EDIT_SEARCH_TERM,
            PermissionEnum::VIEW_LOCATION,
            PermissionEnum::EDIT_LOCATION,
            PermissionEnum::DELETE_LOCATION,
            PermissionEnum::EDIT_NOTIFICATION_TEMPLATE,
            PermissionEnum::EDIT_PAYMENT_GATEWAY,
            PermissionEnum::EDIT_PAYMENT_METHOD,
            PermissionEnum::EDIT_SETTING,
            PermissionEnum::EDIT_SHIPPING,
            PermissionEnum::EDIT_STATIC_BLOCK,
            PermissionEnum::EDIT_TAG,
            PermissionEnum::EDIT_TAX,
            PermissionEnum::VIEW_USER,
            PermissionEnum::VIEW_CUSTOMER,
            PermissionEnum::EDIT_CUSTOMER,
            PermissionEnum::EDIT_CUSTOMER_PASSWORD,
            PermissionEnum::EDIT_BLOCKED_DOMAIN,
            PermissionEnum::EDIT_STUDIO,
            PermissionEnum::EDIT_ROOM,
            PermissionEnum::EDIT_INSTRUCTOR,
            PermissionEnum::EDIT_CLASS_TYPE,
            PermissionEnum::EDIT_SERIES_TYPE,
            PermissionEnum::EDIT_SERIES,
            PermissionEnum::EDIT_CONTRACTS,
            PermissionEnum::EDIT_SCHEDULE_SETTINGS,
            PermissionEnum::EDIT_SCHEDULES,
            PermissionEnum::EDIT_SCHEDULE_BOOKINGS,
            PermissionEnum::EDIT_GIFT_CARD_TEMPLATES,
            PermissionEnum::EDIT_GIFT_CARDS,
            PermissionEnum::EDIT_PRIVATE_BOARDS,
            PermissionEnum::VIEW_REPORT,
            PermissionEnum::EXPORT_CUSTOMER,
        ];
    }

    /**
     * Get instructor permissions
     *
     * @return array
     */
    protected function getInstructorPermissions(): array
    {
        return [
            PermissionEnum::VIEW_BRANCH,
            PermissionEnum::EDIT_BRANCH,
            PermissionEnum::DELETE_BRANCH,
            PermissionEnum::VIEW_PRODUCT,
            PermissionEnum::EDIT_PRODUCT,
            PermissionEnum::DELETE_PRODUCT,
            PermissionEnum::VIEW_WALLET_GROUP,
            PermissionEnum::EDIT_WALLET_GROUP,
            PermissionEnum::VIEW_WALLET_RULE,
            PermissionEnum::EDIT_WALLET_RULE,
            PermissionEnum::DELETE_WALLET_RULE,
            PermissionEnum::VIEW_WALLET,
            PermissionEnum::VIEW_WALLET_TRANSACTION,
            PermissionEnum::VIEW_ADDRESS,
            PermissionEnum::VIEW_DASHBOARD,
            PermissionEnum::EDIT_BANNER,
            PermissionEnum::DELETE_BANNER,
            PermissionEnum::SEARCH_BANNER,
            PermissionEnum::VIEW_DISCOUNT,
            PermissionEnum::EDIT_DISCOUNT,
            PermissionEnum::DELETE_DISCOUNT,
            PermissionEnum::EDIT_CUSTOMER_GROUP,
            PermissionEnum::EDIT_CATEGORY,
            PermissionEnum::DELETE_CATEGORY,
            PermissionEnum::VIEW_ORDER,
            PermissionEnum::EDIT_ORDER,
            PermissionEnum::DELETE_ORDER,
            PermissionEnum::EDIT_ORDER_STATUS,
            PermissionEnum::EDIT_FORMS,
            PermissionEnum::VIEW_SEARCH_TERM,
            PermissionEnum::EDIT_SEARCH_TERM,
            PermissionEnum::VIEW_LOCATION,
            PermissionEnum::EDIT_LOCATION,
            PermissionEnum::DELETE_LOCATION,
            PermissionEnum::EDIT_NOTIFICATION_TEMPLATE,
            PermissionEnum::EDIT_PAYMENT_GATEWAY,
            PermissionEnum::EDIT_PAYMENT_METHOD,
            PermissionEnum::EDIT_SETTING,
            PermissionEnum::EDIT_SHIPPING,
            PermissionEnum::EDIT_STATIC_BLOCK,
            PermissionEnum::EDIT_TAG,
            PermissionEnum::EDIT_TAX,
            PermissionEnum::EDIT_BLOCKED_DOMAIN,
            PermissionEnum::EDIT_STUDIO,
            PermissionEnum::EDIT_ROOM,
            PermissionEnum::EDIT_CLASS_TYPE,
            PermissionEnum::EDIT_SERIES_TYPE,
            PermissionEnum::EDIT_SERIES,
            PermissionEnum::EDIT_CONTRACTS,
            PermissionEnum::EDIT_SCHEDULE_SETTINGS,
            PermissionEnum::EDIT_SCHEDULES,
            PermissionEnum::EDIT_SCHEDULE_BOOKINGS,
            PermissionEnum::EDIT_GIFT_CARD_TEMPLATES,
            PermissionEnum::EDIT_GIFT_CARDS,
            PermissionEnum::VIEW_CUSTOMER,
            PermissionEnum::EDIT_CUSTOMER,
        ];
    }

    /**
     * Get observer permissions
     *
     * @return array
     */
    protected function getObserverPermissions(): array
    {
        return [
            PermissionEnum::VIEW_BRANCH,
            PermissionEnum::VIEW_WALLET_GROUP,
            PermissionEnum::VIEW_WALLET_RULE,
            PermissionEnum::VIEW_WALLET,
            PermissionEnum::VIEW_WALLET_TRANSACTION,
        ];
    }

    /**
     * Get treasury permissions
     *
     * @return array
     */
    protected function getTreasuryPermissions(): array
    {
        return [
            PermissionEnum::VIEW_DASHBOARD,
            PermissionEnum::VIEW_BRANCH,
            PermissionEnum::VIEW_WALLET_GROUP,
            PermissionEnum::VIEW_WALLET_RULE,
            PermissionEnum::VIEW_WALLET,
            PermissionEnum::VIEW_WALLET_TRANSACTION,
            PermissionEnum::VIEW_DISCOUNT,
            PermissionEnum::VIEW_ORDER,
        ];
    }
}
