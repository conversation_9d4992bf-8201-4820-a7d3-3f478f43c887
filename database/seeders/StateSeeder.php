<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $states = [
            ['code' => 'JHR', 'name' => '<PERSON><PERSON>'],
            ['code' => 'KDH', 'name' => 'Kedah'],
            ['code' => 'KTN', 'name' => 'Kelantan'],
            ['code' => 'MLK', 'name' => 'Melaka'],
            ['code' => 'NSN', 'name' => 'Negeri Sembilan'],
            ['code' => 'PHG', 'name' => 'Pahang'],
            ['code' => 'PNG', 'name' => 'Penang'],
            ['code' => 'PRK', 'name' => 'Perak'],
            ['code' => 'PLS', 'name' => 'Perlis'],
            ['code' => 'SGR', 'name' => 'Selangor'],
            ['code' => 'TRG', 'name' => 'Terengganu'],
            ['code' => 'KUL', 'name' => 'Wilayah Persekutuan Kuala Lumpur'],
            ['code' => 'LBN', 'name' => 'Wilayah Persekutuan Labuan'],
            ['code' => 'PJY', 'name' => 'Wilayah Persekutuan Putrajaya'],
            ['code' => 'SBH', 'name' => 'Sabah'],
            ['code' => 'SWK', 'name' => 'Sarawak'],
        ];

        foreach ($states as $state) {
            DB::table('states')->updateOrInsert($state);
        }

        $this->command->info('States seeded successfully!');
    }
}
