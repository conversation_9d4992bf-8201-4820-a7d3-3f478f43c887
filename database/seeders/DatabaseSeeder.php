<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('🌱 Starting database seeding...');

        $this->call([
            RolePermissionSeeder::class,
            NotificationTemplateSeeder::class,
            OrderStatusSeeder::class,
            StateSeeder::class,
        ]);

        $this->command->info('🎉 Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('📝 Summary:');
        $this->command->info('   - Permissions created from PermissionTableSeeder');
        $this->command->info('   - Roles and permissions assigned');
        $this->command->info('   - Mock super admin user created (<EMAIL> / password123)');
        $this->command->warn('   ⚠️  Remember to change the admin password in production!');
    }
}
