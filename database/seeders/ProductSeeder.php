<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ClassType;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\TaxClass;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('🛍️ Seeding Products...');

        // Clear existing products if needed
        if ($this->command->confirm('Clear existing products before seeding?', false)) {
            $this->clearExistingProducts();
        }

        $productCount = $this->command->ask('How many products to create?', 50);
        $withVariants = $this->command->confirm('Create product variants?', true);
        $withCategories = $this->command->confirm('Associate with categories?', true);
        $withImages = $this->command->confirm('Add product images?', false);

        DB::beginTransaction();

        try {
            $categories = $withCategories ? Category::limit(10)->get() : collect();
            $taxClasses = TaxClass::limit(5)->get();
            $classTypes = ClassType::limit(3)->get();

            // Create progress bar
            $bar = $this->command->getOutput()->createProgressBar($productCount);
            $bar->start();

            for ($i = 0; $i < $productCount; $i++) {
                $product = $this->createProduct($taxClasses, $classTypes);

                if ($withVariants) {
                    $this->createProductVariants($product);
                }

                if ($withCategories && $categories->isNotEmpty()) {
                    $this->attachCategories($product, $categories);
                }

                if ($withImages) {
                    $this->attachMedia($product);
                }

                $bar->advance();
            }

            DB::commit();
            $bar->finish();

            $this->command->newLine(2);
            $this->command->info("✅ Successfully created {$productCount} products!");

            if ($withVariants) {
                $variantCount = ProductVariant::count();
                $this->command->info("   📦 Created {$variantCount} product variants");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error("❌ Error occurred: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Clear existing products
     */
    private function clearExistingProducts(): void
    {
        $this->command->info('🗑️ Clearing existing products...');

        DB::table('category_product')->truncate();
        ProductVariant::truncate();
        Product::truncate();

        $this->command->info('✅ Existing products cleared.');
    }

    /**
     * Create a single product with random states
     */
    private function createProduct($taxClasses, $classTypes): Product
    {
        $factory = Product::factory();

        // Apply random states
        $states = ['published', 'draft', 'inactive'];
        $factory = $factory->{$states[array_rand($states)]}();

        // 30% chance for sale
        if (rand(1, 100) <= 30) {
            $factory = $factory->onSale();
        }

        // 70% chance for taxable
        if (rand(1, 100) <= 70) {
            $factory = $factory->taxable();

            if ($taxClasses->isNotEmpty()) {
                $factory = $factory->state([
                    'tax_class_id' => $taxClasses->random()->id
                ]);
            }
        }

        // 40% chance for meta data
        if (rand(1, 100) <= 40) {
            $factory = $factory->withMeta();
        }

        // 20% chance for ClassType productable
        if ($classTypes->isNotEmpty() && rand(1, 100) <= 20) {
            $classType = $classTypes->random();
            $factory = $factory->state([
                'productable_id' => $classType->id,
                'productable_type' => ClassType::class,
            ]);
        }

        return $factory->create();
    }

    /**
     * Create variants for a product
     */
    private function createProductVariants(Product $product): void
    {
        $variantCount = rand(1, 3);

        ProductVariant::factory()
            ->count($variantCount)
            ->create(['product_id' => $product->id]);
    }

    /**
     * Attach categories to a product
     */
    private function attachCategories(Product $product, $categories): void
    {
        $categoryCount = rand(1, 3);
        $selectedCategories = $categories->random(min($categoryCount, $categories->count()));

        $product->categories()->attach($selectedCategories->pluck('id'));
    }

    /**
     * Attach media to a product
     */
    private function attachMedia(Product $product): void
    {
        try {
            // Add main product image
            $product->addMediaFromUrl('https://picsum.photos/400/400?random=' . $product->id)
                ->toMediaCollection('image');

            // Add gallery images
            $galleryCount = rand(1, 3);
            for ($i = 0; $i < $galleryCount; $i++) {
                $product->addMediaFromUrl('https://picsum.photos/400/400?random=' . ($product->id + $i + 100))
                    ->toMediaCollection('gallery');
            }
        } catch (\Exception $e) {
            // Silently fail for media issues
        }
    }
}
