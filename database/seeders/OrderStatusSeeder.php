<?php

namespace Database\Seeders;

use App\Models\OrderStatus;
use Illuminate\Database\Seeder;


class OrderStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $order_statuses = [
            ['name' => 'Pending For Payment Method Approval', 'slug' => 'pending', 'serial' => 1, 'color' => '#23b848', 'default' => true],
            ['name' => 'Order Confirmed Pending For Packing', 'slug' => 'processing', 'serial' => 2, 'color' => '#23b848', 'default' => true],
            ['name' => 'Order Packed Pending For Delivery', 'slug' => 'packed', 'serial' => 3, 'color' => '#23b848', 'default' => true],
            ['name' => 'In Delivery Process', 'slug' => 'delivery', 'serial' => 4, 'color' => '#23b848', 'default' => true],
            ['name' => 'Complete', 'slug' => 'complete', 'serial' => 5, 'color' => '#23b848', 'default' => true],
            ['name' => 'On Hold For Payment Checking', 'slug' => 'holded', 'serial' => 1, 'color' => '#23b848', 'default' => false],
            ['name' => 'Payment Review', 'slug' => 'payment_review', 'serial' => 1, 'color' => '#23b848', 'default' => false],
            ['name' => 'Pending Payment', 'slug' => 'pending_payment', 'serial' => 1, 'color' => '#23b848', 'default' => false],
            ['name' => 'Suspected Fraud', 'slug' => 'fraud', 'serial' => 2, 'color' => '#d87b64', 'default' => false],
            ['name' => 'Canceled', 'slug' => 'canceled', 'serial' => 3, 'color' => '#d87b64', 'default' => false],
            ['name' => 'Closed', 'slug' => 'closed', 'serial' => 3, 'color' => '#d87b64', 'default' => false],
            ['name' => 'In Refund Process (B)', 'slug' => 'in_refund_process_b', 'serial' => 3, 'color' => '#23b848', 'default' => false],
            ['name' => 'In Refund Process (SC)', 'slug' => 'in_refund_process_sc', 'serial' => 3, 'color' => '#23b848', 'default' => false],
            ['name' => 'Arrive At Dropzone', 'slug' => 'arrive_at_dropzone', 'serial' => 4, 'color' => '#23b848', 'default' => false],
            ['name' => 'Failed Delivery', 'slug' => 'failed_delivery', 'serial' => 4, 'color' => '#23b848', 'default' => false],
        ];
        foreach ($order_statuses as $_order_status) {
            OrderStatus::firstOrCreate($_order_status);
        }

        $this->command->info('Order statuses seeded successfully!');
    }
}
