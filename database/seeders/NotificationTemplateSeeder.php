<?php

namespace Database\Seeders;

use App\Enums\NotificationChannel;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class NotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $now = Carbon::now();
        $templates = [
            [
                'name' => 'welcome_notification',
                'channel' => NotificationChannel::MAIL,
                'locale' => 'en',
                'subject' => 'Welcome, {{user_name}}!',
                'content' => 'Hello {{user_name}},<br><br>Welcome to our platform!<br><br>Your contact details:<br>Phone: {{phone_number}}<br>Email: {{email}}',
                'replacements' => json_encode(['user_name', 'phone_number', 'email']),
            ],
            [
                'name' => 'welcome_notification',
                'channel' => NotificationChannel::SMS,
                'locale' => 'en',
                'subject' => null,
                'content' => 'Hi {{user_name}}, welcome to our platform! Contact: {{phone_number}}',
                'replacements' => json_encode(['user_name', 'phone_number', 'email']),
            ],
            [
                'name' => 'welcome_notification',
                'channel' => NotificationChannel::WHATSAPP,
                'locale' => 'en',
                'subject' => null,
                'content' => '👋 Hello {{user_name}}! Welcome to our platform! Your email: {{email}}',
                'replacements' => json_encode(['user_name', 'phone_number', 'email']),
            ],
        ];

        foreach ($templates as $template) {
            $exists = DB::table('notification_templates')
                ->where('name', $template['name'])
                ->where('channel', $template['channel'])
                ->where('locale', $template['locale'])
                ->exists();

            if (!$exists) {
                DB::table('notification_templates')->insert(array_merge($template, [
                    'is_active' => true,
                    'created_at' => $now,
                    'updated_at' => $now,
                ]));
            }
        }
    }
}
