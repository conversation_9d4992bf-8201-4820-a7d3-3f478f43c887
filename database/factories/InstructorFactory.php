<?php

namespace Database\Factories;

use App\Models\Designation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class InstructorFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->name,
            'bio' => fake()->text(50),
            'user_id' => User::factory(),
            'designation_id' => Designation::factory(),
            'priority' => rand(1, 10),
            'order_column' => rand(1, 10),
            'active' => true,
        ];
    }
}
