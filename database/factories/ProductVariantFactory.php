<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductVariantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ProductVariant::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'product_id' => Product::factory(),
            'title' => $this->faker->words(2, true),
            'price' => $this->faker->randomFloat(2, 10, 500),
            'sale_price' => null,
            'is_active' => true,
            'sku' => $this->faker->unique()->bothify('VAR-####-????'),
            'barcode' => $this->faker->ean13(),
            'width' => $this->faker->randomFloat(1, 1, 50),
            'height' => $this->faker->randomFloat(1, 1, 50),
            'length' => $this->faker->randomFloat(1, 1, 50),
            'weight' => $this->faker->randomFloat(3, 0.1, 10),
            'options' => null,
        ];
    }

    /**
     * Indicate that the variant should be active.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * Indicate that the variant should be inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Indicate that the variant should have a sale price.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function onSale()
    {
        return $this->state(function (array $attributes) {
            $price = $attributes['price'] ?? 100;
            return [
                'sale_price' => $price * 0.8, // 20% discount
            ];
        });
    }

    /**
     * Indicate that the variant should have options.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withOptions()
    {
        return $this->state(function (array $attributes) {
            return [
                'options' => [
                    'size' => $this->faker->randomElement(['S', 'M', 'L', 'XL']),
                    'color' => $this->faker->colorName(),
                ],
            ];
        });
    }
}
