<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\BranchDetails;
use Illuminate\Database\Eloquent\Factories\Factory;

class BranchFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Branch::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->company(),
            'code' => strtoupper(fake()->unique()->lexify('??###')),
            'slug' => fake()->unique()->slug(),
            'geolocation' => [
                'lat' => fake()->latitude(),
                'lng' => fake()->longitude(),
                'address' => fake()->address(),
            ],
            'active' => true,
            'virtual' => fake()->boolean(30), // 30% chance of being virtual
        ];
    }

    /**
     * Indicate that the branch should be active.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => true,
            ];
        });
    }

    /**
     * Indicate that the branch should be inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'active' => false,
            ];
        });
    }

    /**
     * Indicate that the branch should be virtual.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function virtual()
    {
        return $this->state(function (array $attributes) {
            return [
                'virtual' => true,
            ];
        });
    }

    /**
     * Indicate that the branch should be physical.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function physical()
    {
        return $this->state(function (array $attributes) {
            return [
                'virtual' => false,
            ];
        });
    }

    /**
     * Indicate that the branch should have details.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withDetails()
    {
        return $this->has(BranchDetails::factory(), 'details');
    }
}
