<?php

namespace Database\Factories;

use App\Enums\SettingType;
use Illuminate\Database\Eloquent\Factories\Factory;

class SettingFactory extends Factory
{
    public function definition(): array
    {
        return [
            'type' => SettingType::GENERAL,
            'options' => [
                'seo' => [
                    'meta_title' => fake()->sentence(),
                    'meta_description' => fake()->sentence(),
                ],
            ],
        ];
    }
}
