<?php

namespace Database\Factories;

use App\Enums\ExportHistoryModel;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ExportHistory>
 */
class ExportHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'model' => ExportHistoryModel::ORDER,
            'filename' => fake()->name . '.xlsx',
            'path' => '/printing',
            'url' => fake()->url,
            'from_date' => now()->subMonth(),
            'to_date' => now(),
        ];
    }
}
