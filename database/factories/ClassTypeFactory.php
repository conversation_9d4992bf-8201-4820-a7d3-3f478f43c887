<?php

namespace Database\Factories;

use App\Enums\ClassTypePriceType;
use App\Models\ClassType;
use Illuminate\Database\Eloquent\Factories\Factory;


/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ClassType>
 */
class ClassTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ClassType::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(2, true);

        $priceTypes = [ClassTypePriceType::FIXED, ClassTypePriceType::FREE];
        $colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];

        return [
            'name' => $name,
            'description' => $this->faker->optional()->paragraph(3),
            'duration_in_minutes' => $this->faker->randomElement([30, 45, 60, 75, 90, 120]),
            'class_count' => $this->faker->numberBetween(1, 12),
            'price_type' => $this->faker->randomElement($priceTypes),
            'price' => $this->faker->optional(0.9)->randomFloat(2, 10, 200),
            'tax_class_id' => null,
            'colour' => $this->faker->randomElement($colors),
            'is_addon' => $this->faker->boolean(20), // 20% chance of being addon
            'is_bookable' => $this->faker->boolean(90),
            'is_active' => $this->faker->boolean(95),
        ];
    }
}
