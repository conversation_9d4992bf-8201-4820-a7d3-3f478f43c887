<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(),
            'price' => fake()->randomFloat(2, 10, 1000),
            'sale_price' => null,
            'sku' => fake()->unique()->bothify('SKU-####-????'),
            'barcode' => fake()->ean13(),
            'is_taxable' => fake()->boolean(70), // 70% chance of being taxable
            'tax_class_id' => null,
            'shipping_class_id' => null,
            'meta' => null,
            'status' => fake()->randomElement(['publish', 'draft', 'inactive']),
            'unit' => fake()->randomElement(['piece', 'kg', 'liter', 'meter']),
            'height' => fake()->randomFloat(1, 1, 100),
            'width' => fake()->randomFloat(1, 1, 100),
            'length' => fake()->randomFloat(1, 1, 100),
            'weight' => fake()->randomFloat(3, 0.1, 50),
        ];
    }
}
