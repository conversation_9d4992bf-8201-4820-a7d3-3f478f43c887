<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserProfile;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'firstname' => fake()->firstName,
            'lastname' => fake()->lastName,
            'email' => fake()->unique()->safeEmail,
            // +60123456789
            'phone' => fake()->e164PhoneNumber(),
            'country_code' => '+1',
            'gender' => fake()->randomElement(['male', 'female', 'other']),
            'dob' => fake()->date(),
            'email_verified_at' => now(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'remember_token' => Str::random(10),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the user should have a profile.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withProfile()
    {
        return $this->has(UserProfile::factory(), 'profile');
    }
}
