<?php

namespace Database\Factories;

use App\Enums\ProductType;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(2, true);
        
        return [
            'name' => ucwords($name),
            'slug' => \Illuminate\Support\Str::slug($name),
            'parent_id' => null,
            'type' => ProductType::DEFAULT,
            'is_active' => true,
            'meta' => [
                'description' => $this->faker->sentence(),
                'keywords' => $this->faker->words(3),
            ],
            'order_column' => $this->faker->numberBetween(1, 100),
            '_lft' => 1,
            '_rgt' => 2,
        ];
    }

    /**
     * Create a root category (no parent).
     */
    public function root(): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => null,
            '_lft' => 1,
            '_rgt' => 2,
        ]);
    }

    /**
     * Create a child category with a specific parent.
     */
    public function child(Category $parent): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parent->id,
            '_lft' => $parent->_rgt,
            '_rgt' => $parent->_rgt + 1,
        ]);
    }

    /**
     * Create an inactive category.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a category with specific type.
     */
    public function ofType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => $type,
        ]);
    }

    /**
     * Create a category with minimal meta data.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'meta' => null,
            'order_column' => null,
        ]);
    }

    /**
     * Create a category with rich meta data.
     */
    public function withRichMeta(): static
    {
        return $this->state(fn (array $attributes) => [
            'meta' => [
                'description' => $this->faker->paragraph(),
                'keywords' => $this->faker->words(5),
                'featured' => $this->faker->boolean(),
                'image' => $this->faker->imageUrl(640, 480, 'business'),
                'color' => $this->faker->hexColor(),
                'icon' => $this->faker->word(),
                'seo_title' => $this->faker->sentence(),
                'seo_description' => $this->faker->paragraph(),
            ],
        ]);
    }

    /**
     * Create electronics category.
     */
    public function electronics(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Electronics',
            'slug' => 'electronics',
            'meta' => [
                'description' => 'Electronic devices and gadgets',
                'keywords' => ['electronics', 'gadgets', 'technology'],
                'featured' => true,
            ],
        ]);
    }

    /**
     * Create fashion category.
     */
    public function fashion(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Fashion & Clothing',
            'slug' => 'fashion-clothing',
            'meta' => [
                'description' => 'Clothing, accessories and fashion items',
                'keywords' => ['fashion', 'clothing', 'apparel'],
                'seasonal' => true,
            ],
        ]);
    }

    /**
     * Create books category.
     */
    public function books(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Books & Literature',
            'slug' => 'books-literature',
            'meta' => [
                'description' => 'Books, ebooks and literature',
                'keywords' => ['books', 'literature', 'reading'],
                'educational' => true,
            ],
        ]);
    }
}
