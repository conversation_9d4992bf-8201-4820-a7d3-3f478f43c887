<?php

namespace Database\Factories;

use App\Models\TaxClass;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TaxClass>
 */
class TaxClassFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = TaxClass::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->randomElement([
                'Standard Tax',
                'Reduced Tax',
                'Zero Tax',
                'Luxury Tax',
                'Digital Services Tax',
                'Import Tax',
                'Local Tax'
            ]),
            'rate' => $this->faker->randomFloat(2, 0, 25), // 0% to 25%
            'country' => $this->faker->optional(0.7)->countryCode(),
            'state' => $this->faker->optional(0.5)->stateAbbr(),
            'zip' => $this->faker->optional(0.3)->postcode(),
            'city' => $this->faker->optional(0.4)->city(),
            'is_global' => $this->faker->boolean(30), // 30% chance of being global
            'priority' => $this->faker->numberBetween(0, 10),
            'on_shipping' => $this->faker->boolean(80), // 80% chance of applying to shipping
        ];
    }

    /**
     * Indicate that the tax class is global.
     */
    public function global(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_global' => true,
            'country' => null,
            'state' => null,
            'zip' => null,
            'city' => null,
        ]);
    }

    /**
     * Indicate that the tax class is for a specific country.
     */
    public function forCountry(string $country): static
    {
        return $this->state(fn (array $attributes) => [
            'is_global' => false,
            'country' => $country,
        ]);
    }

    /**
     * Indicate that the tax class doesn't apply to shipping.
     */
    public function noShipping(): static
    {
        return $this->state(fn (array $attributes) => [
            'on_shipping' => false,
        ]);
    }

    /**
     * Create a standard tax rate.
     */
    public function standard(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Standard Tax',
            'rate' => 10.00,
            'is_global' => true,
            'priority' => 1,
            'on_shipping' => true,
        ]);
    }

    /**
     * Create a zero tax rate.
     */
    public function zero(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Zero Tax',
            'rate' => 0.00,
            'is_global' => true,
            'priority' => 0,
            'on_shipping' => false,
        ]);
    }
}
