<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\BranchDetails;
use Illuminate\Database\Eloquent\Factories\Factory;

class BranchDetailsFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = BranchDetails::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'branch_id' => Branch::factory(),
            'company_name' => fake()->company() . ' Ltd',
            'company_registration_no' => fake()->numerify('##########'),
            'company_address' => fake()->address(),
            'company_phone' => fake()->phoneNumber(),
            'company_email' => fake()->companyEmail(),
        ];
    }
}
