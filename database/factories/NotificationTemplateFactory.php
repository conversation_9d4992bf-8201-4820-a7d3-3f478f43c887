<?php

namespace Database\Factories;

use App\Models\NotificationTemplate;
use App\Enums\NotificationChannel;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationTemplateFactory extends Factory
{
    protected $model = NotificationTemplate::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->slug(2),
            'description' => $this->faker->sentence(),
            'channel' => $this->faker->randomElement(NotificationChannel::getValues()),
            'locale' => $this->faker->randomElement(['en', 'es', 'fr']),
            'subject' => $this->faker->sentence(),
            'content' => $this->faker->paragraph(),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }

    public function active(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function email(): static
    {
        return $this->state(fn(array $attributes) => [
            'channel' => NotificationChannel::MAIL,
            'subject' => $this->faker->sentence(),
        ]);
    }

    public function sms(): static
    {
        return $this->state(fn(array $attributes) => [
            'channel' => NotificationChannel::SMS,
            'subject' => null,
        ]);
    }

    public function whatsapp(): static
    {
        return $this->state(fn(array $attributes) => [
            'channel' => NotificationChannel::WHATSAPP,
            'subject' => null,
        ]);
    }

    public function withName(string $name): static
    {
        return $this->state(fn(array $attributes) => [
            'name' => $name,
        ]);
    }

    public function withLocale(string $locale): static
    {
        return $this->state(fn(array $attributes) => [
            'locale' => $locale,
        ]);
    }
}
