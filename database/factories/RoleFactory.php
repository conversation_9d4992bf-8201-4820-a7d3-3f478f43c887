<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Role>
 */
class RoleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Role::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->slug(2),
            'guard_name' => 'api',
        ];
    }

    /**
     * Indicate that the role should use web guard.
     *
     * @return static
     */
    public function webGuard(): static
    {
        return $this->state(fn(array $attributes) => [
            'guard_name' => 'web',
        ]);
    }

    /**
     * Indicate that the role should use api guard.
     *
     * @return static
     */
    public function apiGuard(): static
    {
        return $this->state(fn(array $attributes) => [
            'guard_name' => 'api',
        ]);
    }
}
