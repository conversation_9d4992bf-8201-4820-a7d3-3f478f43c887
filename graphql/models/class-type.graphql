# ClassType GraphQL Schema
# This file defines the GraphQL schema for class-type-related queries
# Following the GraphQL-first approach: GraphQL for data retrieval, REST for mutations

extend type Query {
  classType(id: ID! @eq): ClassType @find(model: "App\\Models\\ClassType")

  classTypes(
    where: _
      @whereConditions(
        columns: [
          "name"
          "price_type"
          "is_addon"
          "is_bookable"
          "is_active"
          "duration_in_minutes"
          "class_count"
          "tax_class_id"
        ]
      )
    orderBy: _
      @orderBy(
        columns: [
          "id"
          "name"
          "price"
          "duration_in_minutes"
          "created_at"
          "updated_at"
        ]
      )
  ): [ClassType!]! @paginate(defaultCount: 15, maxCount: 100)
}

type ClassType {
  id: ID!
  name: String!
  description: String
  duration_in_minutes: Int!
  class_count: Int!
  price_type: String!
  price: Float
  tax_class_id: ID
  colour: String!
  is_addon: Boolean!
  is_bookable: Boolean!
  is_active: Boolean!
  created_at: DateTime
  updated_at: DateTime
  deleted_at: DateTime

  # Relationships
  taxClass: TaxClass @belongsTo(relation: "taxClass")
  product: Product @hasOne(relation: "product")
  categories: [Category!]! @method(name: "getCategories")
}
