# User GraphQL Schema
# This file defines the GraphQL schema for user-related queries
# Following the GraphQL-first approach: GraphQL for data retrieval, REST for mutations

extend type Query {
  me: User
    @guard(with: ["sanctum"])
    @field(resolver: "App\\GraphQL\\Queries\\UserQuery@me")

  user(id: ID! @eq): User
    @hasPermission(permission: "view-user")
    @find(model: "App\\Models\\User")

  users(
    id: ID @eq
    where: _
      @whereConditions(columns: ["firstname", "lastname", "email", "phone"])
    orderBy: _ @orderBy(columns: ["id", "created_at", "updated_at"])
  ): [User!]!
    @hasPermission(permission: "view-user")
    @paginate(defaultCount: 15, maxCount: 100)
}

type User {
  id: ID!
  is_guest: Boolean!
  ref_id: String
  customer_group_id: ID
  commission_group_id: ID
  firstname: String!
  lastname: String!
  name: String!
  email: String
  email_verified_at: DateTime
  phone: String
  country_code: String
  phone_verified_at: DateTime
  gender: String
  dob: DateTime
  dob_updated_at: DateTime
  is_active: Boolean!
  is_book_blocked: Boolean!
  is_new: Boolean!
  shop_id: ID
  created_at: DateTime
  updated_at: DateTime
  deleted_at: DateTime

  # Relationships
  profile: UserProfile @hasOne(relation: "profile")
  permissions: [Permission!]! @morphMany
  roles: [Role!]! @morphMany
}
