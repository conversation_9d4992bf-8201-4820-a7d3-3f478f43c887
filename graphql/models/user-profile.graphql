# UserProfile GraphQL Schema
# This file defines the GraphQL schema for user profile-related queries

type UserProfile {
  id: ID!
  avatar: Media @morphOne
  bio: String
  socials: String
  race: String
  contact: String
  emergency_contact: String
  emergency_phone: String
  street_address: String
  city: String
  zip: String
  state_id: ID
  studio_id: ID
  branch_id: ID
  customer_id: ID!
  created_at: DateTime
  updated_at: DateTime

  # Relationships
  user: User @belongsTo(relation: "user")
}
