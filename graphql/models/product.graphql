extend type Query {
  product(id: ID! @eq): Product
    @hasPermission(permission: "view-product")
    @find(model: "App\\Models\\Product")

  products(
    where: _
      @whereConditions(
        columns: [
          "id"
          "name"
          "slug"
          "description"
          "price"
          "sale_price"
          "sku"
          "barcode"
          "status"
          "unit"
          "height"
          "width"
          "length"
          "weight"
          "is_taxable"
          "tax_class_id"
          "is_bundle"
          "is_require_double_scanning"
          "created_at"
          "updated_at"
        ]
      )
    orderBy: _
      @orderBy(
        columns: [
          "id"
          "name"
          "slug"
          "description"
          "price"
          "sale_price"
          "sku"
          "barcode"
          "status"
          "unit"
          "height"
          "width"
          "length"
          "weight"
          "is_taxable"
          "tax_class_id"
          "is_bundle"
          "is_require_double_scanning"
          "created_at"
          "updated_at"
        ]
      )
  ): [Product!]!
    @hasPermission(permission: "view-product")
    @paginate(defaultCount: 15, maxCount: 100)
}

type Product {
  id: ID!
  name: String!
  slug: String!
  description: String
  price: Float
  sale_price: Float
  sku: String
  barcode: String
  is_taxable: Boolean
  tax_class_id: ID
  shipping_class_id: ID
  variant_attributes: JSON
  meta: JSON
  status: String
  unit: String
  height: Float
  width: Float
  length: Float
  weight: Float
  is_bundle: Boolean
  is_require_double_scanning: Boolean
  image: Media @morphOne
  gallery: [Media!] @morphMany
  created_at: DateTime
  updated_at: DateTime

  variants: [ProductVariant!]! @hasMany
  categories: [Category!]! @belongsToMany
  warehouses: [Warehouse!]!
    @belongsToMany(relation: "warehouses", pivot: ["label"])

  # Bundle relationships
  bundle_items: [ProductBundleItem!]! @hasMany(relation: "bundleItems")

  # Polymorphic relationship to the source model (ClassType, Service, etc.)
  productable: Productable @morphTo
}

type ProductVariant {
  id: ID!
  product_id: ID!
  title: String!
  price: Float
  sale_price: Float
  sku: String
  barcode: String
  is_active: Boolean
  width: Float
  height: Float
  length: Float
  weight: Float
  options: JSON
  gallery: [Media!] @morphMany
  created_at: DateTime
  updated_at: DateTime
}

type ProductBundleItem {
  id: ID!
  bundle_product_id: ID!
  item_product_id: ID!
  quantity: Int!
  created_at: DateTime
  updated_at: DateTime

  # Relationships
  bundle: Product
    @belongsTo(relation: "bundle", foreignKey: "bundle_product_id")
  item: Product @belongsTo(relation: "item", foreignKey: "item_product_id")
}

type ProductWarehousePivot {
  id: ID!
  label: String
}

extend type Warehouse {
  pivot: ProductWarehousePivot
}

# Union type for polymorphic relationships
union Productable = ClassType
