# Role GraphQL Schema
# This file defines the GraphQL schema for role-related queries
# Following the GraphQL-first approach: GraphQL for data retrieval, REST for mutations

extend type Query {
  role(id: ID! @eq): Role
    @hasPermission(permission: "view-role-permission")
    @find(model: "App\\Models\\Role")

  roles(
    where: _ @whereConditions(columns: ["name", "guard_name"])
    orderBy: _ @orderBy(columns: ["id", "name", "created_at", "updated_at"])
  ): [Role!]!
    @hasPermission(permission: "view-role-permission")
    @paginate(defaultCount: 15, maxCount: 100, model: "App\\Models\\Role")
}

type Permission {
  id: ID!
  name: String!
  guard_name: String!
  created_at: DateTime
  updated_at: DateTime
}

type Role {
  id: ID!
  name: String!
  guard_name: String!
  created_at: DateTime
  updated_at: DateTime

  # Relationships
  permissions: [Permission!]! @morphMany
}
