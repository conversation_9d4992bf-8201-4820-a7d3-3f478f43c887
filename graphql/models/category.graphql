# Category GraphQL Schema
# This file defines the GraphQL schema for category-related queries

extend type Query {
  category(id: ID! @eq): Category @find(model: "App\\Models\\Category")

  categories(
    where: _
      @whereConditions(
        columns: [
          "name"
          "slug"
          "type"
          "is_active"
          "order_column"
          "parent_id"
        ]
      )
    whereHas: _
      @whereHasConditions(relation: "parentCategory", columns: ["name", "slug"])
    orderBy: _
      @orderBy(
        columns: [
          "id"
          "name"
          "slug"
          "order_column"
          "created_at"
          "updated_at"
        ]
      )
  ): [Category!]! @paginate(defaultCount: 15, maxCount: 100)
}

type Category {
  id: ID!
  name: String!
  slug: String!
  parent_id: ID
  type: String!
  is_active: Boolean!
  meta: JSON
  order_column: Int
  created_at: DateTime
  updated_at: DateTime
  deleted_at: DateTime
  image: Media @morphOne

  # Nested set fields
  _lft: Int
  _rgt: Int

  # Computed fields
  full_path: String
    @field(resolver: "App\\GraphQL\\Queries\\CategoryQuery@fullPath")
  has_children: Boolean
    @field(resolver: "App\\GraphQL\\Queries\\CategoryQuery@hasChildren")
  is_root: Boolean
    @field(resolver: "App\\GraphQL\\Queries\\CategoryQuery@isRoot")

  # Relationships
  parentCategory: Category @belongsTo(relation: "parentCategory")
  childCategories: [Category!]! @hasMany(relation: "childCategories")

  # Nested set relationships
  ancestors: [Category!]!
    @field(resolver: "App\\GraphQL\\Queries\\CategoryQuery@ancestors")
  descendants: [Category!]!
    @field(resolver: "App\\GraphQL\\Queries\\CategoryQuery@descendants")
  siblings: [Category!]!
    @field(resolver: "App\\GraphQL\\Queries\\CategoryQuery@siblings")
}
