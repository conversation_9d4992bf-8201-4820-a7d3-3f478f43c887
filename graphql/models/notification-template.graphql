# NotificationTemplate GraphQL Schema
# This file defines the GraphQL schema for notification template-related queries
# Following the GraphQL-first approach: GraphQL for data retrieval, REST for mutations

extend type Query {
  notificationTemplate(id: ID! @eq): NotificationTemplate
    @hasPermission(permission: "edit-notification-template")
    @find(model: "App\\Models\\NotificationTemplate")

  notificationTemplates(
    where: _
      @whereConditions(
        columns: [
          "name"
          "description"
          "channel"
          "locale"
          "subject"
          "content"
          "replacements"
          "is_active"
        ]
      )
    orderBy: _
      @orderBy(
        columns: ["id", "name", "channel", "locale", "created_at", "updated_at"]
      )
  ): [NotificationTemplate!]!
    @hasPermission(permission: "edit-notification-template")
    @paginate(defaultCount: 15, maxCount: 100)
}

type NotificationTemplate {
  id: ID!
  name: String!
  description: String
  channel: String!
  locale: String!
  subject: String
  content: String!
  replacements: [String!]
  is_active: Boolean!
  created_at: DateTime
  updated_at: DateTime
}
