extend type Query {
  branch(id: ID! @eq): Branch
    @hasPermission(permission: "view-branch")
    @find(model: "App\\Models\\Branch")

  branches(
    where: _
      @whereConditions(columns: ["name", "code", "slug", "active", "virtual"])
    orderBy: _
      @orderBy(columns: ["id", "name", "code", "created_at", "updated_at"])
  ): [Branch!]!
    @hasPermission(permission: "view-branch")
    @paginate(defaultCount: 15, maxCount: 100)
}

type Branch {
  id: ID!
  name: String!
  code: String
  slug: String!
  image: Media @morphOne
  geolocation: JSON
  active: Boolean!
  virtual: Boolean!
  created_at: DateTime
  updated_at: DateTime

  # Relationships
  details: BranchDetails @hasOne
  users: [User!]! @belongsToMany
}

type BranchDetails {
  id: ID!
  branch_id: ID!
  company_name: String
  company_registration_no: String
  company_address: String
  company_phone: String
  company_email: String
  created_at: DateTime
  updated_at: DateTime

  # Relationships
  branch: Branch @belongsTo
}
