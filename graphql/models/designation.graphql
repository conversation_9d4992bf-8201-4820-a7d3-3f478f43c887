extend type Query {
    designation(id: ID! @eq): Designation
    @hasPermission(permission: "view-designation")
    @find(model: "App\\Models\\Designation")

    designations(
        id: ID @eq
        where: _
        @whereConditions(columns: ["name"])
        orderBy: _ @orderBy(columns: ["id", "name", "created_at", "updated_at"])
    ): [Designation!]!
    @hasPermission(permission: "view-designation")
    @paginate(defaultCount: 15, maxCount: 100)
}

type Designation {
    id: ID!
    name: String!
    created_at: DateTime!
    updated_at: DateTime!

    # Relationships
    instructors: [Instructor] @hasMany
}

