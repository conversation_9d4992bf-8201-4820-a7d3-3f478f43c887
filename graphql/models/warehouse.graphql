# Warehouse GraphQL Schema
# This file defines the GraphQL schema for warehouse-related queries

extend type Query {
  warehouse(id: ID! @eq): Warehouse
    @hasPermission(permission: "view-warehouse")
    @find(model: "App\\Models\\Warehouse")

  warehouses(
    where: _ @whereConditions(columns: ["name", "location", "description"])
    orderBy: _ @orderBy(columns: ["id", "name", "created_at", "updated_at"])
  ): [Warehouse!]!
    @hasPermission(permission: "view-warehouse")
    @paginate(defaultCount: 15, maxCount: 100)
}

type Warehouse {
  id: ID!
  name: String!
  location: String!
  description: String
  created_at: DateTime
  updated_at: DateTime
  deleted_at: DateTime

  products: [Product!]! @belongsToMany
}

extend type Product {
  pivot: ProductWarehousePivot
}
