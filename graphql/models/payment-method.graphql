type PaymentMethod {
  id: ID!
  title: String!
  code: String!
  description: String
  image: String
  payment_info: JSON
  is_active: Boolean!
  is_visible: Boolean!
  payment_gateway_id: ID
  created_at: DateTime!
  updated_at: DateTime!

  # Relationships
  payment_gateway: PaymentGateway @belongsTo(relation: "paymentGateway")
}

type PaymentGateway {
  id: ID!
  name: String!
  slug: String!
  description: String
  image: String
  is_active: Boolean!
  created_at: DateTime!
  updated_at: DateTime!

  # Relationships
  payment_methods: [PaymentMethod!]! @hasMany(relation: "paymentMethods")
}
