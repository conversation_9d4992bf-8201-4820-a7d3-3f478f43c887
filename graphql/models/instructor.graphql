extend type Query {
  instructor(id: ID! @eq): Instructor
    @hasPermission(permission: "view-instructor")
    @find(model: "App\\Models\\Instructor")

  instructors(
    id: ID @eq
    where: _ @whereConditions(columns: ["name", "active"])
    orderBy: _ @orderBy(columns: ["id", "created_at", "updated_at"])
  ): [Instructor!]!
    @hasPermission(permission: "view-instructor")
    @paginate(defaultCount: 15, maxCount: 100)
}

type Instructor {
  id: ID!
  avatar: Media @morphOne
  name: String!
  bio: String
  priority: Int
  active: Boolean!
  order_column: Int
  created_at: DateTime!
  updated_at: DateTime!

  # Relationships
  user: User @belongsTo(relation: "user")
  designation: Designation @belongsTo(relation: "designation")
}
