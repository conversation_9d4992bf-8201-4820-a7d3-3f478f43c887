"A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`."
scalar DateTime
  @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\DateTime")

"A date string with format `Y-m-d`, e.g. `2011-05-23`."
scalar Date @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\Date")

"Indicates what fields are available at the top level of a query operation."
type Query {
  "Health check endpoint"
  health: String @field(resolver: "App\\GraphQL\\Queries\\HealthQuery@health")
}

"Root mutation type"
type Mutation {
  "Placeholder for mutations - actual mutations handled via REST API"
  _placeholder: String
    @field(resolver: "App\\GraphQL\\Queries\\HealthQuery@health")
}

#import common.graphql
#import models/*.graphql
